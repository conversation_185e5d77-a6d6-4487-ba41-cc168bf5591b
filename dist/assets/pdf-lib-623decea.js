import{g as Yu}from"./vendor-1c28ea83.js";/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var Hi=function(e,t){return Hi=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var i in n)n.hasOwnProperty(i)&&(r[i]=n[i])},Hi(e,t)};function E(e,t){Hi(e,t);function r(){this.constructor=e}e.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}var H=function(){return H=Object.assign||function(t){for(var r,n=1,i=arguments.length;n<i;n++){r=arguments[n];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(t[a]=r[a])}return t},H.apply(this,arguments)};function Ju(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)t.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r}function Y(e,t,r,n){function i(a){return a instanceof r?a:new r(function(o){o(a)})}return new(r||(r=Promise))(function(a,o){function s(c){try{u(n.next(c))}catch(l){o(l)}}function f(c){try{u(n.throw(c))}catch(l){o(l)}}function u(c){c.done?a(c.value):i(c.value).then(s,f)}u((n=n.apply(e,t||[])).next())})}function J(e,t){var r={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},n,i,a,o;return o={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function s(u){return function(c){return f([u,c])}}function f(u){if(n)throw new TypeError("Generator is already executing.");for(;r;)try{if(n=1,i&&(a=u[0]&2?i.return:u[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,u[1])).done)return a;switch(i=0,a&&(u=[u[0]&2,a.value]),u[0]){case 0:case 1:a=u;break;case 4:return r.label++,{value:u[1],done:!1};case 5:r.label++,i=u[1],u=[0];continue;case 7:u=r.ops.pop(),r.trys.pop();continue;default:if(a=r.trys,!(a=a.length>0&&a[a.length-1])&&(u[0]===6||u[0]===2)){r=0;continue}if(u[0]===3&&(!a||u[1]>a[0]&&u[1]<a[3])){r.label=u[1];break}if(u[0]===6&&r.label<a[1]){r.label=a[1],a=u;break}if(a&&r.label<a[2]){r.label=a[2],r.ops.push(u);break}a[2]&&r.ops.pop(),r.trys.pop();continue}u=t.call(e,r)}catch(c){u=[6,c],i=0}finally{n=a=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}function re(){for(var e=0,t=0,r=arguments.length;t<r;t++)e+=arguments[t].length;for(var n=Array(e),i=0,t=0;t<r;t++)for(var a=arguments[t],o=0,s=a.length;o<s;o++,i++)n[i]=a[o];return n}var or="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Rr=new Uint8Array(256);for(var bn=0;bn<or.length;bn++)Rr[or.charCodeAt(bn)]=bn;var Qu=function(e){for(var t="",r=e.length,n=0;n<r;n+=3)t+=or[e[n]>>2],t+=or[(e[n]&3)<<4|e[n+1]>>4],t+=or[(e[n+1]&15)<<2|e[n+2]>>6],t+=or[e[n+2]&63];return r%3===2?t=t.substring(0,t.length-1)+"=":r%3===1&&(t=t.substring(0,t.length-2)+"=="),t},Wa=function(e){var t=e.length*.75,r=e.length,n,i=0,a,o,s,f;e[e.length-1]==="="&&(t--,e[e.length-2]==="="&&t--);var u=new Uint8Array(t);for(n=0;n<r;n+=4)a=Rr[e.charCodeAt(n)],o=Rr[e.charCodeAt(n+1)],s=Rr[e.charCodeAt(n+2)],f=Rr[e.charCodeAt(n+3)],u[i++]=a<<2|o>>4,u[i++]=(o&15)<<4|s>>2,u[i++]=(s&3)<<6|f&63;return u},_u=/^(data)?:?([\w\/\+]+)?;?(charset=[\w-]+|base64)?.*,/i,$u=function(e){var t=e.trim(),r=t.substring(0,100),n=r.match(_u);if(!n)return Wa(t);var i=n[0],a=t.substring(i.length);return Wa(a)},G=function(e){return e.charCodeAt(0)},ef=function(e){return e.codePointAt(0)},nn=function(e,t){return et(e.toString(16),t,"0").toUpperCase()},oi=function(e){return nn(e,2)},vt=function(e){return String.fromCharCode(e)},tf=function(e){return vt(parseInt(e,16))},et=function(e,t,r){for(var n="",i=0,a=t-e.length;i<a;i++)n+=r;return n+e},ke=function(e,t,r){for(var n=e.length,i=0;i<n;i++)t[r++]=e.charCodeAt(i);return n},rf=function(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")},an=function(e){return e.replace(/\t|\u0085|\u2028|\u2029/g,"    ").replace(/[\b\v]/g,"")},nf=["\\n","\\f","\\r","\\u000B"],os=function(e){return/^[\n\f\r\u000B]$/.test(e)},ss=function(e){return e.split(/[\n\f\r\u000B]/)},us=function(e){return e.replace(/[\n\f\r\u000B]/g," ")},fs=function(e,t){var r=e.charCodeAt(t),n,i=t+1,a=1;return r>=55296&&r<=56319&&e.length>i&&(n=e.charCodeAt(i),n>=56320&&n<=57343&&(a=2)),[e.slice(t,t+a),a]},af=function(e){for(var t=[],r=0,n=e.length;r<n;){var i=fs(e,r),a=i[0],o=i[1];t.push(a),r+=o}return t},of=function(e){for(var t=nf.join("|"),r=["$"],n=0,i=e.length;n<i;n++){var a=e[n];if(os(a))throw new TypeError("`wordBreak` must not include "+t);r.push(a===""?".":rf(a))}var o=r.join("|");return new RegExp("("+t+")|((.*?)("+o+"))","gm")},sf=function(e,t,r,n){for(var i=of(t),a=an(e).match(i),o="",s=0,f=[],u=function(){o!==""&&f.push(o),o="",s=0},c=0,l=a.length;c<l;c++){var h=a[c];if(os(h))u();else{var d=n(h);s+d>r&&u(),o+=h,s+=d}}return u(),f},uf=/^D:(\d\d\d\d)(\d\d)?(\d\d)?(\d\d)?(\d\d)?(\d\d)?([+\-Z])?(\d\d)?'?(\d\d)?'?$/,cs=function(e){var t=e.match(uf);if(t){var r=t[1],n=t[2],i=n===void 0?"01":n,a=t[3],o=a===void 0?"01":a,s=t[4],f=s===void 0?"00":s,u=t[5],c=u===void 0?"00":u,l=t[6],h=l===void 0?"00":l,d=t[7],v=d===void 0?"Z":d,g=t[8],m=g===void 0?"00":g,b=t[9],F=b===void 0?"00":b,w=v==="Z"?"Z":""+v+m+":"+F,C=new Date(r+"-"+i+"-"+o+"T"+f+":"+c+":"+h+w);return C}},ha=function(e,t){for(var r,n=0,i;n<e.length;){var a=e.substring(n).match(t);if(!a)return{match:i,pos:n};i=a,n+=((r=a.index)!==null&&r!==void 0?r:0)+a[0].length}return{match:i,pos:n}},Gn=function(e){return e[e.length-1]},Xi=function(e){if(e instanceof Uint8Array)return e;for(var t=e.length,r=new Uint8Array(t),n=0;n<t;n++)r[n]=e.charCodeAt(n);return r},ff=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var r=e.length,n=[],i=0;i<r;i++){var a=e[i];n[i]=a instanceof Uint8Array?a:Xi(a)}for(var o=0,i=0;i<r;i++)o+=e[i].length;for(var s=new Uint8Array(o),f=0,u=0;u<r;u++)for(var c=n[u],l=0,h=c.length;l<h;l++)s[f++]=c[l];return s},cf=function(e){for(var t=0,r=0,n=e.length;r<n;r++)t+=e[r].length;for(var i=new Uint8Array(t),a=0,r=0,n=e.length;r<n;r++){var o=e[r];i.set(o,a),a+=o.length}return i},ls=function(e){for(var t="",r=0,n=e.length;r<n;r++)t+=vt(e[r]);return t},lf=function(e,t){return e.id-t.id},hf=function(e,t){for(var r=[],n=0,i=e.length;n<i;n++){var a=e[n],o=e[n-1];(n===0||t(a)!==t(o))&&r.push(a)}return r},er=function(e){for(var t=e.length,r=0,n=Math.floor(t/2);r<n;r++){var i=r,a=t-r-1,o=e[r];e[i]=e[a],e[a]=o}return e},df=function(e){for(var t=0,r=0,n=e.length;r<n;r++)t+=e[r];return t},vf=function(e,t){for(var r=new Array(t-e),n=0,i=r.length;n<i;n++)r[n]=e+n;return r},pf=function(e,t){for(var r=new Array(t.length),n=0,i=t.length;n<i;n++)r[n]=e[t[n]];return r},gf=function(e){return e instanceof Uint8Array||e instanceof ArrayBuffer||typeof e=="string"},Sr=function(e){if(typeof e=="string")return $u(e);if(e instanceof ArrayBuffer)return new Uint8Array(e);if(e instanceof Uint8Array)return e;throw new TypeError("`input` must be one of `string | ArrayBuffer | Uint8Array`")},dr=function(){return new Promise(function(e){setTimeout(function(){return e()},0)})},yf=function(e,t){t===void 0&&(t=!0);var r=[];t&&r.push(65279);for(var n=0,i=e.length;n<i;){var a=e.codePointAt(n);if(a<65536)r.push(a),n+=1;else if(a<1114112)r.push(hs(a),ds(a)),n+=2;else throw new Error("Invalid code point: 0x"+oi(a))}return new Uint16Array(r)},bf=function(e){return e>=0&&e<=65535},mf=function(e){return e>=65536&&e<=1114111},hs=function(e){return Math.floor((e-65536)/1024)+55296},ds=function(e){return(e-65536)%1024+56320},Rt;(function(e){e.BigEndian="BigEndian",e.LittleEndian="LittleEndian"})(Rt||(Rt={}));var Fr="�".codePointAt(0),vs=function(e,t){if(t===void 0&&(t=!0),e.length<=1)return String.fromCodePoint(Fr);for(var r=t?wf(e):Rt.BigEndian,n=t?2:0,i=[];e.length-n>=2;){var a=Ka(e[n++],e[n++],r);if(xf(a))if(e.length-n<2)i.push(Fr);else{var o=Ka(e[n++],e[n++],r);qa(o)?i.push(a,o):i.push(Fr)}else qa(a)?(n+=2,i.push(Fr)):i.push(a)}return n<e.length&&i.push(Fr),String.fromCodePoint.apply(String,i)},xf=function(e){return e>=55296&&e<=56319},qa=function(e){return e>=56320&&e<=57343},Ka=function(e,t,r){if(r===Rt.LittleEndian)return t<<8|e;if(r===Rt.BigEndian)return e<<8|t;throw new Error("Invalid byteOrder: "+r)},wf=function(e){return ps(e)?Rt.BigEndian:gs(e)?Rt.LittleEndian:Rt.BigEndian},ps=function(e){return e[0]===254&&e[1]===255},gs=function(e){return e[0]===255&&e[1]===254},ys=function(e){return ps(e)||gs(e)},Sf=function(e){var t=String(e);if(Math.abs(e)<1){var r=parseInt(e.toString().split("e-")[1]);if(r){var n=e<0;n&&(e*=-1),e*=Math.pow(10,r-1),t="0."+new Array(r).join("0")+e.toString().substring(2),n&&(t="-"+t)}}else{var r=parseInt(e.toString().split("+")[1]);r>20&&(r-=20,e/=Math.pow(10,r),t=e.toString()+new Array(r+1).join("0"))}return t},Nn=function(e){return Math.ceil(e.toString(2).length/8)},tr=function(e){for(var t=new Uint8Array(Nn(e)),r=1;r<=t.length;r++)t[r-1]=e>>(t.length-r)*8;return t},on=function(e){throw new Error(e)},yt={};(function(e){var t=typeof Uint8Array<"u"&&typeof Uint16Array<"u"&&typeof Int32Array<"u";function r(a,o){return Object.prototype.hasOwnProperty.call(a,o)}e.assign=function(a){for(var o=Array.prototype.slice.call(arguments,1);o.length;){var s=o.shift();if(s){if(typeof s!="object")throw new TypeError(s+"must be non-object");for(var f in s)r(s,f)&&(a[f]=s[f])}}return a},e.shrinkBuf=function(a,o){return a.length===o?a:a.subarray?a.subarray(0,o):(a.length=o,a)};var n={arraySet:function(a,o,s,f,u){if(o.subarray&&a.subarray){a.set(o.subarray(s,s+f),u);return}for(var c=0;c<f;c++)a[u+c]=o[s+c]},flattenChunks:function(a){var o,s,f,u,c,l;for(f=0,o=0,s=a.length;o<s;o++)f+=a[o].length;for(l=new Uint8Array(f),u=0,o=0,s=a.length;o<s;o++)c=a[o],l.set(c,u),u+=c.length;return l}},i={arraySet:function(a,o,s,f,u){for(var c=0;c<f;c++)a[u+c]=o[s+c]},flattenChunks:function(a){return[].concat.apply([],a)}};e.setTyped=function(a){a?(e.Buf8=Uint8Array,e.Buf16=Uint16Array,e.Buf32=Int32Array,e.assign(e,n)):(e.Buf8=Array,e.Buf16=Array,e.Buf32=Array,e.assign(e,i))},e.setTyped(t)})(yt);var sn={},ct={},pr={},Ff=yt,kf=4,La=0,Ga=1,Cf=2;function gr(e){for(var t=e.length;--t>=0;)e[t]=0}var Tf=0,bs=1,Pf=2,Af=3,Df=258,da=29,un=256,Xr=un+1+da,fr=30,va=19,ms=2*Xr+1,Vt=15,Ai=16,Rf=7,pa=256,xs=16,ws=17,Ss=18,Zi=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],zn=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],Of=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],Fs=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],Ef=512,dt=new Array((Xr+2)*2);gr(dt);var Mr=new Array(fr*2);gr(Mr);var Zr=new Array(Ef);gr(Zr);var Yr=new Array(Df-Af+1);gr(Yr);var ga=new Array(da);gr(ga);var Hn=new Array(fr);gr(Hn);function Di(e,t,r,n,i){this.static_tree=e,this.extra_bits=t,this.extra_base=r,this.elems=n,this.max_length=i,this.has_stree=e&&e.length}var ks,Cs,Ts;function Ri(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}function Ps(e){return e<256?Zr[e]:Zr[256+(e>>>7)]}function Jr(e,t){e.pending_buf[e.pending++]=t&255,e.pending_buf[e.pending++]=t>>>8&255}function Ne(e,t,r){e.bi_valid>Ai-r?(e.bi_buf|=t<<e.bi_valid&65535,Jr(e,e.bi_buf),e.bi_buf=t>>Ai-e.bi_valid,e.bi_valid+=r-Ai):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=r)}function st(e,t,r){Ne(e,r[t*2],r[t*2+1])}function As(e,t){var r=0;do r|=e&1,e>>>=1,r<<=1;while(--t>0);return r>>>1}function Bf(e){e.bi_valid===16?(Jr(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=e.bi_buf&255,e.bi_buf>>=8,e.bi_valid-=8)}function Nf(e,t){var r=t.dyn_tree,n=t.max_code,i=t.stat_desc.static_tree,a=t.stat_desc.has_stree,o=t.stat_desc.extra_bits,s=t.stat_desc.extra_base,f=t.stat_desc.max_length,u,c,l,h,d,v,g=0;for(h=0;h<=Vt;h++)e.bl_count[h]=0;for(r[e.heap[e.heap_max]*2+1]=0,u=e.heap_max+1;u<ms;u++)c=e.heap[u],h=r[r[c*2+1]*2+1]+1,h>f&&(h=f,g++),r[c*2+1]=h,!(c>n)&&(e.bl_count[h]++,d=0,c>=s&&(d=o[c-s]),v=r[c*2],e.opt_len+=v*(h+d),a&&(e.static_len+=v*(i[c*2+1]+d)));if(g!==0){do{for(h=f-1;e.bl_count[h]===0;)h--;e.bl_count[h]--,e.bl_count[h+1]+=2,e.bl_count[f]--,g-=2}while(g>0);for(h=f;h!==0;h--)for(c=e.bl_count[h];c!==0;)l=e.heap[--u],!(l>n)&&(r[l*2+1]!==h&&(e.opt_len+=(h-r[l*2+1])*r[l*2],r[l*2+1]=h),c--)}}function Ds(e,t,r){var n=new Array(Vt+1),i=0,a,o;for(a=1;a<=Vt;a++)n[a]=i=i+r[a-1]<<1;for(o=0;o<=t;o++){var s=e[o*2+1];s!==0&&(e[o*2]=As(n[s]++,s))}}function zf(){var e,t,r,n,i,a=new Array(Vt+1);for(r=0,n=0;n<da-1;n++)for(ga[n]=r,e=0;e<1<<Zi[n];e++)Yr[r++]=n;for(Yr[r-1]=n,i=0,n=0;n<16;n++)for(Hn[n]=i,e=0;e<1<<zn[n];e++)Zr[i++]=n;for(i>>=7;n<fr;n++)for(Hn[n]=i<<7,e=0;e<1<<zn[n]-7;e++)Zr[256+i++]=n;for(t=0;t<=Vt;t++)a[t]=0;for(e=0;e<=143;)dt[e*2+1]=8,e++,a[8]++;for(;e<=255;)dt[e*2+1]=9,e++,a[9]++;for(;e<=279;)dt[e*2+1]=7,e++,a[7]++;for(;e<=287;)dt[e*2+1]=8,e++,a[8]++;for(Ds(dt,Xr+1,a),e=0;e<fr;e++)Mr[e*2+1]=5,Mr[e*2]=As(e,5);ks=new Di(dt,Zi,un+1,Xr,Vt),Cs=new Di(Mr,zn,0,fr,Vt),Ts=new Di(new Array(0),Of,0,va,Rf)}function Rs(e){var t;for(t=0;t<Xr;t++)e.dyn_ltree[t*2]=0;for(t=0;t<fr;t++)e.dyn_dtree[t*2]=0;for(t=0;t<va;t++)e.bl_tree[t*2]=0;e.dyn_ltree[pa*2]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function Os(e){e.bi_valid>8?Jr(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0}function jf(e,t,r,n){Os(e),n&&(Jr(e,r),Jr(e,~r)),Ff.arraySet(e.pending_buf,e.window,t,r,e.pending),e.pending+=r}function Ha(e,t,r,n){var i=t*2,a=r*2;return e[i]<e[a]||e[i]===e[a]&&n[t]<=n[r]}function Oi(e,t,r){for(var n=e.heap[r],i=r<<1;i<=e.heap_len&&(i<e.heap_len&&Ha(t,e.heap[i+1],e.heap[i],e.depth)&&i++,!Ha(t,n,e.heap[i],e.depth));)e.heap[r]=e.heap[i],r=i,i<<=1;e.heap[r]=n}function Xa(e,t,r){var n,i,a=0,o,s;if(e.last_lit!==0)do n=e.pending_buf[e.d_buf+a*2]<<8|e.pending_buf[e.d_buf+a*2+1],i=e.pending_buf[e.l_buf+a],a++,n===0?st(e,i,t):(o=Yr[i],st(e,o+un+1,t),s=Zi[o],s!==0&&(i-=ga[o],Ne(e,i,s)),n--,o=Ps(n),st(e,o,r),s=zn[o],s!==0&&(n-=Hn[o],Ne(e,n,s)));while(a<e.last_lit);st(e,pa,t)}function Yi(e,t){var r=t.dyn_tree,n=t.stat_desc.static_tree,i=t.stat_desc.has_stree,a=t.stat_desc.elems,o,s,f=-1,u;for(e.heap_len=0,e.heap_max=ms,o=0;o<a;o++)r[o*2]!==0?(e.heap[++e.heap_len]=f=o,e.depth[o]=0):r[o*2+1]=0;for(;e.heap_len<2;)u=e.heap[++e.heap_len]=f<2?++f:0,r[u*2]=1,e.depth[u]=0,e.opt_len--,i&&(e.static_len-=n[u*2+1]);for(t.max_code=f,o=e.heap_len>>1;o>=1;o--)Oi(e,r,o);u=a;do o=e.heap[1],e.heap[1]=e.heap[e.heap_len--],Oi(e,r,1),s=e.heap[1],e.heap[--e.heap_max]=o,e.heap[--e.heap_max]=s,r[u*2]=r[o*2]+r[s*2],e.depth[u]=(e.depth[o]>=e.depth[s]?e.depth[o]:e.depth[s])+1,r[o*2+1]=r[s*2+1]=u,e.heap[1]=u++,Oi(e,r,1);while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],Nf(e,t),Ds(r,f,e.bl_count)}function Za(e,t,r){var n,i=-1,a,o=t[0*2+1],s=0,f=7,u=4;for(o===0&&(f=138,u=3),t[(r+1)*2+1]=65535,n=0;n<=r;n++)a=o,o=t[(n+1)*2+1],!(++s<f&&a===o)&&(s<u?e.bl_tree[a*2]+=s:a!==0?(a!==i&&e.bl_tree[a*2]++,e.bl_tree[xs*2]++):s<=10?e.bl_tree[ws*2]++:e.bl_tree[Ss*2]++,s=0,i=a,o===0?(f=138,u=3):a===o?(f=6,u=3):(f=7,u=4))}function Ya(e,t,r){var n,i=-1,a,o=t[0*2+1],s=0,f=7,u=4;for(o===0&&(f=138,u=3),n=0;n<=r;n++)if(a=o,o=t[(n+1)*2+1],!(++s<f&&a===o)){if(s<u)do st(e,a,e.bl_tree);while(--s!==0);else a!==0?(a!==i&&(st(e,a,e.bl_tree),s--),st(e,xs,e.bl_tree),Ne(e,s-3,2)):s<=10?(st(e,ws,e.bl_tree),Ne(e,s-3,3)):(st(e,Ss,e.bl_tree),Ne(e,s-11,7));s=0,i=a,o===0?(f=138,u=3):a===o?(f=6,u=3):(f=7,u=4)}}function Mf(e){var t;for(Za(e,e.dyn_ltree,e.l_desc.max_code),Za(e,e.dyn_dtree,e.d_desc.max_code),Yi(e,e.bl_desc),t=va-1;t>=3&&e.bl_tree[Fs[t]*2+1]===0;t--);return e.opt_len+=3*(t+1)+5+5+4,t}function If(e,t,r,n){var i;for(Ne(e,t-257,5),Ne(e,r-1,5),Ne(e,n-4,4),i=0;i<n;i++)Ne(e,e.bl_tree[Fs[i]*2+1],3);Ya(e,e.dyn_ltree,t-1),Ya(e,e.dyn_dtree,r-1)}function Uf(e){var t=4093624447,r;for(r=0;r<=31;r++,t>>>=1)if(t&1&&e.dyn_ltree[r*2]!==0)return La;if(e.dyn_ltree[9*2]!==0||e.dyn_ltree[10*2]!==0||e.dyn_ltree[13*2]!==0)return Ga;for(r=32;r<un;r++)if(e.dyn_ltree[r*2]!==0)return Ga;return La}var Ja=!1;function Vf(e){Ja||(zf(),Ja=!0),e.l_desc=new Ri(e.dyn_ltree,ks),e.d_desc=new Ri(e.dyn_dtree,Cs),e.bl_desc=new Ri(e.bl_tree,Ts),e.bi_buf=0,e.bi_valid=0,Rs(e)}function Es(e,t,r,n){Ne(e,(Tf<<1)+(n?1:0),3),jf(e,t,r,!0)}function Wf(e){Ne(e,bs<<1,3),st(e,pa,dt),Bf(e)}function qf(e,t,r,n){var i,a,o=0;e.level>0?(e.strm.data_type===Cf&&(e.strm.data_type=Uf(e)),Yi(e,e.l_desc),Yi(e,e.d_desc),o=Mf(e),i=e.opt_len+3+7>>>3,a=e.static_len+3+7>>>3,a<=i&&(i=a)):i=a=r+5,r+4<=i&&t!==-1?Es(e,t,r,n):e.strategy===kf||a===i?(Ne(e,(bs<<1)+(n?1:0),3),Xa(e,dt,Mr)):(Ne(e,(Pf<<1)+(n?1:0),3),If(e,e.l_desc.max_code+1,e.d_desc.max_code+1,o+1),Xa(e,e.dyn_ltree,e.dyn_dtree)),Rs(e),n&&Os(e)}function Kf(e,t,r){return e.pending_buf[e.d_buf+e.last_lit*2]=t>>>8&255,e.pending_buf[e.d_buf+e.last_lit*2+1]=t&255,e.pending_buf[e.l_buf+e.last_lit]=r&255,e.last_lit++,t===0?e.dyn_ltree[r*2]++:(e.matches++,t--,e.dyn_ltree[(Yr[r]+un+1)*2]++,e.dyn_dtree[Ps(t)*2]++),e.last_lit===e.lit_bufsize-1}pr._tr_init=Vf;pr._tr_stored_block=Es;pr._tr_flush_block=qf;pr._tr_tally=Kf;pr._tr_align=Wf;function Lf(e,t,r,n){for(var i=e&65535|0,a=e>>>16&65535|0,o=0;r!==0;){o=r>2e3?2e3:r,r-=o;do i=i+t[n++]|0,a=a+i|0;while(--o);i%=65521,a%=65521}return i|a<<16|0}var Bs=Lf;function Gf(){for(var e,t=[],r=0;r<256;r++){e=r;for(var n=0;n<8;n++)e=e&1?3988292384^e>>>1:e>>>1;t[r]=e}return t}var Hf=Gf();function Xf(e,t,r,n){var i=Hf,a=n+r;e^=-1;for(var o=n;o<a;o++)e=e>>>8^i[(e^t[o])&255];return e^-1}var Ns=Xf,ya={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},Ae=yt,He=pr,zs=Bs,Ft=Ns,Zf=ya,Zt=0,Yf=1,Jf=3,Ot=4,Qa=5,ut=0,_a=1,Xe=-2,Qf=-3,Ei=-5,_f=-1,$f=1,mn=2,ec=3,tc=4,rc=0,nc=2,si=8,ic=9,ac=15,oc=8,sc=29,uc=256,Ji=uc+1+sc,fc=30,cc=19,lc=2*Ji+1,hc=15,te=3,Pt=258,tt=Pt+te+1,dc=32,ui=42,Qi=69,jn=73,Mn=91,In=103,Wt=113,Or=666,me=1,fn=2,qt=3,yr=4,vc=3;function At(e,t){return e.msg=Zf[t],t}function $a(e){return(e<<1)-(e>4?9:0)}function Tt(e){for(var t=e.length;--t>=0;)e[t]=0}function kt(e){var t=e.state,r=t.pending;r>e.avail_out&&(r=e.avail_out),r!==0&&(Ae.arraySet(e.output,t.pending_buf,t.pending_out,r,e.next_out),e.next_out+=r,t.pending_out+=r,e.total_out+=r,e.avail_out-=r,t.pending-=r,t.pending===0&&(t.pending_out=0))}function Ce(e,t){He._tr_flush_block(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,kt(e.strm)}function oe(e,t){e.pending_buf[e.pending++]=t}function kr(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=t&255}function pc(e,t,r,n){var i=e.avail_in;return i>n&&(i=n),i===0?0:(e.avail_in-=i,Ae.arraySet(t,e.input,e.next_in,i,r),e.state.wrap===1?e.adler=zs(e.adler,t,i,r):e.state.wrap===2&&(e.adler=Ft(e.adler,t,i,r)),e.next_in+=i,e.total_in+=i,i)}function js(e,t){var r=e.max_chain_length,n=e.strstart,i,a,o=e.prev_length,s=e.nice_match,f=e.strstart>e.w_size-tt?e.strstart-(e.w_size-tt):0,u=e.window,c=e.w_mask,l=e.prev,h=e.strstart+Pt,d=u[n+o-1],v=u[n+o];e.prev_length>=e.good_match&&(r>>=2),s>e.lookahead&&(s=e.lookahead);do if(i=t,!(u[i+o]!==v||u[i+o-1]!==d||u[i]!==u[n]||u[++i]!==u[n+1])){n+=2,i++;do;while(u[++n]===u[++i]&&u[++n]===u[++i]&&u[++n]===u[++i]&&u[++n]===u[++i]&&u[++n]===u[++i]&&u[++n]===u[++i]&&u[++n]===u[++i]&&u[++n]===u[++i]&&n<h);if(a=Pt-(h-n),n=h-Pt,a>o){if(e.match_start=t,o=a,a>=s)break;d=u[n+o-1],v=u[n+o]}}while((t=l[t&c])>f&&--r!==0);return o<=e.lookahead?o:e.lookahead}function Kt(e){var t=e.w_size,r,n,i,a,o;do{if(a=e.window_size-e.lookahead-e.strstart,e.strstart>=t+(t-tt)){Ae.arraySet(e.window,e.window,t,t,0),e.match_start-=t,e.strstart-=t,e.block_start-=t,n=e.hash_size,r=n;do i=e.head[--r],e.head[r]=i>=t?i-t:0;while(--n);n=t,r=n;do i=e.prev[--r],e.prev[r]=i>=t?i-t:0;while(--n);a+=t}if(e.strm.avail_in===0)break;if(n=pc(e.strm,e.window,e.strstart+e.lookahead,a),e.lookahead+=n,e.lookahead+e.insert>=te)for(o=e.strstart-e.insert,e.ins_h=e.window[o],e.ins_h=(e.ins_h<<e.hash_shift^e.window[o+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[o+te-1])&e.hash_mask,e.prev[o&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=o,o++,e.insert--,!(e.lookahead+e.insert<te)););}while(e.lookahead<tt&&e.strm.avail_in!==0)}function gc(e,t){var r=65535;for(r>e.pending_buf_size-5&&(r=e.pending_buf_size-5);;){if(e.lookahead<=1){if(Kt(e),e.lookahead===0&&t===Zt)return me;if(e.lookahead===0)break}e.strstart+=e.lookahead,e.lookahead=0;var n=e.block_start+r;if((e.strstart===0||e.strstart>=n)&&(e.lookahead=e.strstart-n,e.strstart=n,Ce(e,!1),e.strm.avail_out===0)||e.strstart-e.block_start>=e.w_size-tt&&(Ce(e,!1),e.strm.avail_out===0))return me}return e.insert=0,t===Ot?(Ce(e,!0),e.strm.avail_out===0?qt:yr):(e.strstart>e.block_start&&(Ce(e,!1),e.strm.avail_out===0),me)}function Bi(e,t){for(var r,n;;){if(e.lookahead<tt){if(Kt(e),e.lookahead<tt&&t===Zt)return me;if(e.lookahead===0)break}if(r=0,e.lookahead>=te&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+te-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),r!==0&&e.strstart-r<=e.w_size-tt&&(e.match_length=js(e,r)),e.match_length>=te)if(n=He._tr_tally(e,e.strstart-e.match_start,e.match_length-te),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=te){e.match_length--;do e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+te-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart;while(--e.match_length!==0);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else n=He._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(n&&(Ce(e,!1),e.strm.avail_out===0))return me}return e.insert=e.strstart<te-1?e.strstart:te-1,t===Ot?(Ce(e,!0),e.strm.avail_out===0?qt:yr):e.last_lit&&(Ce(e,!1),e.strm.avail_out===0)?me:fn}function rr(e,t){for(var r,n,i;;){if(e.lookahead<tt){if(Kt(e),e.lookahead<tt&&t===Zt)return me;if(e.lookahead===0)break}if(r=0,e.lookahead>=te&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+te-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=te-1,r!==0&&e.prev_length<e.max_lazy_match&&e.strstart-r<=e.w_size-tt&&(e.match_length=js(e,r),e.match_length<=5&&(e.strategy===$f||e.match_length===te&&e.strstart-e.match_start>4096)&&(e.match_length=te-1)),e.prev_length>=te&&e.match_length<=e.prev_length){i=e.strstart+e.lookahead-te,n=He._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-te),e.lookahead-=e.prev_length-1,e.prev_length-=2;do++e.strstart<=i&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+te-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart);while(--e.prev_length!==0);if(e.match_available=0,e.match_length=te-1,e.strstart++,n&&(Ce(e,!1),e.strm.avail_out===0))return me}else if(e.match_available){if(n=He._tr_tally(e,0,e.window[e.strstart-1]),n&&Ce(e,!1),e.strstart++,e.lookahead--,e.strm.avail_out===0)return me}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(n=He._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<te-1?e.strstart:te-1,t===Ot?(Ce(e,!0),e.strm.avail_out===0?qt:yr):e.last_lit&&(Ce(e,!1),e.strm.avail_out===0)?me:fn}function yc(e,t){for(var r,n,i,a,o=e.window;;){if(e.lookahead<=Pt){if(Kt(e),e.lookahead<=Pt&&t===Zt)return me;if(e.lookahead===0)break}if(e.match_length=0,e.lookahead>=te&&e.strstart>0&&(i=e.strstart-1,n=o[i],n===o[++i]&&n===o[++i]&&n===o[++i])){a=e.strstart+Pt;do;while(n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&i<a);e.match_length=Pt-(a-i),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=te?(r=He._tr_tally(e,1,e.match_length-te),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(r=He._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),r&&(Ce(e,!1),e.strm.avail_out===0))return me}return e.insert=0,t===Ot?(Ce(e,!0),e.strm.avail_out===0?qt:yr):e.last_lit&&(Ce(e,!1),e.strm.avail_out===0)?me:fn}function bc(e,t){for(var r;;){if(e.lookahead===0&&(Kt(e),e.lookahead===0)){if(t===Zt)return me;break}if(e.match_length=0,r=He._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,r&&(Ce(e,!1),e.strm.avail_out===0))return me}return e.insert=0,t===Ot?(Ce(e,!0),e.strm.avail_out===0?qt:yr):e.last_lit&&(Ce(e,!1),e.strm.avail_out===0)?me:fn}function it(e,t,r,n,i){this.good_length=e,this.max_lazy=t,this.nice_length=r,this.max_chain=n,this.func=i}var sr;sr=[new it(0,0,0,0,gc),new it(4,4,8,4,Bi),new it(4,5,16,8,Bi),new it(4,6,32,32,Bi),new it(4,4,16,16,rr),new it(8,16,32,32,rr),new it(8,16,128,128,rr),new it(8,32,128,256,rr),new it(32,128,258,1024,rr),new it(32,258,258,4096,rr)];function mc(e){e.window_size=2*e.w_size,Tt(e.head),e.max_lazy_match=sr[e.level].max_lazy,e.good_match=sr[e.level].good_length,e.nice_match=sr[e.level].nice_length,e.max_chain_length=sr[e.level].max_chain,e.strstart=0,e.block_start=0,e.lookahead=0,e.insert=0,e.match_length=e.prev_length=te-1,e.match_available=0,e.ins_h=0}function xc(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=si,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new Ae.Buf16(lc*2),this.dyn_dtree=new Ae.Buf16((2*fc+1)*2),this.bl_tree=new Ae.Buf16((2*cc+1)*2),Tt(this.dyn_ltree),Tt(this.dyn_dtree),Tt(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new Ae.Buf16(hc+1),this.heap=new Ae.Buf16(2*Ji+1),Tt(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new Ae.Buf16(2*Ji+1),Tt(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function Ms(e){var t;return!e||!e.state?At(e,Xe):(e.total_in=e.total_out=0,e.data_type=nc,t=e.state,t.pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?ui:Wt,e.adler=t.wrap===2?0:1,t.last_flush=Zt,He._tr_init(t),ut)}function Is(e){var t=Ms(e);return t===ut&&mc(e.state),t}function wc(e,t){return!e||!e.state||e.state.wrap!==2?Xe:(e.state.gzhead=t,ut)}function Us(e,t,r,n,i,a){if(!e)return Xe;var o=1;if(t===_f&&(t=6),n<0?(o=0,n=-n):n>15&&(o=2,n-=16),i<1||i>ic||r!==si||n<8||n>15||t<0||t>9||a<0||a>tc)return At(e,Xe);n===8&&(n=9);var s=new xc;return e.state=s,s.strm=e,s.wrap=o,s.gzhead=null,s.w_bits=n,s.w_size=1<<s.w_bits,s.w_mask=s.w_size-1,s.hash_bits=i+7,s.hash_size=1<<s.hash_bits,s.hash_mask=s.hash_size-1,s.hash_shift=~~((s.hash_bits+te-1)/te),s.window=new Ae.Buf8(s.w_size*2),s.head=new Ae.Buf16(s.hash_size),s.prev=new Ae.Buf16(s.w_size),s.lit_bufsize=1<<i+6,s.pending_buf_size=s.lit_bufsize*4,s.pending_buf=new Ae.Buf8(s.pending_buf_size),s.d_buf=1*s.lit_bufsize,s.l_buf=(1+2)*s.lit_bufsize,s.level=t,s.strategy=a,s.method=r,Is(e)}function Sc(e,t){return Us(e,t,si,ac,oc,rc)}function Fc(e,t){var r,n,i,a;if(!e||!e.state||t>Qa||t<0)return e?At(e,Xe):Xe;if(n=e.state,!e.output||!e.input&&e.avail_in!==0||n.status===Or&&t!==Ot)return At(e,e.avail_out===0?Ei:Xe);if(n.strm=e,r=n.last_flush,n.last_flush=t,n.status===ui)if(n.wrap===2)e.adler=0,oe(n,31),oe(n,139),oe(n,8),n.gzhead?(oe(n,(n.gzhead.text?1:0)+(n.gzhead.hcrc?2:0)+(n.gzhead.extra?4:0)+(n.gzhead.name?8:0)+(n.gzhead.comment?16:0)),oe(n,n.gzhead.time&255),oe(n,n.gzhead.time>>8&255),oe(n,n.gzhead.time>>16&255),oe(n,n.gzhead.time>>24&255),oe(n,n.level===9?2:n.strategy>=mn||n.level<2?4:0),oe(n,n.gzhead.os&255),n.gzhead.extra&&n.gzhead.extra.length&&(oe(n,n.gzhead.extra.length&255),oe(n,n.gzhead.extra.length>>8&255)),n.gzhead.hcrc&&(e.adler=Ft(e.adler,n.pending_buf,n.pending,0)),n.gzindex=0,n.status=Qi):(oe(n,0),oe(n,0),oe(n,0),oe(n,0),oe(n,0),oe(n,n.level===9?2:n.strategy>=mn||n.level<2?4:0),oe(n,vc),n.status=Wt);else{var o=si+(n.w_bits-8<<4)<<8,s=-1;n.strategy>=mn||n.level<2?s=0:n.level<6?s=1:n.level===6?s=2:s=3,o|=s<<6,n.strstart!==0&&(o|=dc),o+=31-o%31,n.status=Wt,kr(n,o),n.strstart!==0&&(kr(n,e.adler>>>16),kr(n,e.adler&65535)),e.adler=1}if(n.status===Qi)if(n.gzhead.extra){for(i=n.pending;n.gzindex<(n.gzhead.extra.length&65535)&&!(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>i&&(e.adler=Ft(e.adler,n.pending_buf,n.pending-i,i)),kt(e),i=n.pending,n.pending===n.pending_buf_size));)oe(n,n.gzhead.extra[n.gzindex]&255),n.gzindex++;n.gzhead.hcrc&&n.pending>i&&(e.adler=Ft(e.adler,n.pending_buf,n.pending-i,i)),n.gzindex===n.gzhead.extra.length&&(n.gzindex=0,n.status=jn)}else n.status=jn;if(n.status===jn)if(n.gzhead.name){i=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>i&&(e.adler=Ft(e.adler,n.pending_buf,n.pending-i,i)),kt(e),i=n.pending,n.pending===n.pending_buf_size)){a=1;break}n.gzindex<n.gzhead.name.length?a=n.gzhead.name.charCodeAt(n.gzindex++)&255:a=0,oe(n,a)}while(a!==0);n.gzhead.hcrc&&n.pending>i&&(e.adler=Ft(e.adler,n.pending_buf,n.pending-i,i)),a===0&&(n.gzindex=0,n.status=Mn)}else n.status=Mn;if(n.status===Mn)if(n.gzhead.comment){i=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>i&&(e.adler=Ft(e.adler,n.pending_buf,n.pending-i,i)),kt(e),i=n.pending,n.pending===n.pending_buf_size)){a=1;break}n.gzindex<n.gzhead.comment.length?a=n.gzhead.comment.charCodeAt(n.gzindex++)&255:a=0,oe(n,a)}while(a!==0);n.gzhead.hcrc&&n.pending>i&&(e.adler=Ft(e.adler,n.pending_buf,n.pending-i,i)),a===0&&(n.status=In)}else n.status=In;if(n.status===In&&(n.gzhead.hcrc?(n.pending+2>n.pending_buf_size&&kt(e),n.pending+2<=n.pending_buf_size&&(oe(n,e.adler&255),oe(n,e.adler>>8&255),e.adler=0,n.status=Wt)):n.status=Wt),n.pending!==0){if(kt(e),e.avail_out===0)return n.last_flush=-1,ut}else if(e.avail_in===0&&$a(t)<=$a(r)&&t!==Ot)return At(e,Ei);if(n.status===Or&&e.avail_in!==0)return At(e,Ei);if(e.avail_in!==0||n.lookahead!==0||t!==Zt&&n.status!==Or){var f=n.strategy===mn?bc(n,t):n.strategy===ec?yc(n,t):sr[n.level].func(n,t);if((f===qt||f===yr)&&(n.status=Or),f===me||f===qt)return e.avail_out===0&&(n.last_flush=-1),ut;if(f===fn&&(t===Yf?He._tr_align(n):t!==Qa&&(He._tr_stored_block(n,0,0,!1),t===Jf&&(Tt(n.head),n.lookahead===0&&(n.strstart=0,n.block_start=0,n.insert=0))),kt(e),e.avail_out===0))return n.last_flush=-1,ut}return t!==Ot?ut:n.wrap<=0?_a:(n.wrap===2?(oe(n,e.adler&255),oe(n,e.adler>>8&255),oe(n,e.adler>>16&255),oe(n,e.adler>>24&255),oe(n,e.total_in&255),oe(n,e.total_in>>8&255),oe(n,e.total_in>>16&255),oe(n,e.total_in>>24&255)):(kr(n,e.adler>>>16),kr(n,e.adler&65535)),kt(e),n.wrap>0&&(n.wrap=-n.wrap),n.pending!==0?ut:_a)}function kc(e){var t;return!e||!e.state?Xe:(t=e.state.status,t!==ui&&t!==Qi&&t!==jn&&t!==Mn&&t!==In&&t!==Wt&&t!==Or?At(e,Xe):(e.state=null,t===Wt?At(e,Qf):ut))}function Cc(e,t){var r=t.length,n,i,a,o,s,f,u,c;if(!e||!e.state||(n=e.state,o=n.wrap,o===2||o===1&&n.status!==ui||n.lookahead))return Xe;for(o===1&&(e.adler=zs(e.adler,t,r,0)),n.wrap=0,r>=n.w_size&&(o===0&&(Tt(n.head),n.strstart=0,n.block_start=0,n.insert=0),c=new Ae.Buf8(n.w_size),Ae.arraySet(c,t,r-n.w_size,n.w_size,0),t=c,r=n.w_size),s=e.avail_in,f=e.next_in,u=e.input,e.avail_in=r,e.next_in=0,e.input=t,Kt(n);n.lookahead>=te;){i=n.strstart,a=n.lookahead-(te-1);do n.ins_h=(n.ins_h<<n.hash_shift^n.window[i+te-1])&n.hash_mask,n.prev[i&n.w_mask]=n.head[n.ins_h],n.head[n.ins_h]=i,i++;while(--a);n.strstart=i,n.lookahead=te-1,Kt(n)}return n.strstart+=n.lookahead,n.block_start=n.strstart,n.insert=n.lookahead,n.lookahead=0,n.match_length=n.prev_length=te-1,n.match_available=0,e.next_in=f,e.input=u,e.avail_in=s,n.wrap=o,ut}ct.deflateInit=Sc;ct.deflateInit2=Us;ct.deflateReset=Is;ct.deflateResetKeep=Ms;ct.deflateSetHeader=wc;ct.deflate=Fc;ct.deflateEnd=kc;ct.deflateSetDictionary=Cc;ct.deflateInfo="pako deflate (from Nodeca project)";var Yt={},fi=yt,Vs=!0,Ws=!0;try{String.fromCharCode.apply(null,[0])}catch{Vs=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch{Ws=!1}var Qr=new fi.Buf8(256);for(var wt=0;wt<256;wt++)Qr[wt]=wt>=252?6:wt>=248?5:wt>=240?4:wt>=224?3:wt>=192?2:1;Qr[254]=Qr[254]=1;Yt.string2buf=function(e){var t,r,n,i,a,o=e.length,s=0;for(i=0;i<o;i++)r=e.charCodeAt(i),(r&64512)===55296&&i+1<o&&(n=e.charCodeAt(i+1),(n&64512)===56320&&(r=65536+(r-55296<<10)+(n-56320),i++)),s+=r<128?1:r<2048?2:r<65536?3:4;for(t=new fi.Buf8(s),a=0,i=0;a<s;i++)r=e.charCodeAt(i),(r&64512)===55296&&i+1<o&&(n=e.charCodeAt(i+1),(n&64512)===56320&&(r=65536+(r-55296<<10)+(n-56320),i++)),r<128?t[a++]=r:r<2048?(t[a++]=192|r>>>6,t[a++]=128|r&63):r<65536?(t[a++]=224|r>>>12,t[a++]=128|r>>>6&63,t[a++]=128|r&63):(t[a++]=240|r>>>18,t[a++]=128|r>>>12&63,t[a++]=128|r>>>6&63,t[a++]=128|r&63);return t};function qs(e,t){if(t<65534&&(e.subarray&&Ws||!e.subarray&&Vs))return String.fromCharCode.apply(null,fi.shrinkBuf(e,t));for(var r="",n=0;n<t;n++)r+=String.fromCharCode(e[n]);return r}Yt.buf2binstring=function(e){return qs(e,e.length)};Yt.binstring2buf=function(e){for(var t=new fi.Buf8(e.length),r=0,n=t.length;r<n;r++)t[r]=e.charCodeAt(r);return t};Yt.buf2string=function(e,t){var r,n,i,a,o=t||e.length,s=new Array(o*2);for(n=0,r=0;r<o;){if(i=e[r++],i<128){s[n++]=i;continue}if(a=Qr[i],a>4){s[n++]=65533,r+=a-1;continue}for(i&=a===2?31:a===3?15:7;a>1&&r<o;)i=i<<6|e[r++]&63,a--;if(a>1){s[n++]=65533;continue}i<65536?s[n++]=i:(i-=65536,s[n++]=55296|i>>10&1023,s[n++]=56320|i&1023)}return qs(s,n)};Yt.utf8border=function(e,t){var r;for(t=t||e.length,t>e.length&&(t=e.length),r=t-1;r>=0&&(e[r]&192)===128;)r--;return r<0||r===0?t:r+Qr[e[r]]>t?r:t};function Tc(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}var Ks=Tc,Ir=ct,Ur=yt,_i=Yt,$i=ya,Pc=Ks,Ls=Object.prototype.toString,Ac=0,Ni=4,cr=0,eo=1,to=2,Dc=-1,Rc=0,Oc=8;function Lt(e){if(!(this instanceof Lt))return new Lt(e);this.options=Ur.assign({level:Dc,method:Oc,chunkSize:16384,windowBits:15,memLevel:8,strategy:Rc,to:""},e||{});var t=this.options;t.raw&&t.windowBits>0?t.windowBits=-t.windowBits:t.gzip&&t.windowBits>0&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new Pc,this.strm.avail_out=0;var r=Ir.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(r!==cr)throw new Error($i[r]);if(t.header&&Ir.deflateSetHeader(this.strm,t.header),t.dictionary){var n;if(typeof t.dictionary=="string"?n=_i.string2buf(t.dictionary):Ls.call(t.dictionary)==="[object ArrayBuffer]"?n=new Uint8Array(t.dictionary):n=t.dictionary,r=Ir.deflateSetDictionary(this.strm,n),r!==cr)throw new Error($i[r]);this._dict_set=!0}}Lt.prototype.push=function(e,t){var r=this.strm,n=this.options.chunkSize,i,a;if(this.ended)return!1;a=t===~~t?t:t===!0?Ni:Ac,typeof e=="string"?r.input=_i.string2buf(e):Ls.call(e)==="[object ArrayBuffer]"?r.input=new Uint8Array(e):r.input=e,r.next_in=0,r.avail_in=r.input.length;do{if(r.avail_out===0&&(r.output=new Ur.Buf8(n),r.next_out=0,r.avail_out=n),i=Ir.deflate(r,a),i!==eo&&i!==cr)return this.onEnd(i),this.ended=!0,!1;(r.avail_out===0||r.avail_in===0&&(a===Ni||a===to))&&(this.options.to==="string"?this.onData(_i.buf2binstring(Ur.shrinkBuf(r.output,r.next_out))):this.onData(Ur.shrinkBuf(r.output,r.next_out)))}while((r.avail_in>0||r.avail_out===0)&&i!==eo);return a===Ni?(i=Ir.deflateEnd(this.strm),this.onEnd(i),this.ended=!0,i===cr):(a===to&&(this.onEnd(cr),r.avail_out=0),!0)};Lt.prototype.onData=function(e){this.chunks.push(e)};Lt.prototype.onEnd=function(e){e===cr&&(this.options.to==="string"?this.result=this.chunks.join(""):this.result=Ur.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg};function ba(e,t){var r=new Lt(t);if(r.push(e,!0),r.err)throw r.msg||$i[r.err];return r.result}function Ec(e,t){return t=t||{},t.raw=!0,ba(e,t)}function Bc(e,t){return t=t||{},t.gzip=!0,ba(e,t)}sn.Deflate=Lt;sn.deflate=ba;sn.deflateRaw=Ec;sn.gzip=Bc;var cn={},rt={},xn=30,Nc=12,zc=function(t,r){var n,i,a,o,s,f,u,c,l,h,d,v,g,m,b,F,w,C,S,k,T,P,D,N,A;n=t.state,i=t.next_in,N=t.input,a=i+(t.avail_in-5),o=t.next_out,A=t.output,s=o-(r-t.avail_out),f=o+(t.avail_out-257),u=n.dmax,c=n.wsize,l=n.whave,h=n.wnext,d=n.window,v=n.hold,g=n.bits,m=n.lencode,b=n.distcode,F=(1<<n.lenbits)-1,w=(1<<n.distbits)-1;e:do{g<15&&(v+=N[i++]<<g,g+=8,v+=N[i++]<<g,g+=8),C=m[v&F];t:for(;;){if(S=C>>>24,v>>>=S,g-=S,S=C>>>16&255,S===0)A[o++]=C&65535;else if(S&16){k=C&65535,S&=15,S&&(g<S&&(v+=N[i++]<<g,g+=8),k+=v&(1<<S)-1,v>>>=S,g-=S),g<15&&(v+=N[i++]<<g,g+=8,v+=N[i++]<<g,g+=8),C=b[v&w];r:for(;;){if(S=C>>>24,v>>>=S,g-=S,S=C>>>16&255,S&16){if(T=C&65535,S&=15,g<S&&(v+=N[i++]<<g,g+=8,g<S&&(v+=N[i++]<<g,g+=8)),T+=v&(1<<S)-1,T>u){t.msg="invalid distance too far back",n.mode=xn;break e}if(v>>>=S,g-=S,S=o-s,T>S){if(S=T-S,S>l&&n.sane){t.msg="invalid distance too far back",n.mode=xn;break e}if(P=0,D=d,h===0){if(P+=c-S,S<k){k-=S;do A[o++]=d[P++];while(--S);P=o-T,D=A}}else if(h<S){if(P+=c+h-S,S-=h,S<k){k-=S;do A[o++]=d[P++];while(--S);if(P=0,h<k){S=h,k-=S;do A[o++]=d[P++];while(--S);P=o-T,D=A}}}else if(P+=h-S,S<k){k-=S;do A[o++]=d[P++];while(--S);P=o-T,D=A}for(;k>2;)A[o++]=D[P++],A[o++]=D[P++],A[o++]=D[P++],k-=3;k&&(A[o++]=D[P++],k>1&&(A[o++]=D[P++]))}else{P=o-T;do A[o++]=A[P++],A[o++]=A[P++],A[o++]=A[P++],k-=3;while(k>2);k&&(A[o++]=A[P++],k>1&&(A[o++]=A[P++]))}}else if(S&64){t.msg="invalid distance code",n.mode=xn;break e}else{C=b[(C&65535)+(v&(1<<S)-1)];continue r}break}}else if(S&64)if(S&32){n.mode=Nc;break e}else{t.msg="invalid literal/length code",n.mode=xn;break e}else{C=m[(C&65535)+(v&(1<<S)-1)];continue t}break}}while(i<a&&o<f);k=g>>3,i-=k,g-=k<<3,v&=(1<<g)-1,t.next_in=i,t.next_out=o,t.avail_in=i<a?5+(a-i):5-(i-a),t.avail_out=o<f?257+(f-o):257-(o-f),n.hold=v,n.bits=g},ro=yt,nr=15,no=852,io=592,ao=0,zi=1,oo=2,jc=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],Mc=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],Ic=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],Uc=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64],Vc=function(t,r,n,i,a,o,s,f){var u=f.bits,c=0,l=0,h=0,d=0,v=0,g=0,m=0,b=0,F=0,w=0,C,S,k,T,P,D=null,N=0,A,z=new ro.Buf16(nr+1),B=new ro.Buf16(nr+1),W=null,L=0,j,ie,Fe;for(c=0;c<=nr;c++)z[c]=0;for(l=0;l<i;l++)z[r[n+l]]++;for(v=u,d=nr;d>=1&&z[d]===0;d--);if(v>d&&(v=d),d===0)return a[o++]=1<<24|64<<16|0,a[o++]=1<<24|64<<16|0,f.bits=1,0;for(h=1;h<d&&z[h]===0;h++);for(v<h&&(v=h),b=1,c=1;c<=nr;c++)if(b<<=1,b-=z[c],b<0)return-1;if(b>0&&(t===ao||d!==1))return-1;for(B[1]=0,c=1;c<nr;c++)B[c+1]=B[c]+z[c];for(l=0;l<i;l++)r[n+l]!==0&&(s[B[r[n+l]]++]=l);if(t===ao?(D=W=s,A=19):t===zi?(D=jc,N-=257,W=Mc,L-=257,A=256):(D=Ic,W=Uc,A=-1),w=0,l=0,c=h,P=o,g=v,m=0,k=-1,F=1<<v,T=F-1,t===zi&&F>no||t===oo&&F>io)return 1;for(;;){j=c-m,s[l]<A?(ie=0,Fe=s[l]):s[l]>A?(ie=W[L+s[l]],Fe=D[N+s[l]]):(ie=32+64,Fe=0),C=1<<c-m,S=1<<g,h=S;do S-=C,a[P+(w>>m)+S]=j<<24|ie<<16|Fe|0;while(S!==0);for(C=1<<c-1;w&C;)C>>=1;if(C!==0?(w&=C-1,w+=C):w=0,l++,--z[c]===0){if(c===d)break;c=r[n+s[l]]}if(c>v&&(w&T)!==k){for(m===0&&(m=v),P+=h,g=c-m,b=1<<g;g+m<d&&(b-=z[g+m],!(b<=0));)g++,b<<=1;if(F+=1<<g,t===zi&&F>no||t===oo&&F>io)return 1;k=w&T,a[k]=v<<24|g<<16|P-o|0}}return w!==0&&(a[P+w]=c-m<<24|64<<16|0),f.bits=v,0},ze=yt,ea=Bs,at=Ns,Wc=zc,Vr=Vc,qc=0,Gs=1,Hs=2,so=4,Kc=5,wn=6,Gt=0,Lc=1,Gc=2,Ze=-2,Xs=-3,Zs=-4,Hc=-5,uo=8,Ys=1,fo=2,co=3,lo=4,ho=5,vo=6,po=7,go=8,yo=9,bo=10,Xn=11,lt=12,ji=13,mo=14,Mi=15,xo=16,wo=17,So=18,Fo=19,Sn=20,Fn=21,ko=22,Co=23,To=24,Po=25,Ao=26,Ii=27,Do=28,Ro=29,he=30,Js=31,Xc=32,Zc=852,Yc=592,Jc=15,Qc=Jc;function Oo(e){return(e>>>24&255)+(e>>>8&65280)+((e&65280)<<8)+((e&255)<<24)}function _c(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new ze.Buf16(320),this.work=new ze.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function Qs(e){var t;return!e||!e.state?Ze:(t=e.state,e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=t.wrap&1),t.mode=Ys,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new ze.Buf32(Zc),t.distcode=t.distdyn=new ze.Buf32(Yc),t.sane=1,t.back=-1,Gt)}function _s(e){var t;return!e||!e.state?Ze:(t=e.state,t.wsize=0,t.whave=0,t.wnext=0,Qs(e))}function $s(e,t){var r,n;return!e||!e.state||(n=e.state,t<0?(r=0,t=-t):(r=(t>>4)+1,t<48&&(t&=15)),t&&(t<8||t>15))?Ze:(n.window!==null&&n.wbits!==t&&(n.window=null),n.wrap=r,n.wbits=t,_s(e))}function eu(e,t){var r,n;return e?(n=new _c,e.state=n,n.window=null,r=$s(e,t),r!==Gt&&(e.state=null),r):Ze}function $c(e){return eu(e,Qc)}var Eo=!0,Ui,Vi;function el(e){if(Eo){var t;for(Ui=new ze.Buf32(512),Vi=new ze.Buf32(32),t=0;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(Vr(Gs,e.lens,0,288,Ui,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;Vr(Hs,e.lens,0,32,Vi,0,e.work,{bits:5}),Eo=!1}e.lencode=Ui,e.lenbits=9,e.distcode=Vi,e.distbits=5}function tu(e,t,r,n){var i,a=e.state;return a.window===null&&(a.wsize=1<<a.wbits,a.wnext=0,a.whave=0,a.window=new ze.Buf8(a.wsize)),n>=a.wsize?(ze.arraySet(a.window,t,r-a.wsize,a.wsize,0),a.wnext=0,a.whave=a.wsize):(i=a.wsize-a.wnext,i>n&&(i=n),ze.arraySet(a.window,t,r-n,i,a.wnext),n-=i,n?(ze.arraySet(a.window,t,r-n,n,0),a.wnext=n,a.whave=a.wsize):(a.wnext+=i,a.wnext===a.wsize&&(a.wnext=0),a.whave<a.wsize&&(a.whave+=i))),0}function tl(e,t){var r,n,i,a,o,s,f,u,c,l,h,d,v,g,m=0,b,F,w,C,S,k,T,P,D=new ze.Buf8(4),N,A,z=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!e||!e.state||!e.output||!e.input&&e.avail_in!==0)return Ze;r=e.state,r.mode===lt&&(r.mode=ji),o=e.next_out,i=e.output,f=e.avail_out,a=e.next_in,n=e.input,s=e.avail_in,u=r.hold,c=r.bits,l=s,h=f,P=Gt;e:for(;;)switch(r.mode){case Ys:if(r.wrap===0){r.mode=ji;break}for(;c<16;){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}if(r.wrap&2&&u===35615){r.check=0,D[0]=u&255,D[1]=u>>>8&255,r.check=at(r.check,D,2,0),u=0,c=0,r.mode=fo;break}if(r.flags=0,r.head&&(r.head.done=!1),!(r.wrap&1)||(((u&255)<<8)+(u>>8))%31){e.msg="incorrect header check",r.mode=he;break}if((u&15)!==uo){e.msg="unknown compression method",r.mode=he;break}if(u>>>=4,c-=4,T=(u&15)+8,r.wbits===0)r.wbits=T;else if(T>r.wbits){e.msg="invalid window size",r.mode=he;break}r.dmax=1<<T,e.adler=r.check=1,r.mode=u&512?bo:lt,u=0,c=0;break;case fo:for(;c<16;){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}if(r.flags=u,(r.flags&255)!==uo){e.msg="unknown compression method",r.mode=he;break}if(r.flags&57344){e.msg="unknown header flags set",r.mode=he;break}r.head&&(r.head.text=u>>8&1),r.flags&512&&(D[0]=u&255,D[1]=u>>>8&255,r.check=at(r.check,D,2,0)),u=0,c=0,r.mode=co;case co:for(;c<32;){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}r.head&&(r.head.time=u),r.flags&512&&(D[0]=u&255,D[1]=u>>>8&255,D[2]=u>>>16&255,D[3]=u>>>24&255,r.check=at(r.check,D,4,0)),u=0,c=0,r.mode=lo;case lo:for(;c<16;){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}r.head&&(r.head.xflags=u&255,r.head.os=u>>8),r.flags&512&&(D[0]=u&255,D[1]=u>>>8&255,r.check=at(r.check,D,2,0)),u=0,c=0,r.mode=ho;case ho:if(r.flags&1024){for(;c<16;){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}r.length=u,r.head&&(r.head.extra_len=u),r.flags&512&&(D[0]=u&255,D[1]=u>>>8&255,r.check=at(r.check,D,2,0)),u=0,c=0}else r.head&&(r.head.extra=null);r.mode=vo;case vo:if(r.flags&1024&&(d=r.length,d>s&&(d=s),d&&(r.head&&(T=r.head.extra_len-r.length,r.head.extra||(r.head.extra=new Array(r.head.extra_len)),ze.arraySet(r.head.extra,n,a,d,T)),r.flags&512&&(r.check=at(r.check,n,d,a)),s-=d,a+=d,r.length-=d),r.length))break e;r.length=0,r.mode=po;case po:if(r.flags&2048){if(s===0)break e;d=0;do T=n[a+d++],r.head&&T&&r.length<65536&&(r.head.name+=String.fromCharCode(T));while(T&&d<s);if(r.flags&512&&(r.check=at(r.check,n,d,a)),s-=d,a+=d,T)break e}else r.head&&(r.head.name=null);r.length=0,r.mode=go;case go:if(r.flags&4096){if(s===0)break e;d=0;do T=n[a+d++],r.head&&T&&r.length<65536&&(r.head.comment+=String.fromCharCode(T));while(T&&d<s);if(r.flags&512&&(r.check=at(r.check,n,d,a)),s-=d,a+=d,T)break e}else r.head&&(r.head.comment=null);r.mode=yo;case yo:if(r.flags&512){for(;c<16;){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}if(u!==(r.check&65535)){e.msg="header crc mismatch",r.mode=he;break}u=0,c=0}r.head&&(r.head.hcrc=r.flags>>9&1,r.head.done=!0),e.adler=r.check=0,r.mode=lt;break;case bo:for(;c<32;){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}e.adler=r.check=Oo(u),u=0,c=0,r.mode=Xn;case Xn:if(r.havedict===0)return e.next_out=o,e.avail_out=f,e.next_in=a,e.avail_in=s,r.hold=u,r.bits=c,Gc;e.adler=r.check=1,r.mode=lt;case lt:if(t===Kc||t===wn)break e;case ji:if(r.last){u>>>=c&7,c-=c&7,r.mode=Ii;break}for(;c<3;){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}switch(r.last=u&1,u>>>=1,c-=1,u&3){case 0:r.mode=mo;break;case 1:if(el(r),r.mode=Sn,t===wn){u>>>=2,c-=2;break e}break;case 2:r.mode=wo;break;case 3:e.msg="invalid block type",r.mode=he}u>>>=2,c-=2;break;case mo:for(u>>>=c&7,c-=c&7;c<32;){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}if((u&65535)!==(u>>>16^65535)){e.msg="invalid stored block lengths",r.mode=he;break}if(r.length=u&65535,u=0,c=0,r.mode=Mi,t===wn)break e;case Mi:r.mode=xo;case xo:if(d=r.length,d){if(d>s&&(d=s),d>f&&(d=f),d===0)break e;ze.arraySet(i,n,a,d,o),s-=d,a+=d,f-=d,o+=d,r.length-=d;break}r.mode=lt;break;case wo:for(;c<14;){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}if(r.nlen=(u&31)+257,u>>>=5,c-=5,r.ndist=(u&31)+1,u>>>=5,c-=5,r.ncode=(u&15)+4,u>>>=4,c-=4,r.nlen>286||r.ndist>30){e.msg="too many length or distance symbols",r.mode=he;break}r.have=0,r.mode=So;case So:for(;r.have<r.ncode;){for(;c<3;){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}r.lens[z[r.have++]]=u&7,u>>>=3,c-=3}for(;r.have<19;)r.lens[z[r.have++]]=0;if(r.lencode=r.lendyn,r.lenbits=7,N={bits:r.lenbits},P=Vr(qc,r.lens,0,19,r.lencode,0,r.work,N),r.lenbits=N.bits,P){e.msg="invalid code lengths set",r.mode=he;break}r.have=0,r.mode=Fo;case Fo:for(;r.have<r.nlen+r.ndist;){for(;m=r.lencode[u&(1<<r.lenbits)-1],b=m>>>24,F=m>>>16&255,w=m&65535,!(b<=c);){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}if(w<16)u>>>=b,c-=b,r.lens[r.have++]=w;else{if(w===16){for(A=b+2;c<A;){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}if(u>>>=b,c-=b,r.have===0){e.msg="invalid bit length repeat",r.mode=he;break}T=r.lens[r.have-1],d=3+(u&3),u>>>=2,c-=2}else if(w===17){for(A=b+3;c<A;){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}u>>>=b,c-=b,T=0,d=3+(u&7),u>>>=3,c-=3}else{for(A=b+7;c<A;){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}u>>>=b,c-=b,T=0,d=11+(u&127),u>>>=7,c-=7}if(r.have+d>r.nlen+r.ndist){e.msg="invalid bit length repeat",r.mode=he;break}for(;d--;)r.lens[r.have++]=T}}if(r.mode===he)break;if(r.lens[256]===0){e.msg="invalid code -- missing end-of-block",r.mode=he;break}if(r.lenbits=9,N={bits:r.lenbits},P=Vr(Gs,r.lens,0,r.nlen,r.lencode,0,r.work,N),r.lenbits=N.bits,P){e.msg="invalid literal/lengths set",r.mode=he;break}if(r.distbits=6,r.distcode=r.distdyn,N={bits:r.distbits},P=Vr(Hs,r.lens,r.nlen,r.ndist,r.distcode,0,r.work,N),r.distbits=N.bits,P){e.msg="invalid distances set",r.mode=he;break}if(r.mode=Sn,t===wn)break e;case Sn:r.mode=Fn;case Fn:if(s>=6&&f>=258){e.next_out=o,e.avail_out=f,e.next_in=a,e.avail_in=s,r.hold=u,r.bits=c,Wc(e,h),o=e.next_out,i=e.output,f=e.avail_out,a=e.next_in,n=e.input,s=e.avail_in,u=r.hold,c=r.bits,r.mode===lt&&(r.back=-1);break}for(r.back=0;m=r.lencode[u&(1<<r.lenbits)-1],b=m>>>24,F=m>>>16&255,w=m&65535,!(b<=c);){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}if(F&&!(F&240)){for(C=b,S=F,k=w;m=r.lencode[k+((u&(1<<C+S)-1)>>C)],b=m>>>24,F=m>>>16&255,w=m&65535,!(C+b<=c);){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}u>>>=C,c-=C,r.back+=C}if(u>>>=b,c-=b,r.back+=b,r.length=w,F===0){r.mode=Ao;break}if(F&32){r.back=-1,r.mode=lt;break}if(F&64){e.msg="invalid literal/length code",r.mode=he;break}r.extra=F&15,r.mode=ko;case ko:if(r.extra){for(A=r.extra;c<A;){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}r.length+=u&(1<<r.extra)-1,u>>>=r.extra,c-=r.extra,r.back+=r.extra}r.was=r.length,r.mode=Co;case Co:for(;m=r.distcode[u&(1<<r.distbits)-1],b=m>>>24,F=m>>>16&255,w=m&65535,!(b<=c);){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}if(!(F&240)){for(C=b,S=F,k=w;m=r.distcode[k+((u&(1<<C+S)-1)>>C)],b=m>>>24,F=m>>>16&255,w=m&65535,!(C+b<=c);){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}u>>>=C,c-=C,r.back+=C}if(u>>>=b,c-=b,r.back+=b,F&64){e.msg="invalid distance code",r.mode=he;break}r.offset=w,r.extra=F&15,r.mode=To;case To:if(r.extra){for(A=r.extra;c<A;){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}r.offset+=u&(1<<r.extra)-1,u>>>=r.extra,c-=r.extra,r.back+=r.extra}if(r.offset>r.dmax){e.msg="invalid distance too far back",r.mode=he;break}r.mode=Po;case Po:if(f===0)break e;if(d=h-f,r.offset>d){if(d=r.offset-d,d>r.whave&&r.sane){e.msg="invalid distance too far back",r.mode=he;break}d>r.wnext?(d-=r.wnext,v=r.wsize-d):v=r.wnext-d,d>r.length&&(d=r.length),g=r.window}else g=i,v=o-r.offset,d=r.length;d>f&&(d=f),f-=d,r.length-=d;do i[o++]=g[v++];while(--d);r.length===0&&(r.mode=Fn);break;case Ao:if(f===0)break e;i[o++]=r.length,f--,r.mode=Fn;break;case Ii:if(r.wrap){for(;c<32;){if(s===0)break e;s--,u|=n[a++]<<c,c+=8}if(h-=f,e.total_out+=h,r.total+=h,h&&(e.adler=r.check=r.flags?at(r.check,i,h,o-h):ea(r.check,i,h,o-h)),h=f,(r.flags?u:Oo(u))!==r.check){e.msg="incorrect data check",r.mode=he;break}u=0,c=0}r.mode=Do;case Do:if(r.wrap&&r.flags){for(;c<32;){if(s===0)break e;s--,u+=n[a++]<<c,c+=8}if(u!==(r.total&4294967295)){e.msg="incorrect length check",r.mode=he;break}u=0,c=0}r.mode=Ro;case Ro:P=Lc;break e;case he:P=Xs;break e;case Js:return Zs;case Xc:default:return Ze}return e.next_out=o,e.avail_out=f,e.next_in=a,e.avail_in=s,r.hold=u,r.bits=c,(r.wsize||h!==e.avail_out&&r.mode<he&&(r.mode<Ii||t!==so))&&tu(e,e.output,e.next_out,h-e.avail_out),l-=e.avail_in,h-=e.avail_out,e.total_in+=l,e.total_out+=h,r.total+=h,r.wrap&&h&&(e.adler=r.check=r.flags?at(r.check,i,h,e.next_out-h):ea(r.check,i,h,e.next_out-h)),e.data_type=r.bits+(r.last?64:0)+(r.mode===lt?128:0)+(r.mode===Sn||r.mode===Mi?256:0),(l===0&&h===0||t===so)&&P===Gt&&(P=Hc),P}function rl(e){if(!e||!e.state)return Ze;var t=e.state;return t.window&&(t.window=null),e.state=null,Gt}function nl(e,t){var r;return!e||!e.state||(r=e.state,!(r.wrap&2))?Ze:(r.head=t,t.done=!1,Gt)}function il(e,t){var r=t.length,n,i,a;return!e||!e.state||(n=e.state,n.wrap!==0&&n.mode!==Xn)?Ze:n.mode===Xn&&(i=1,i=ea(i,t,r,0),i!==n.check)?Xs:(a=tu(e,t,r,r),a?(n.mode=Js,Zs):(n.havedict=1,Gt))}rt.inflateReset=_s;rt.inflateReset2=$s;rt.inflateResetKeep=Qs;rt.inflateInit=$c;rt.inflateInit2=eu;rt.inflate=tl;rt.inflateEnd=rl;rt.inflateGetHeader=nl;rt.inflateSetDictionary=il;rt.inflateInfo="pako inflate (from Nodeca project)";var ru={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8};function al(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}var ol=al,lr=rt,Wr=yt,Un=Yt,pe=ru,ta=ya,sl=Ks,ul=ol,nu=Object.prototype.toString;function Ht(e){if(!(this instanceof Ht))return new Ht(e);this.options=Wr.assign({chunkSize:16384,windowBits:0,to:""},e||{});var t=this.options;t.raw&&t.windowBits>=0&&t.windowBits<16&&(t.windowBits=-t.windowBits,t.windowBits===0&&(t.windowBits=-15)),t.windowBits>=0&&t.windowBits<16&&!(e&&e.windowBits)&&(t.windowBits+=32),t.windowBits>15&&t.windowBits<48&&(t.windowBits&15||(t.windowBits|=15)),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new sl,this.strm.avail_out=0;var r=lr.inflateInit2(this.strm,t.windowBits);if(r!==pe.Z_OK)throw new Error(ta[r]);if(this.header=new ul,lr.inflateGetHeader(this.strm,this.header),t.dictionary&&(typeof t.dictionary=="string"?t.dictionary=Un.string2buf(t.dictionary):nu.call(t.dictionary)==="[object ArrayBuffer]"&&(t.dictionary=new Uint8Array(t.dictionary)),t.raw&&(r=lr.inflateSetDictionary(this.strm,t.dictionary),r!==pe.Z_OK)))throw new Error(ta[r])}Ht.prototype.push=function(e,t){var r=this.strm,n=this.options.chunkSize,i=this.options.dictionary,a,o,s,f,u,c=!1;if(this.ended)return!1;o=t===~~t?t:t===!0?pe.Z_FINISH:pe.Z_NO_FLUSH,typeof e=="string"?r.input=Un.binstring2buf(e):nu.call(e)==="[object ArrayBuffer]"?r.input=new Uint8Array(e):r.input=e,r.next_in=0,r.avail_in=r.input.length;do{if(r.avail_out===0&&(r.output=new Wr.Buf8(n),r.next_out=0,r.avail_out=n),a=lr.inflate(r,pe.Z_NO_FLUSH),a===pe.Z_NEED_DICT&&i&&(a=lr.inflateSetDictionary(this.strm,i)),a===pe.Z_BUF_ERROR&&c===!0&&(a=pe.Z_OK,c=!1),a!==pe.Z_STREAM_END&&a!==pe.Z_OK)return this.onEnd(a),this.ended=!0,!1;r.next_out&&(r.avail_out===0||a===pe.Z_STREAM_END||r.avail_in===0&&(o===pe.Z_FINISH||o===pe.Z_SYNC_FLUSH))&&(this.options.to==="string"?(s=Un.utf8border(r.output,r.next_out),f=r.next_out-s,u=Un.buf2string(r.output,s),r.next_out=f,r.avail_out=n-f,f&&Wr.arraySet(r.output,r.output,s,f,0),this.onData(u)):this.onData(Wr.shrinkBuf(r.output,r.next_out))),r.avail_in===0&&r.avail_out===0&&(c=!0)}while((r.avail_in>0||r.avail_out===0)&&a!==pe.Z_STREAM_END);return a===pe.Z_STREAM_END&&(o=pe.Z_FINISH),o===pe.Z_FINISH?(a=lr.inflateEnd(this.strm),this.onEnd(a),this.ended=!0,a===pe.Z_OK):(o===pe.Z_SYNC_FLUSH&&(this.onEnd(pe.Z_OK),r.avail_out=0),!0)};Ht.prototype.onData=function(e){this.chunks.push(e)};Ht.prototype.onEnd=function(e){e===pe.Z_OK&&(this.options.to==="string"?this.result=this.chunks.join(""):this.result=Wr.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg};function ma(e,t){var r=new Ht(t);if(r.push(e,!0),r.err)throw r.msg||ta[r.err];return r.result}function fl(e,t){return t=t||{},t.raw=!0,ma(e,t)}cn.Inflate=Ht;cn.inflate=ma;cn.inflateRaw=fl;cn.ungzip=ma;var cl=yt.assign,ll=sn,hl=cn,dl=ru,iu={};cl(iu,ll,hl,dl);var vl=iu;const ci=Yu(vl);var Bo="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Er=new Uint8Array(256);for(var kn=0;kn<Bo.length;kn++)Er[Bo.charCodeAt(kn)]=kn;var pl=function(e){var t=e.length*.75,r=e.length,n,i=0,a,o,s,f;e[e.length-1]==="="&&(t--,e[e.length-2]==="="&&t--);var u=new Uint8Array(t);for(n=0;n<r;n+=4)a=Er[e.charCodeAt(n)],o=Er[e.charCodeAt(n+1)],s=Er[e.charCodeAt(n+2)],f=Er[e.charCodeAt(n+3)],u[i++]=a<<2|o>>4,u[i++]=(o&15)<<4|s>>2,u[i++]=(s&3)<<6|f&63;return u},gl=function(e){for(var t="",r=0;r<e.length;r++)t+=String.fromCharCode(e[r]);return t},au=function(e){return gl(ci.inflate(pl(e)))},yl=function(e,t,r){for(var n="",i=0,a=t-e.length;i<a;i++)n+=r;return n+e};const bl="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",ml="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",xl="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",wl="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",Sl="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",Fl="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",kl="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",Cl="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",Tl="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",Pl="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",Al="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",Dl="eJyFnVtzG0mOhf+Kgk+7Ee5ZSdbN/aa+ebzuMdvupmjORD9QUlnmmmJpSMoSZ2L++9YNwMEBkn5xuL6TdUkkgLxUFvXv0Y/1/X212o6+H1397XEzv6sOTl6+Onx1cHry6uXJ6MXol3q1fTe/r5oCfyzuq813H+r7+aoVHpdLFA5UmN8vljuUGjitFnef27tIqTfb+XJxc7m6WzbFDpvjzS+L5+r2t8X25vPo++36sXox+vHzfD2/2Vbr36v21J+ft9XqtrrVGzWP9sMP9fPo+398d3R28eK746OLF0eHh4cvLl5d/PliNGkKr5eLVfVbvVlsF/Vq9P13jQzCH58XN19W1WYz+v604VfVetMVGx0eHv+luVBzk3f1dnHT1uTH+mG3bitx8F83/31w9Ori9EX773n376v231eH3b8vu3/PDy5v6+vq4PfdZlvdbw7erG7q9UO9nm+r278cHFwulwcf2qs1dqs21fprQ3szLjYH84Pten5b3c/XXw7qTwe/Llb1dvdQfffXqjnr8vXBfHX7P/X6YNGcvHm83ixuF/P1otr8pXncn5vb3C5Wd7/ffK66Buie4vdtc8p8fStqU/DH+cNfhzY5Ozt+MfooRyetJS43N62p14148fLF6KdKjxsjn78Y/b69/et09P3xRfffq+a/Fyd9e/2t2q4XN41B//Hv0fRjU6S93LvmQTYP88aO/3nR45cvX/a4er5Zzu+Vnxxe9Pyfj3VjqeulKqeHw4VWj/fXbUPdraJ2Wy+X87XyC7nLQ7W+ab1chPPz4Tbz+0baNNaJT9Y9QdfiUXuYr6vVsvpUkvxp+njzTXvFzRdTzk6Gs5aPG6Vqs5smOOfxFp93D5+rVSzeVGVRW02OpZKb5XzzOT7Nv6p1HWm9qiLcPiUlt5/XVVL2U/24Tujia1J2s3hOYPW1Stq2ym26WsADa5Vv6mW9SixR3S+8pC2wbNNAoNU/H+fLiO/WVRPIVs2TkxNxmmrTpRpRXh0fDW0P3nd83LNLRWdn5z36IaIf44k/Wamj4fo/21OenvXol3ji64j+Gh3sjaEmtXXof+OJb+ND/GqhJyf+LZ74LqJxfPrfYqn30Tgf4om/x+f6I15rEtGVtZq05zSW+hjRLN7x79Gq101n9qXaurShnnndaD5O+TyfU07OXklOuVksbhbrm0fLohocj23S3jQ9T5J5u/zmHka9eB6vdB1L3ST5N5ZK7vwpnngX0edopEVE/xdP/BJLWQhr5k+slSSdJO09RPTPWEfLDRpCm/hcST57jOhr9LinWCrJpLvYHP8ydHFo/uUd4VhbHTpTX556uJMj8MbtYnlb7Opv66fEzq53tp5g243TzDmOJOw/tQNDzLNW56zv+LSs14uEb6rCVW4e1003fmMGPJLad2GzWXQD1yT996MWZ01z8sdFo9zX23zk0Mrdhb8hk+kl7X1aJCwZPzUDuXQ4cDu/u6uSnrvnOBSjAUfbdtW6gtg/tbHQ/G49f4CkJqdeN9OHKqmlmfd6vtlj4f1qYfylDeD1bs7Q22a5XDxsFptEauq6/Vw/urFi6Padc1vLredfk3iY3zxuE9zn8k/L6jlqhci6n9+s6+TG1+squ/FtvZ3fuIgzadG0JBrAEhrGoT1sdduYNBujPq7u5uvH++X8MblNfdcMM78kl5tjPaBd7p3P6uDi0kY9x+eDz9fr20/NMM+NC22A4vtYG394rjcY2w1eHh3qDe6bPPe4dHeQzDRPRqO3bchvNkn3tSyMzevCc9bJILqJzmZC3Hh90mpvQoNax+z9zzp/7zXWMaVNapfzbWdjo/AEOoq+XXxdgDvbKf7JbLichIY9duGkSXKSdRYUg9pVdzMvChKoaryk3c8FiuFyQ8wpGuwc/3TWEnSCzQHCTWzG0GQImIL4KSZV9PxMxWHNI7kV5RwbFXo/sFrmdnmXPYCFR8lHfUq1cX52NZtIla7m0yqYMyZK8xBXTeCUEW3wSnc/H+6yrP9Vre6STPKhEFGvs0qac+wNkn2ee1nqRtaFJr3hutrsJ1pOxyR/fK7XSa3GdHczA0WBTvOIX0iyLZhtQjcwi/muzS1vbB67Mc46eV7vgmbFEqe0Kknw/nG5XTwsd8lz+QqCk/vmkI6vGW1tF/Pl7eJTMsHalVPDO38fc9jEWSw29rrZnl6nLN0U0t2qlAapQSGnzFM/fkMXwsW3ZsCAK3A6AVrXX6oVToM0Oa6ru8XGD3wtRAsjrzcxLs50LvLYRLWbjZixCyPIdcEyNceSxmXBpf7uLXZ68kpGrt06l18F01r+vLURiiXZYgJcZnnr5fHgvdtCkqmKvWNJuCwNH/Z4pTewzZZLoVG697jUIqWuh3Ou9iOlO5fjeLx3WMI9powLquU2We7ZuiRtOfGp3pMR40hPzrt/TGrin8hMlY4zLRbI9DZP9SOc81PM440DrxtHhkfTbiRMYaRtloWO5G06yNAZhm+4V7JuoK90spxYnpC9KYT+m1KI/0pPLWZojPZ5voSeQWK8nZnQMrc2xb6x88qPmszTvtF+hUioSt3znc+lWKGhVbNG9fnMeDbcVQfOZzjqYE2WyF541BRalgnn+XiDks2pZvPbxU2WZ38q9GfrvbV559vHHpdGuzbc3OvWe+91WfCFy2KOzmcDY38dy8NJv2kjkUJvX0oUX9Lxs47H3EDArrY3FPwj2PLu3jst67u2vVd1Moqvy7n0MUoSys2lCpF8t3fOUEFHbjYvuO8q7cbh9WHoISzll2L858f2VeSfL0Zvq/Xqt/li3b5A/sfosn1RPXrx3cnhny+Goz57ONQ/p0dDTkf42h/1WcUhrBgK4+bo9FSP5BEAgXM4rk3laB//DrnM45TBZI71i0MO9YGD6L07+qM5Ojo60kMxmmOu/qBM3KUm0QCTggEmqQEm0QCTogEmiQFk6OdYl1GQXLWVeKmH0+bwlbbprBUPVZxJnZDBwwOGfQHOSF+bw/MTOXpq73YsRzt/JDcDBPca6FAIA0ARRYFyCgXjHA+ivE4QRYbyNDxEhRhRRH6iPHMWFaPHqERuozz3HZXZgVSgMFJOsST8fUQYVco4tExI40vkSbw8R5ryfRZMYk6lggUL0adyyYIhDlXwwSgYI1IYhKUgjE1lHKAqJFEqWhqqIkK8CoKgFbRLEIWv8hjDQyhhDCuiGFZOMWycY1iU1wmiGFaexrCoEMOKyAOVZx6oYvRAlcgDleceqDJ7oAoUw8ophoW/jwhjWBnHsAlpDIs8iZfnGFa+z4JJDKtUsGAhhlUuWTDEsAo+hgVjDAuDGBaEMayMY1iFJIZFS2NYRIhhQRDDgnYJohhWHmMY2wkD2XOKZi9SSJPIce3k1yVOEe7FNMxdEYh1z8ldvZj5rC8RHdfr5L1ezF3Yl2E/9iqlAy9STnDi+wLH7OAFThGkpnnClZkUbskZw4vfbIIkd3h9XxMUsogvs7cJQj7xqk8qTsPM4gRIL45jjvECJxqvJtnGFUhTjisBecdxSD6O70qc0pAXYy4ygpkIKeUhlCgLOYlzEIivc0r5B6U0+0AByD1Iye1Rypwe9ejyqJLDo5S7O5ZgZ0eNsg1KlGtAep9SzDOIOcs4Lc0xUGKS3orzC0rfMHSSW1AtG7qQV7DEHkOHnIKazyigYD4BDNkEKOYSxJxJUEvyCMhpFgEdcghQyCBAdzml7IFSzB1D42DiUERZQzmlDOOcL0R5nSDKFMrTNCEq5AhF5LfKM6dVMXqsSuSuynNfVZkdVQVKB8opFwh/HxFmAWWcAkxI41/kSbw8R77yfRZMYl6lggUL0a5yyYIhzlXwQS4YI1wYhLcgjG1lHNgqJFEtWhrSIkI8C4JgFrRLEIWx8hjDYjgMYmMUxSZQGIPAcazS64xRJJuQhrLKEMvGyBVNyHzR1OiMppE3mpC7o+nsj6ZQSJtAMa3C+4RhVBvksAYljWvVJ8ktOLJN2GvOJLZNK5mzEN2mF80Z4tsUH+DKMcIVQogrwxg3yEFuShLlKqZhrirEuTIIdGW7jFGomxBjXWyFsW6MYt0EinUQONZVep0xinUT0lhXGWLdGDmnCZlzmhqd0zRyThNy5zSdndMUinUTKNZVeJ8wjHWDHOugpLGu+iS5Bce6CXvNmcS6aSVzFmLd9KI5Q6yb4mNdOca6Qoh1ZRjrBjnWTUliXcU01lWFWFfWxvopheguY9pMLGBD9Np6+CjbAkoIxblginLFHOOD8DoSim/BaXQPIsS2EHJFwZkjihbdUBRyQsG5C4rKDiicolkwxfKA3weCcSyIo1h5GsODOgmX5vgVvMdoSeyKkhutELeiFowWYla4j9iBYrwOCKJ1IBirgjhShSdxOkhplA4axOhAoDceyC4S6okFx3548BgMTkUUncopPI1zfIryOkEUocrTEBUVYlQR+ZvyzOFUjB6nErmc8tznVGanU4FCVTnFqvD3EWG0KuNwNSGNV5En8fIcscr3WTCJWZUKFixErcolC4a4VcEHrmCMXGEQuoIwdpVx8KqQRK9oafiKCPErCAJY0C5BFMLKQwz/0NDL5qivcnck5wKSeAPk2hc43AGotCogbTFg2ljAhnYCIs5vaNJZVo+sIRS5xwXumkapPC4g8j9QtCLAtCLAhor05KfB7id25DPmT2h3QK4iwKEiQKUigPRxgenjAhseF4jY3dCVO2rj5KUezTS4fsLgABSywLCb11lGEZlHOdlIeWYoFaO1VCKTKWe7qcDGU8FbUDGZUfhVRGBQQbNoLDat8sS+3XcA3r6C2L7C2b7CU/uKmNhXJLav8GBfEYJ9RSD7Cmb7DvwqIrTvgGbRWMG+woN9fxlM2+fsX9CqgMSggJwtgcMdgIoFAanxgKndgA0mAyLWMtSOwY60PnNNpoakBoB8fjWO+dWo5ldDlkWNWRY1JlnUiNTAUP/jUC++uzgUUju9jnWqCxWo0wrUsQI1dxCmJFWrZWAHKNZj+NUqqcj/Du51ZkdSEUDSOIBc3YBD3YBK3QBpDYBp4wAbGgeIVKpHb0f9MPylHelow5AfWhjHoYVRHVoYoqYAxQYdxqQpAOkIQ1F7dHyqR/LUgGRMjQgrAhwqglQ/5HBY6gdIawFMm8NYrWOkt+j0gJJB3FtyeqB+EPc2cXpQaHj3Fp0ekB/LtehRQ6A78qHaoSRUOx5CtaM+VDuUhmqnUKh2jLJQx1wWasnOWX4X/WMXG91NtjAuSKAQITWLFioSA4cKUAyRmocTFeLIIpmCjFSKN69WJYtxFJJKAclqEptU5FstlkUslaDgJZXjmGQOaS9DdJNAgU5qFvNUJIY/FaBMQGqeFKgQ5weSKVWQSlnDq5BASKBcQmqWVqhIzDBUgJINqXneoUKcgkjmbESyT0xe3JVcidMVqSEOfh3160r9EkJ3JMGGyK0lmdAtsRweyuFUB5+/jmRhRUVYUzHm5uyK3UqK3a17/6BPvfNj+V+pegPFb1iGK4VPWALPauu+7hgeFb/uGOrtv+7wxYIF8q87vJbZAj/boHqyVbLPNgZJJpfZHUTbxeJ8B+XJHZzzQROQQA3BatYcvgw2ilegabwwK54SmonkpLF8idSgIXxTGwXjFsN3KDAkVzSuIjKr8cygoqIphYERBc2SYsFwKiQmEy0zlmi7WE82kPJgmncjXA7tjnxv2iG/HNqhpFfteOhKO+r7zw5Rf9gxWg7tmFsO7YjvDN9J8F4miOqinCqkPKuVirFqKlH9lHMlVeCaquCrq5jqjOuGjKjOYd2QeVbnbN2QJapzXDdkgevM64aMuc4uyi+LAtffq2wFr6a28EUSi/gCbBevBut4OdjIy2QpL5K95B3IZYLIRsrJOsozu6gYLaIS2UI5W0EFrr8KvuaKfZ3HrrrjWNNxrOS4UL9xWrVxrNU4qdA4qcs4VGOc16DtpfqF2zF2UIiS177joVs61aOpu+pHV3LmStqKryHsKnoaE+24kGjHhUQ73pdox+VEOy4k2nEp0Y5LiXacJ9pxIdEqhzYJI+PAs9bBkTHZcxpv9zGeOIsncrNlI+VBcl8TQQN6Tq3oRWpKL2bt6UvERvU6tawXuXm9ym3sVd/QXqPWDp/7nSTW43bf97FfVuSq0CrTwnN8LFxnVrgOe0Xxg7dBh09FwDGQklugRE6BUuYSqEeHQJXcASV2BtTYFVDzjoAKuQF9i3US7MQuUP4SKxa4Si0/Te/+Mb3CLL0CN3vh66RBlQ8LoMUVUXMrp7ZWnjW0irGVVaImVs7tqwI3rgq+ZRVTs+KXNSfeDNyghe9qSL2K9pzG232MJ87iidx82Tcog+RX1bAJWaGWZJkalOWsXblMbF4uQa3MMjc269zmrPumZ5U8gGRwBFbIH4KcuQUVuiq22LT4RB+LV5sVr8aew3J0IP3UAFzHGDmNCeQuJmSOYmp0EdPIOUxgtzCFHcIU7wrGyQnctzgnZBFu+NKXOCxfJdadJvf8mJw7S87lRk2/Vhk0Wd2B1lREjamc2lJ51pQqxpZUiRpSObejCtyMKvhWVEyNiCt6J94M3ISFFT1Sr6I9p/F2H+OJs3giN162wjdIcZI+LkzSx4VJ+njfJH1cnqSPC5P0cWmSPi5N0sf5JH1cmqTjTt0TbwZuu8I+XVKvoj2n8XYf44mzeCK3XbantZd+G5qtX479DVsMkDQWINdOwMNe1d+wdQBpwwDTNgE2NAcQaQlDtvmpO/JvDDvkNz91KHlz2PHwurCj/h1hh+idX8foRV/H3Nu9jvhNQy2SzU/DZuIW6T6igb0f4ZbZ7shvme1QsmW242HLbEf9ltkOpVtmO4W2zHaMtsx2zG2Z/TDqN0mc2JHfs9ihZFtix8OOxI76zYgdoqcGhXYodkzeUwPy+w8/DJF9ZkcS1IhcPJswcdeZxPpOCvWdpPWdxPpyK4GS1HdCmzE/QCsZaRPQhR61uad/u/JhyDFndqQb2AzhrrSeykIOtL4iMonyzC4qRuOoRBZSnptJZbaVCuQgyslLcGHtjBD5S2FhjdRJvDa7j/J9tkocSaWCrQoupXLJVsG5VPAehmuHFx6Br+FCIfkRe122UDhI8vYFXE8RmVN5Zk4VozlVInMqz82pMptTBXI95eR6wsH1FJHrGc9cT9RJvDa7nvJ9tkpcT6WCrQqup3LJVsH1VPCuh5v1LzwC18PN+uRH7HrZZn2RwvZAeYh8e2CupgYubg/MC7Cx924PzAsFw+fbA3OVHTbsEDlLBXbefTtE0jKT0j2DO3v12zbPXNsX2Gvzkpv7QvttHl3ey+T4YevMRSZgEISdM6lfh4Ao7pvpC/wxGqYZL/VIpxmGdJphyE8zjOM0w6hOMwzZNMOYTTOMyTTDiE4zFLXRfHShRzr6NuRH38Zx9G1UR9+GePRtio2+jen3CIZ0aqHIvqnojuSpAYndAbmKAA8R0FHv9h0iN+6Y2h0uONgdiM8bLer/wrVMWXvST5f6rUotac84V103GQOSxILIfcFjPGy97ilsHIbC+mGPIdpW3TH7sEfZ8HfPZSbbosVIpvzdkV896RCtW7SsdgasYwvXhebEPcNApUaAyC9B0boCE78EJK1qSOe31ohrV611rP1aGhGR6xJMsL+NLtmtpe0+4xM70i7BkO8HjKPrG1XXN8Rp3hQLCmOW0I1JFlfy5Cy380exvXexXXGz1ZDRwmYr5pSP881WLMbMHDZbMeccHTdbseCzddhsRRzydpgGMM8yeDYNYIlyeWkawDJn9TANYE75Xfg8tjRneuWU7pVnSULFmPhVouyvnLsAFbgfUMF3BoqpRxBO3YJh1zcIhhStiHoJ5dRVGI9f7ZgYOw2TYs+hGnUfyrkPUYE7EhG4NxEOXYoiyqzKuXMRoY6twt2M8n1ulHQ4KlGvozzvelTm/kcF6oSUU08knLsj4etoDe6YlFPvZDzrokRN+imRoLNSRD2W8qzbUjH2XSpRB6Y878VU5q5MBe7PVPCdmuCn2BK7BBWcLevowg5b6Q3yHba5yl3fnh22eZGkG8x32OZq6BILO2xzmbrHfIdtqmJXmS9Y5GrabRYXLPIC3IXuXbDIC4XuNF+wyFXuWp06L3lY6Ga9yp2tV9Nc6YskHa8vwN2vV0Mn7OXQFXuZOmQvcrfsVO6cSfRdtP+CEro2L3B37VXutEnNum5fJOnAqUDSjfsS/pNcVu33HlI5dOxODt27U7GT9wL3VV4NHb7/ZLPU9qHz9+q33TobCPgCPBzwamFQ4AuFoYGXeYDgVR4mODUMFpy6LtkzDBy8ysMHUtNBhCuTDSVcARxQeIGHFV5NBxe+SDLE8AV4oOHVwnDDFwqDDi+HoYeXaQDixKdSS++Kwt4QiAOTyTAaObEjvx49wXEHoGRdekIjDKC+N5i4sQQwWkaewKgBiM/wsn6O1QjfTjCnCuXfTrAYqxa+nWDOlYzfTrDgqxu+nRh4+OYg5VT7/JuDVMzsUPzmINXJIoVvDlKVbZN+c5BqZCXafp9QslC2/T6RMusUtt8nKlkm3X6faGyVZPt9opBFcG86I7JF2JvOPLNCtjedJap/3JvOAtec96Yzpjone7oLClmgtKe7IGf22LOnu1CCrFPc013Q2VaFPd0FlSznNjMHRtaKm5mDkFko3cwcNLJKspk5KGyJsJk5cKq9/pL0Zcao9iZQ7U3Iam9qrL1pVHsTuPamcO1N8bU3TrUffqn3MhKquWCqt+Cs1qLFOotCNRbM9RXOtRXu6yrU1/RqqOXwS61XWEVkulcTmF9fAAFXFQDrWgIwWxwAaBsYAcoORkC6OGCs/Y3jIzvyW0w75IfsJoydTWgvSIeSxux4aMiO+kbsULrXoFOoaTvmd3J0KLYd7E/tDrXtgKkRgPm3rMbxdxKN6nq4IZs3G7N2gztJuwHSX0pUJBOkfurWk2Hz7fErQVSHKqmrLTgAyqtapVV16wl44WiCKjFBlZlAVwmGH99oWbs2cGZHunXDkP9ZLeP4G0JG9eexDNlvYhmjnxpsWe2NbL/oCMxHOgg4ozKqywSGeKUQrmErAsZ0URDK6eRfke3GtmI43TZvaufY5xrqOrEG5L3EOHqJUfUGQ1RDUMxPjNm6kjH5SdGOTCUx9603dYkZmAY3MGouEzAxA9bEDMwSM0DzboAS4IA0MRvrFrHtyO+Sn4b0Cjzskp9iegWU7pKfuvQKTF3MkD62Ilthno7CsvJ0FNaSpyG3Ag/LD1PMrYBojWw6iovC0xGvBE8xsxqSWHh5bqTPrP2a5XRIrHZGFWupaRVRXssq9IZTTqtQ2HeSU5dVgSWV16R6puGycCctfA8+denPWO2uWse6ZwunU859RmNz5uui01FcDJ2OwgrodBSWPaeY+awRMfFZY7eJ71RP08QHyP95AePhs6QpJj5A/PcETLE/JWDM/oqAMfkDAkraBb7zl3qk6doQpuWOzny+nCX5cpbky1kpX87yfDlL8uUsy5ezLF/OYr6cJflyNsIfMZ1hvgSUvD2ZUb4E6t+CzJJ8CQrtc5hhvgTkf2x0NuTLYZQzw4SJTFsAGOV+E3DXqlH/w8ozlzOBwYdQBvVLKEP+p5VnkDX78JqNwnh0NqRNuEyVVFYTp2OFylZpZf2IFEpHI1SJEarMCDYi7UepsyF79u8nZpg9AdEfAJkN2fPoSK9rg0dgvrogYAwb9XtvZkkCxWvQ67sZZlAsp1MORTx4nFEOtaZ/9IZ6pHnHLGRRFMIsY4ZpFFCopEk00Zi5PIoF/VxrpuvnkFrCy4EgcIbMXw8ENcmV4QVBEELWjK8IgkL5M7wkYAEyafjWjXmWU7Nv3Vii7Fr61o1lzrPhWzfmlHGFY9pVxulIBU7AKqSJSdWYnVSiVKSc85EKISmpQulZOeVo4RSthn22Fp5VO+RtFTh5m7DPUEkaNynJ5SoWrBiyugpFK4b8LgIkeUWU6ZVzuhcBc74yTvwqpNlf1dgFqET9gPJCZ6A69wgqcLegAvUNwkMHIULSS4j0mNg89BcqpJ2GqrHnUIm6D+WFPkR17khUCL2JKtSl0EtFybXZW8VM476l+F4xK5D0MNmbxUwL/Uz6bjETqbfJ3i4mGvQ5SKnbQSnreVCPnQ+q1P+glHdBWIJ7IdSoI0KJ+iKQsDtCzIkWNe6UUEvTLRaIGRdVyqsocWpFLWRXFKmbQslWkYJGWcMpvsMCqXCt0G2hxj2X075hzaT/cmrShaFetnboyFDbZ+3QnYEGPRpS6tRQ4n4NNOzaEHPvhlrawWGB2MehSt0cSoWeDotwZ4ca93eoUZcHUuj1QEs6PlAf8wYK3R9qaQ+IBWIniCr1gygVukIswr0haqFDRNH3iU3Ydn9fsu8F2qN241r/YlFSHhYQBKWG5IelBEEpt9sHijoO5eGRoTRQKCvbR6CgICiluwWgmDIo5/629VDO/W3roRz8dd2hFPx13aEM/gnPoRD+Cc++1DV6br+4ez245LEdiScCSt6yXZPfAfVv2a4TPwOF3r9dO7cCNniTka9arZtRvxYKRxpNhnBc1FNxsV2C6ALK41Xw2w9GdJXs2w+R5M8Ru+sY5CuZEq/Vd5L9Hy24vV7K3y3os5hTvdRW0H7uqTvyOwM6lO0MUM/Toyd39OxK7vyRr1puZenG8fkU0UMqT5/UpRqPniJ6jifuEkRVKHuLDDmwHoqoHsrTeogK9cAPkwg9xxN3CaJ6lP3VDY9cZRznGjkxr1bI3gl/KvDnwnV2Jc71dWKsNHQKdzmlCqOUVpc7n0CfUvqcXmGXU6okSkkVbdzq6oiYK4laXksogdUE/JTj5/wiuwLmqqIW6ypd912CqI7K0/q5YYFHTxE9xxN3CaK6KI/10LHFXcaoJiakVfFjF2JPCXtOzt1ljOpjQqyQDoLuMkYVMiGtkB9kEXtK2HNy7i5jVCETQoU+jWS2r0d+Z0eHbG6vKNns0fGw2aOjfrNHh2hLR8fohw875n74sCN+l0eLmmhaVptNN5VU+Ekt2B4tdITWHfmR5CcadfQTy7vBNnagk1IlYhkj/nW8Ynwbr1BfxiuxN+6KbLqrSN63KxCT9ESmHvNIfA0U+2ooTuqiWqiQKr5Wiqlqyql+yl0llfqaxs9JU+5rXfiYNBUTC5Q/JU11b43Sh6SpSpbJPyNNNWcl/VNgeuDsEf78VwsXLi0t4tB0URgOLdJxwyL2Q4skny+SlNgeWbR3Rz5DdcjWywzFDNXxkKF66lbFFPvE1SFKXB2jxNWy2h/FZ64LD1inD1jHJwnrS6Ykz1j7/XId8pnUdydJR5J3IV/il8bD9QpfGucqteC+L43zItFse740zkuQjUtfGucy+0D86jcX9poldZLyV795gb3VKnhR6avfXCbfKnz1m6q7kiOx85W/Be0LLIdRU3+XpVul61H8OnUQ5GfYDUleOtEje85kzJiPFleYNocrxbn6qjBXX5Xn6iucYg8XjpPnVWHyvCpPnlfeRHj5QqOxwLf6RqOtcHVwuJWgXSzFl1ceLlyPcB2udiPqWi5+qEc+CGu+ZE+xOYfrxgWa2rWwP5Fvk7ZwL4XudbhhYbWhjqsKyXX4/uVVhV6nvnx4hHQNoObZfrgC37w02+9VHDAM940T19rNUv2JfLt0ltpL9B0h3JIUuDMpu+LV+DlYjo/jBkbDgyQT3dpPaulcvm0+qe01SX9wP8yIxx7t4ol8s+yvyg4SxvtwL3wbcOzRLp7I90pTQCc9uAs8xHMf8tOG1xCFVWove03OWFaf5Fvdi1SQ58hV/0kCq8l2di4CdcoL+E3urNKudpZpMz/L7qMGFv1O+E7NjbXHUnvM9C0b7TfQHuvsM80+u5SN8m2LwP+HL6HQ5Ubtm7LTw4ibB5xvc22pTu6xDwuv0dJVUsIP/pzmYyTWYZ0/p/6kS6bJRCHV3MMmJboJ7mnEfruB1/SGmSZvu3LVP05S4mF+U+Wm6ax9ETG1RyzxVWveWFf3pZwoudPTuiNd2zOU3aIVdBvHsV5M39n2lZOG49u6d2QXHtEDlN6ReZUfJez5G56Hf79yeB73ruvCI3qe0rsur/LzhB9AlOdJf7JLnsqJ+Gxe4Cf0av6c+c9eHWc3pmcefLRL0ER81CjWFTWP/Vqa13D9ySu6fuaxrZx5TpuDlMtqmae6TubwH2o3Jbo6QTixtYj2t6eEdH96ypH2t+BfeSI2JQwG6pUmzLsFz37E1B3porYhaQpAfseEcdwxYVR3TBiyfRHGbF+EMdkXYUTMbUgi4EyJze66Iz/h65C2BaD4Z6c6HqaFPcWFIMP+r1F1iP4aVcfor1G1rNZQ6o78y4UOJdtUOh62qXTUb1PpULpNpVNom0rHpEsGpLZXpHHeG/9phK+CntChAPlXQU/BoYCHkfUTOhQgWlx6cg4FzL0KekKHMuQd6mmEK29Po7Dc9hQaB3hagTpWIF9CexrFdbOnUVgsexqFFbKn2DjPLjKeY2Q8x8h4LkTGcxoZz3lkPMfIeE4i4zmJjF1ojl2s2I5HDIS5eLLlNip40p//+X+DG1I7",Rl="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",Ol="eJxtmNtu20YQhl+F4FULyMGeD7pz3AY1ChtG7NpFA18w1NomIlECSRcxgrx7SVk7+wOdG8H5OJydf2Z2d5gf9cV+t0v9VK/r+6vXsXlOlbHe28paq229qj/t++m62aXZ4J/m8PRb1z9/baZxefK63Z6eXN5dVMvTCh83u277xr/6kLrnl2XNq7TpXnczuZyabdee98/b2VzM/x4/dd/T5qab2pd6PQ2vaVVfvDRD005puE3Lu7eH1HbN9hTjx4/77/X6y5lcnUmjVzHIVVDicVX/1W/SsO36dLMfu6nb9/X6TAoBD+5euvZbn8axXtuZ36dhPJrVQqgPQoh5hev91LWLkIv94W1Ygq9+aX+tZAx2tfz64284/sblN/rqfLP/mqrbt3FKu7G67Nv9cNgPzZQ2H6rz7bb6vLgZq89pTMO/M/xfEqturJpqSM/d7GJIm2oamk3aNcO3av80O5xh3yyKmm1193ZIT02bqovTKjP+MAf++7zsZvZ3276kYyWWXB0z99S18/PbafPHQ71W4fjn/fxnFO+ZvkrT0LVzTr78qB/+nk38bHM9exgP8zr1z9U7jt6840YW5uSJKcZOCaBBnKgm5mU8MVNYyMwWFvO7Ukagkmgg6sDWQ5yFFqjzUrLEaQ3BEmiwNsMSaZS0vgWfOkPHWQowNeTUc0kumnxZvsgPxlGai6VTGUqAVCTQ6QkWnc77DKEiLktSUBJKqHIQZ86d8gCpHYoiEzMsb1ubYy8vW50DChB5ZhGqrijD0EqUIeiaEHIfCg5Kpuu0ApiToaGPSY0uaQsyr65L2oKi1yFt1PLaQ3lzfXTgXodGoJYzglndSLDMPg1sTPJpQJHJigw0QrGERqD9YhyTOgONQDUyuF1zaxuokc/BW2ztXCMrGZ9WMW1oQZHIXWNBkSCfRZEL5BMUiZw6CzVSFCfUSGZFNjIldoKDkonTKQiJIGzWmFd3BizJJ9SINoLDriOfUCOZS+zg+KGD1qGiLNMLxtJD1/ns00ON6EzyUCM6vbxhoBKaqbG3DFQCNiL1iHccBPV0DHhQH/JW8EW90dkyFKGywCJU0WkVSvSGeiSUODWFFD0HYdPQVoiRgfPMA+/nnRgiAyNYSjpWNQcNSMrtFCUH4ZIRpSCWocFCSuhCEY6hoUClc0WC52BJlCYYLQdhN+hygRRRlo5BKRRLS6oihSqh+ZzzRGG1Mo4Iz1LoP0qsxDGFzk0JE42ji0jCPejomJKCuwil4m5CiRMEUMVSzVLDUstSx1Juc0oVWMpqY295qVltmtWmWW2a1aZZbZrVplltmtWmWW2G1WZYbYbVZlhthtVmWG2G1WZYbYbVZlhtltVmWW2W1WZZbZbVZlltltVmWW2W1QYjQCh7E2aAQHeGhCFgPoNoy8KNb2wxBhmGKBxoUZXlLGsLI6AsftEDHV0wIURVbANLcTKlGGBIKPOAxCmhePCKUwFzAmpDFRQvjA9R06Hq8TONvshgKDCuRAZTXigUxjxNFfKRo3CLhnIJBMFRvMZpqpNBMlQJzGT5WFQMVQI/AikPMIhEU1aDjqJvQwmjSHB05cC9jbYwc5UtAHNLhDw41ha+lEqF4JaH3gmB61SYcqInxTDmQK8v08vjqv4zDf1N0w3Lf4A8/vwPpfK11w==";var El={Courier:wl,"Courier-Bold":bl,"Courier-Oblique":xl,"Courier-BoldOblique":ml,Helvetica:Cl,"Helvetica-Bold":Sl,"Helvetica-Oblique":kl,"Helvetica-BoldOblique":Fl,"Times-Roman":Dl,"Times-Bold":Tl,"Times-Italic":Al,"Times-BoldItalic":Pl,Symbol:Rl,ZapfDingbats:Ol},_r;(function(e){e.Courier="Courier",e.CourierBold="Courier-Bold",e.CourierOblique="Courier-Oblique",e.CourierBoldOblique="Courier-BoldOblique",e.Helvetica="Helvetica",e.HelveticaBold="Helvetica-Bold",e.HelveticaOblique="Helvetica-Oblique",e.HelveticaBoldOblique="Helvetica-BoldOblique",e.TimesRoman="Times-Roman",e.TimesRomanBold="Times-Bold",e.TimesRomanItalic="Times-Italic",e.TimesRomanBoldItalic="Times-BoldItalic",e.Symbol="Symbol",e.ZapfDingbats="ZapfDingbats"})(_r||(_r={}));var No={},Bl=function(){function e(){var t=this;this.getWidthOfGlyph=function(r){return t.CharWidths[r]},this.getXAxisKerningForPair=function(r,n){return(t.KernPairXAmounts[r]||{})[n]}}return e.load=function(t){var r=No[t];if(r)return r;var n=au(El[t]),i=Object.assign(new e,JSON.parse(n));return i.CharWidths=i.CharMetrics.reduce(function(a,o){return a[o.N]=o.WX,a},{}),i.KernPairXAmounts=i.KernPairs.reduce(function(a,o){var s=o[0],f=o[1],u=o[2];return a[s]||(a[s]={}),a[s][f]=u,a},{}),No[t]=i,i},e}();const Nl="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";var zl=au(Nl),Wi=JSON.parse(zl),qi=function(){function e(t,r){var n=this;this.canEncodeUnicodeCodePoint=function(i){return i in n.unicodeMappings},this.encodeUnicodeCodePoint=function(i){var a=n.unicodeMappings[i];if(!a){var o=String.fromCharCode(i),s="0x"+yl(i.toString(16),4,"0"),f=n.name+' cannot encode "'+o+'" ('+s+")";throw new Error(f)}return{code:a[0],name:a[1]}},this.name=t,this.supportedCodePoints=Object.keys(r).map(Number).sort(function(i,a){return i-a}),this.unicodeMappings=r}return e}(),Cn={Symbol:new qi("Symbol",Wi.symbol),ZapfDingbats:new qi("ZapfDingbats",Wi.zapfdingbats),WinAnsi:new qi("WinAnsi",Wi.win1252)},li=function(e){return Object.keys(e).map(function(t){return e[t]})},jl=li(_r),zo=function(e){return jl.includes(e)},Tn=function(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height},ye=function(e){return"`"+e+"`"},Ml=function(e){return"'"+e+"'"},jo=function(e){var t=typeof e;return t==="string"?Ml(e):t==="undefined"?ye(e):e},Il=function(e,t,r){for(var n=new Array(r.length),i=0,a=r.length;i<a;i++){var o=r[i];n[i]=jo(o)}var s=n.join(" or ");return ye(t)+" must be one of "+s+", but was actually "+jo(e)},Dt=function(e,t,r){Array.isArray(r)||(r=li(r));for(var n=0,i=r.length;n<i;n++)if(e===r[n])return;throw new TypeError(Il(e,t,r))},We=function(e,t,r){Array.isArray(r)||(r=li(r)),Dt(e,t,r.concat(void 0))},Ul=function(e,t,r){Array.isArray(r)||(r=li(r));for(var n=0,i=e.length;n<i;n++)Dt(e[n],t,r)},Vl=function(e){return e===null?"null":e===void 0?"undefined":typeof e=="string"?"string":isNaN(e)?"NaN":typeof e=="number"?"number":typeof e=="boolean"?"boolean":typeof e=="symbol"?"symbol":typeof e=="bigint"?"bigint":e.constructor&&e.constructor.name?e.constructor.name:e.name?e.name:e.constructor?String(e.constructor):String(e)},Wl=function(e,t){return t==="null"?e===null:t==="undefined"?e===void 0:t==="string"?typeof e=="string":t==="number"?typeof e=="number"&&!isNaN(e):t==="boolean"?typeof e=="boolean":t==="symbol"?typeof e=="symbol":t==="bigint"?typeof e=="bigint":t===Date?e instanceof Date:t===Array?e instanceof Array:t===Uint8Array?e instanceof Uint8Array:t===ArrayBuffer?e instanceof ArrayBuffer:t===Function?e instanceof Function:e instanceof t[0]},ql=function(e,t,r){for(var n=new Array(r.length),i=0,a=r.length;i<a;i++){var o=r[i];o==="null"&&(n[i]=ye("null")),o==="undefined"&&(n[i]=ye("undefined")),o==="string"?n[i]=ye("string"):o==="number"?n[i]=ye("number"):o==="boolean"?n[i]=ye("boolean"):o==="symbol"?n[i]=ye("symbol"):o==="bigint"?n[i]=ye("bigint"):o===Array?n[i]=ye("Array"):o===Uint8Array?n[i]=ye("Uint8Array"):o===ArrayBuffer?n[i]=ye("ArrayBuffer"):n[i]=ye(o[1])}var s=n.join(" or ");return ye(t)+" must be of type "+s+", but was actually of type "+ye(Vl(e))},x=function(e,t,r){for(var n=0,i=r.length;n<i;n++)if(Wl(e,r[n]))return;throw new TypeError(ql(e,t,r))},O=function(e,t,r){x(e,t,r.concat("undefined"))},ou=function(e,t,r){for(var n=0,i=e.length;n<i;n++)x(e[n],t,r)},je=function(e,t,r,n){if(x(e,t,["number"]),x(r,"min",["number"]),x(n,"max",["number"]),n=Math.max(r,n),e<r||e>n)throw new Error(ye(t)+" must be at least "+r+" and at most "+n+", but was actually "+e)},_e=function(e,t,r,n){x(e,t,["number","undefined"]),typeof e=="number"&&je(e,t,r,n)},su=function(e,t,r){if(x(e,t,["number"]),e%r!==0)throw new Error(ye(t)+" must be a multiple of "+r+", but was actually "+e)},Kl=function(e,t){if(!Number.isInteger(e))throw new Error(ye(t)+" must be an integer, but was actually "+e)},hi=function(e,t){if(![1,0].includes(Math.sign(e)))throw new Error(ye(t)+" must be a positive number or 0, but was actually "+e)},X=new Uint16Array(256);for(var Pn=0;Pn<256;Pn++)X[Pn]=Pn;X[22]=G("");X[24]=G("˘");X[25]=G("ˇ");X[26]=G("ˆ");X[27]=G("˙");X[28]=G("˝");X[29]=G("˛");X[30]=G("˚");X[31]=G("˜");X[127]=G("�");X[128]=G("•");X[129]=G("†");X[130]=G("‡");X[131]=G("…");X[132]=G("—");X[133]=G("–");X[134]=G("ƒ");X[135]=G("⁄");X[136]=G("‹");X[137]=G("›");X[138]=G("−");X[139]=G("‰");X[140]=G("„");X[141]=G("“");X[142]=G("”");X[143]=G("‘");X[144]=G("’");X[145]=G("‚");X[146]=G("™");X[147]=G("ﬁ");X[148]=G("ﬂ");X[149]=G("Ł");X[150]=G("Œ");X[151]=G("Š");X[152]=G("Ÿ");X[153]=G("Ž");X[154]=G("ı");X[155]=G("ł");X[156]=G("œ");X[157]=G("š");X[158]=G("ž");X[159]=G("�");X[160]=G("€");X[173]=G("�");var uu=function(e){for(var t=new Array(e.length),r=0,n=e.length;r<n;r++)t[r]=X[e[r]];return String.fromCodePoint.apply(String,t)},pt=function(){function e(t){this.populate=t,this.value=void 0}return e.prototype.getValue=function(){return this.value},e.prototype.access=function(){return this.value||(this.value=this.populate()),this.value},e.prototype.invalidate=function(){this.value=void 0},e.populatedBy=function(t){return new e(t)},e}(),Le=function(e){E(t,e);function t(r,n){var i=this,a="Method "+r+"."+n+"() not implemented";return i=e.call(this,a)||this,i}return t}(Error),xa=function(e){E(t,e);function t(r){var n=this,i="Cannot construct "+r+" - it has a private constructor";return n=e.call(this,i)||this,n}return t}(Error),Zn=function(e){E(t,e);function t(r,n){var i=this,a=function(f){var u,c;return(u=f==null?void 0:f.name)!==null&&u!==void 0?u:(c=f==null?void 0:f.constructor)===null||c===void 0?void 0:c.name},o=Array.isArray(r)?r.map(a):[a(r)],s="Expected instance of "+o.join(" or ")+", "+("but got instance of "+(n&&a(n)));return i=e.call(this,s)||this,i}return t}(Error),Ll=function(e){E(t,e);function t(r){var n=this,i=r+" stream encoding not supported";return n=e.call(this,i)||this,n}return t}(Error),wa=function(e){E(t,e);function t(r,n){var i=this,a="Cannot call "+r+"."+n+"() more than once";return i=e.call(this,a)||this,i}return t}(Error);(function(e){E(t,e);function t(r){var n=this,i="Missing catalog (ref="+r+")";return n=e.call(this,i)||this,n}return t})(Error);var Gl=function(e){E(t,e);function t(){var r=this,n="Can't embed page with missing Contents";return r=e.call(this,n)||this,r}return t}(Error),Hl=function(e){E(t,e);function t(r){var n,i,a,o=this,s=(a=(i=(n=r==null?void 0:r.contructor)===null||n===void 0?void 0:n.name)!==null&&i!==void 0?i:r==null?void 0:r.name)!==null&&a!==void 0?a:r,f="Unrecognized stream type: "+s;return o=e.call(this,f)||this,o}return t}(Error),Xl=function(e){E(t,e);function t(){var r=this,n="Found mismatched contexts while embedding pages. All pages in the array passed to `PDFDocument.embedPages()` must be from the same document.";return r=e.call(this,n)||this,r}return t}(Error),Zl=function(e){E(t,e);function t(r){var n=this,i="Attempted to convert PDFArray with "+r+" elements to rectangle, but must have exactly 4 elements.";return n=e.call(this,i)||this,n}return t}(Error),fu=function(e){E(t,e);function t(r){var n=this,i='Attempted to convert "'+r+'" to a date, but it does not match the PDF date string format.';return n=e.call(this,i)||this,n}return t}(Error),Mo=function(e){E(t,e);function t(r,n){var i=this,a="Invalid targetIndex specified: targetIndex="+r+" must be less than Count="+n;return i=e.call(this,a)||this,i}return t}(Error),Io=function(e){E(t,e);function t(r,n){var i=this,a="Failed to "+n+" at targetIndex="+r+" due to corrupt page tree: It is likely that one or more 'Count' entries are invalid";return i=e.call(this,a)||this,i}return t}(Error),Yn=function(e){E(t,e);function t(r,n,i){var a=this,o="index should be at least "+n+" and at most "+i+", but was actually "+r;return a=e.call(this,o)||this,a}return t}(Error),Sa=function(e){E(t,e);function t(){var r=this,n="Attempted to set invalid field value";return r=e.call(this,n)||this,r}return t}(Error),Yl=function(e){E(t,e);function t(){var r=this,n="Attempted to select multiple values for single-select field";return r=e.call(this,n)||this,r}return t}(Error),Jl=function(e){E(t,e);function t(r){var n=this,i="No /DA (default appearance) entry found for field: "+r;return n=e.call(this,i)||this,n}return t}(Error),Ql=function(e){E(t,e);function t(r){var n=this,i="No Tf operator found for DA of field: "+r;return n=e.call(this,i)||this,n}return t}(Error),Uo=function(e){E(t,e);function t(r,n){var i=this,a="Failed to parse number "+("(line:"+r.line+" col:"+r.column+" offset="+r.offset+'): "'+n+'"');return i=e.call(this,a)||this,i}return t}(Error),jt=function(e){E(t,e);function t(r,n){var i=this,a="Failed to parse PDF document "+("(line:"+r.line+" col:"+r.column+" offset="+r.offset+"): "+n);return i=e.call(this,a)||this,i}return t}(Error),_l=function(e){E(t,e);function t(r,n,i){var a=this,o="Expected next byte to be "+n+" but it was actually "+i;return a=e.call(this,r,o)||this,a}return t}(jt),$l=function(e){E(t,e);function t(r,n){var i=this,a="Failed to parse PDF object starting with the following byte: "+n;return i=e.call(this,r,a)||this,i}return t}(jt),eh=function(e){E(t,e);function t(r){var n=this,i="Failed to parse invalid PDF object";return n=e.call(this,r,i)||this,n}return t}(jt),th=function(e){E(t,e);function t(r){var n=this,i="Failed to parse PDF stream";return n=e.call(this,r,i)||this,n}return t}(jt),rh=function(e){E(t,e);function t(r){var n=this,i="Failed to parse PDF literal string due to unbalanced parenthesis";return n=e.call(this,r,i)||this,n}return t}(jt),nh=function(e){E(t,e);function t(r){var n=this,i="Parser stalled";return n=e.call(this,r,i)||this,n}return t}(jt),ih=function(e){E(t,e);function t(r){var n=this,i="No PDF header found";return n=e.call(this,r,i)||this,n}return t}(jt),ah=function(e){E(t,e);function t(r,n){var i=this,a="Did not find expected keyword '"+ls(n)+"'";return i=e.call(this,r,a)||this,i}return t}(jt),ra;(function(e){e[e.Null=0]="Null",e[e.Backspace=8]="Backspace",e[e.Tab=9]="Tab",e[e.Newline=10]="Newline",e[e.FormFeed=12]="FormFeed",e[e.CarriageReturn=13]="CarriageReturn",e[e.Space=32]="Space",e[e.ExclamationPoint=33]="ExclamationPoint",e[e.Hash=35]="Hash",e[e.Percent=37]="Percent",e[e.LeftParen=40]="LeftParen",e[e.RightParen=41]="RightParen",e[e.Plus=43]="Plus",e[e.Minus=45]="Minus",e[e.Dash=45]="Dash",e[e.Period=46]="Period",e[e.ForwardSlash=47]="ForwardSlash",e[e.Zero=48]="Zero",e[e.One=49]="One",e[e.Two=50]="Two",e[e.Three=51]="Three",e[e.Four=52]="Four",e[e.Five=53]="Five",e[e.Six=54]="Six",e[e.Seven=55]="Seven",e[e.Eight=56]="Eight",e[e.Nine=57]="Nine",e[e.LessThan=60]="LessThan",e[e.GreaterThan=62]="GreaterThan",e[e.A=65]="A",e[e.D=68]="D",e[e.E=69]="E",e[e.F=70]="F",e[e.O=79]="O",e[e.P=80]="P",e[e.R=82]="R",e[e.LeftSquareBracket=91]="LeftSquareBracket",e[e.BackSlash=92]="BackSlash",e[e.RightSquareBracket=93]="RightSquareBracket",e[e.a=97]="a",e[e.b=98]="b",e[e.d=100]="d",e[e.e=101]="e",e[e.f=102]="f",e[e.i=105]="i",e[e.j=106]="j",e[e.l=108]="l",e[e.m=109]="m",e[e.n=110]="n",e[e.o=111]="o",e[e.r=114]="r",e[e.s=115]="s",e[e.t=116]="t",e[e.u=117]="u",e[e.x=120]="x",e[e.LeftCurly=123]="LeftCurly",e[e.RightCurly=125]="RightCurly",e[e.Tilde=126]="Tilde"})(ra||(ra={}));const y=ra;var di=function(){function e(t,r){this.major=String(t),this.minor=String(r)}return e.prototype.toString=function(){var t=vt(129);return"%PDF-"+this.major+"."+this.minor+`
%`+t+t+t+t},e.prototype.sizeInBytes=function(){return 12+this.major.length+this.minor.length},e.prototype.copyBytesInto=function(t,r){var n=r;return t[r++]=y.Percent,t[r++]=y.P,t[r++]=y.D,t[r++]=y.F,t[r++]=y.Dash,r+=ke(this.major,t,r),t[r++]=y.Period,r+=ke(this.minor,t,r),t[r++]=y.Newline,t[r++]=y.Percent,t[r++]=129,t[r++]=129,t[r++]=129,t[r++]=129,r-n},e.forVersion=function(t,r){return new e(t,r)},e}(),De=function(){function e(){}return e.prototype.clone=function(t){throw new Le(this.constructor.name,"clone")},e.prototype.toString=function(){throw new Le(this.constructor.name,"toString")},e.prototype.sizeInBytes=function(){throw new Le(this.constructor.name,"sizeInBytes")},e.prototype.copyBytesInto=function(t,r){throw new Le(this.constructor.name,"copyBytesInto")},e}(),q=function(e){E(t,e);function t(r){var n=e.call(this)||this;return n.numberValue=r,n.stringValue=Sf(r),n}return t.prototype.asNumber=function(){return this.numberValue},t.prototype.value=function(){return this.numberValue},t.prototype.clone=function(){return t.of(this.numberValue)},t.prototype.toString=function(){return this.stringValue},t.prototype.sizeInBytes=function(){return this.stringValue.length},t.prototype.copyBytesInto=function(r,n){return n+=ke(this.stringValue,r,n),this.stringValue.length},t.of=function(r){return new t(r)},t}(De),ae=function(e){E(t,e);function t(r){var n=e.call(this)||this;return n.array=[],n.context=r,n}return t.prototype.size=function(){return this.array.length},t.prototype.push=function(r){this.array.push(r)},t.prototype.insert=function(r,n){this.array.splice(r,0,n)},t.prototype.indexOf=function(r){var n=this.array.indexOf(r);return n===-1?void 0:n},t.prototype.remove=function(r){this.array.splice(r,1)},t.prototype.set=function(r,n){this.array[r]=n},t.prototype.get=function(r){return this.array[r]},t.prototype.lookupMaybe=function(r){for(var n,i=[],a=1;a<arguments.length;a++)i[a-1]=arguments[a];return(n=this.context).lookupMaybe.apply(n,re([this.get(r)],i))},t.prototype.lookup=function(r){for(var n,i=[],a=1;a<arguments.length;a++)i[a-1]=arguments[a];return(n=this.context).lookup.apply(n,re([this.get(r)],i))},t.prototype.asRectangle=function(){if(this.size()!==4)throw new Zl(this.size());var r=this.lookup(0,q).asNumber(),n=this.lookup(1,q).asNumber(),i=this.lookup(2,q).asNumber(),a=this.lookup(3,q).asNumber(),o=r,s=n,f=i-r,u=a-n;return{x:o,y:s,width:f,height:u}},t.prototype.asArray=function(){return this.array.slice()},t.prototype.clone=function(r){for(var n=t.withContext(r||this.context),i=0,a=this.size();i<a;i++)n.push(this.array[i]);return n},t.prototype.toString=function(){for(var r="[ ",n=0,i=this.size();n<i;n++)r+=this.get(n).toString(),r+=" ";return r+="]",r},t.prototype.sizeInBytes=function(){for(var r=3,n=0,i=this.size();n<i;n++)r+=this.get(n).sizeInBytes()+1;return r},t.prototype.copyBytesInto=function(r,n){var i=n;r[n++]=y.LeftSquareBracket,r[n++]=y.Space;for(var a=0,o=this.size();a<o;a++)n+=this.get(a).copyBytesInto(r,n),r[n++]=y.Space;return r[n++]=y.RightSquareBracket,n-i},t.prototype.scalePDFNumbers=function(r,n){for(var i=0,a=this.size();i<a;i++){var o=this.lookup(i);if(o instanceof q){var s=i%2===0?r:n;this.set(i,q.of(o.asNumber()*s))}}},t.withContext=function(r){return new t(r)},t}(De),Ki={},$r=function(e){E(t,e);function t(r,n){var i=this;if(r!==Ki)throw new xa("PDFBool");return i=e.call(this)||this,i.value=n,i}return t.prototype.asBoolean=function(){return this.value},t.prototype.clone=function(){return this},t.prototype.toString=function(){return String(this.value)},t.prototype.sizeInBytes=function(){return this.value?4:5},t.prototype.copyBytesInto=function(r,n){return this.value?(r[n++]=y.t,r[n++]=y.r,r[n++]=y.u,r[n++]=y.e,4):(r[n++]=y.f,r[n++]=y.a,r[n++]=y.l,r[n++]=y.s,r[n++]=y.e,5)},t.True=new t(Ki,!0),t.False=new t(Ki,!1),t}(De),Qe=new Uint8Array(256);Qe[y.LeftParen]=1;Qe[y.RightParen]=1;Qe[y.LessThan]=1;Qe[y.GreaterThan]=1;Qe[y.LeftSquareBracket]=1;Qe[y.RightSquareBracket]=1;Qe[y.LeftCurly]=1;Qe[y.RightCurly]=1;Qe[y.ForwardSlash]=1;Qe[y.Percent]=1;var bt=new Uint8Array(256);bt[y.Null]=1;bt[y.Tab]=1;bt[y.Newline]=1;bt[y.FormFeed]=1;bt[y.CarriageReturn]=1;bt[y.Space]=1;var Fa=new Uint8Array(256);for(var Cr=0,oh=256;Cr<oh;Cr++)Fa[Cr]=bt[Cr]||Qe[Cr]?1:0;Fa[y.Hash]=1;var sh=function(e){return e.replace(/#([\dABCDEF]{2})/g,function(t,r){return tf(r)})},uh=function(e){return e>=y.ExclamationPoint&&e<=y.Tilde&&!Fa[e]},Vo={},Wo=new Map,p=function(e){E(t,e);function t(r,n){var i=this;if(r!==Vo)throw new xa("PDFName");i=e.call(this)||this;for(var a="/",o=0,s=n.length;o<s;o++){var f=n[o],u=G(f);a+=uh(u)?f:"#"+oi(u)}return i.encodedName=a,i}return t.prototype.asBytes=function(){for(var r=[],n="",i=!1,a=function(l){l!==void 0&&r.push(l),i=!1},o=1,s=this.encodedName.length;o<s;o++){var f=this.encodedName[o],u=G(f),c=this.encodedName[o+1];i?u>=y.Zero&&u<=y.Nine||u>=y.a&&u<=y.f||u>=y.A&&u<=y.F?(n+=f,(n.length===2||!(c>="0"&&c<="9"||c>="a"&&c<="f"||c>="A"&&c<="F"))&&(a(parseInt(n,16)),n="")):a(u):u===y.Hash?i=!0:a(u)}return new Uint8Array(r)},t.prototype.decodeText=function(){var r=this.asBytes();return String.fromCharCode.apply(String,Array.from(r))},t.prototype.asString=function(){return this.encodedName},t.prototype.value=function(){return this.encodedName},t.prototype.clone=function(){return this},t.prototype.toString=function(){return this.encodedName},t.prototype.sizeInBytes=function(){return this.encodedName.length},t.prototype.copyBytesInto=function(r,n){return n+=ke(this.encodedName,r,n),this.encodedName.length},t.of=function(r){var n=sh(r),i=Wo.get(n);return i||(i=new t(Vo,n),Wo.set(n,i)),i},t.Length=t.of("Length"),t.FlateDecode=t.of("FlateDecode"),t.Resources=t.of("Resources"),t.Font=t.of("Font"),t.XObject=t.of("XObject"),t.ExtGState=t.of("ExtGState"),t.Contents=t.of("Contents"),t.Type=t.of("Type"),t.Parent=t.of("Parent"),t.MediaBox=t.of("MediaBox"),t.Page=t.of("Page"),t.Annots=t.of("Annots"),t.TrimBox=t.of("TrimBox"),t.ArtBox=t.of("ArtBox"),t.BleedBox=t.of("BleedBox"),t.CropBox=t.of("CropBox"),t.Rotate=t.of("Rotate"),t.Title=t.of("Title"),t.Author=t.of("Author"),t.Subject=t.of("Subject"),t.Creator=t.of("Creator"),t.Keywords=t.of("Keywords"),t.Producer=t.of("Producer"),t.CreationDate=t.of("CreationDate"),t.ModDate=t.of("ModDate"),t}(De),fh=function(e){E(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.asNull=function(){return null},t.prototype.clone=function(){return this},t.prototype.toString=function(){return"null"},t.prototype.sizeInBytes=function(){return 4},t.prototype.copyBytesInto=function(r,n){return r[n++]=y.n,r[n++]=y.u,r[n++]=y.l,r[n++]=y.l,4},t}(De);const Be=new fh;var Z=function(e){E(t,e);function t(r,n){var i=e.call(this)||this;return i.dict=r,i.context=n,i}return t.prototype.keys=function(){return Array.from(this.dict.keys())},t.prototype.values=function(){return Array.from(this.dict.values())},t.prototype.entries=function(){return Array.from(this.dict.entries())},t.prototype.set=function(r,n){this.dict.set(r,n)},t.prototype.get=function(r,n){n===void 0&&(n=!1);var i=this.dict.get(r);if(!(i===Be&&!n))return i},t.prototype.has=function(r){var n=this.dict.get(r);return n!==void 0&&n!==Be},t.prototype.lookupMaybe=function(r){for(var n,i=[],a=1;a<arguments.length;a++)i[a-1]=arguments[a];var o=i.includes(Be),s=(n=this.context).lookupMaybe.apply(n,re([this.get(r,o)],i));if(!(s===Be&&!o))return s},t.prototype.lookup=function(r){for(var n,i=[],a=1;a<arguments.length;a++)i[a-1]=arguments[a];var o=i.includes(Be),s=(n=this.context).lookup.apply(n,re([this.get(r,o)],i));if(!(s===Be&&!o))return s},t.prototype.delete=function(r){return this.dict.delete(r)},t.prototype.asMap=function(){return new Map(this.dict)},t.prototype.uniqueKey=function(r){r===void 0&&(r="");for(var n=this.keys(),i=p.of(this.context.addRandomSuffix(r,10));n.includes(i);)i=p.of(this.context.addRandomSuffix(r,10));return i},t.prototype.clone=function(r){for(var n=t.withContext(r||this.context),i=this.entries(),a=0,o=i.length;a<o;a++){var s=i[a],f=s[0],u=s[1];n.set(f,u)}return n},t.prototype.toString=function(){for(var r=`<<
`,n=this.entries(),i=0,a=n.length;i<a;i++){var o=n[i],s=o[0],f=o[1];r+=s.toString()+" "+f.toString()+`
`}return r+=">>",r},t.prototype.sizeInBytes=function(){for(var r=5,n=this.entries(),i=0,a=n.length;i<a;i++){var o=n[i],s=o[0],f=o[1];r+=s.sizeInBytes()+f.sizeInBytes()+2}return r},t.prototype.copyBytesInto=function(r,n){var i=n;r[n++]=y.LessThan,r[n++]=y.LessThan,r[n++]=y.Newline;for(var a=this.entries(),o=0,s=a.length;o<s;o++){var f=a[o],u=f[0],c=f[1];n+=u.copyBytesInto(r,n),r[n++]=y.Space,n+=c.copyBytesInto(r,n),r[n++]=y.Newline}return r[n++]=y.GreaterThan,r[n++]=y.GreaterThan,n-i},t.withContext=function(r){return new t(new Map,r)},t.fromMapWithContext=function(r,n){return new t(r,n)},t}(De),Ie=function(e){E(t,e);function t(r){var n=e.call(this)||this;return n.dict=r,n}return t.prototype.clone=function(r){throw new Le(this.constructor.name,"clone")},t.prototype.getContentsString=function(){throw new Le(this.constructor.name,"getContentsString")},t.prototype.getContents=function(){throw new Le(this.constructor.name,"getContents")},t.prototype.getContentsSize=function(){throw new Le(this.constructor.name,"getContentsSize")},t.prototype.updateDict=function(){var r=this.getContentsSize();this.dict.set(p.Length,q.of(r))},t.prototype.sizeInBytes=function(){return this.updateDict(),this.dict.sizeInBytes()+this.getContentsSize()+18},t.prototype.toString=function(){this.updateDict();var r=this.dict.toString();return r+=`
stream
`,r+=this.getContentsString(),r+=`
endstream`,r},t.prototype.copyBytesInto=function(r,n){this.updateDict();var i=n;n+=this.dict.copyBytesInto(r,n),r[n++]=y.Newline,r[n++]=y.s,r[n++]=y.t,r[n++]=y.r,r[n++]=y.e,r[n++]=y.a,r[n++]=y.m,r[n++]=y.Newline;for(var a=this.getContents(),o=0,s=a.length;o<s;o++)r[n++]=a[o];return r[n++]=y.Newline,r[n++]=y.e,r[n++]=y.n,r[n++]=y.d,r[n++]=y.s,r[n++]=y.t,r[n++]=y.r,r[n++]=y.e,r[n++]=y.a,r[n++]=y.m,n-i},t}(De),en=function(e){E(t,e);function t(r,n){var i=e.call(this,r)||this;return i.contents=n,i}return t.prototype.asUint8Array=function(){return this.contents.slice()},t.prototype.clone=function(r){return t.of(this.dict.clone(r),this.contents.slice())},t.prototype.getContentsString=function(){return ls(this.contents)},t.prototype.getContents=function(){return this.contents},t.prototype.getContentsSize=function(){return this.contents.length},t.of=function(r,n){return new t(r,n)},t}(Ie),qo={},Ko=new Map,fe=function(e){E(t,e);function t(r,n,i){var a=this;if(r!==qo)throw new xa("PDFRef");return a=e.call(this)||this,a.objectNumber=n,a.generationNumber=i,a.tag=n+" "+i+" R",a}return t.prototype.clone=function(){return this},t.prototype.toString=function(){return this.tag},t.prototype.sizeInBytes=function(){return this.tag.length},t.prototype.copyBytesInto=function(r,n){return n+=ke(this.tag,r,n),this.tag.length},t.of=function(r,n){n===void 0&&(n=0);var i=r+" "+n+" R",a=Ko.get(i);return a||(a=new t(qo,r,n),Ko.set(i,a)),a},t}(De),ee=function(){function e(t,r){this.name=t,this.args=r||[]}return e.prototype.clone=function(t){for(var r=new Array(this.args.length),n=0,i=r.length;n<i;n++){var a=this.args[n];r[n]=a instanceof De?a.clone(t):a}return e.of(this.name,r)},e.prototype.toString=function(){for(var t="",r=0,n=this.args.length;r<n;r++)t+=String(this.args[r])+" ";return t+=this.name,t},e.prototype.sizeInBytes=function(){for(var t=0,r=0,n=this.args.length;r<n;r++){var i=this.args[r];t+=(i instanceof De?i.sizeInBytes():i.length)+1}return t+=this.name.length,t},e.prototype.copyBytesInto=function(t,r){for(var n=r,i=0,a=this.args.length;i<a;i++){var o=this.args[i];o instanceof De?r+=o.copyBytesInto(t,r):r+=ke(o,t,r),t[r++]=y.Space}return r+=ke(this.name,t,r),r-n},e.of=function(t,r){return new e(t,r)},e}(),na;(function(e){e.NonStrokingColor="sc",e.NonStrokingColorN="scn",e.NonStrokingColorRgb="rg",e.NonStrokingColorGray="g",e.NonStrokingColorCmyk="k",e.NonStrokingColorspace="cs",e.StrokingColor="SC",e.StrokingColorN="SCN",e.StrokingColorRgb="RG",e.StrokingColorGray="G",e.StrokingColorCmyk="K",e.StrokingColorspace="CS",e.BeginMarkedContentSequence="BDC",e.BeginMarkedContent="BMC",e.EndMarkedContent="EMC",e.MarkedContentPointWithProps="DP",e.MarkedContentPoint="MP",e.DrawObject="Do",e.ConcatTransformationMatrix="cm",e.PopGraphicsState="Q",e.PushGraphicsState="q",e.SetFlatness="i",e.SetGraphicsStateParams="gs",e.SetLineCapStyle="J",e.SetLineDashPattern="d",e.SetLineJoinStyle="j",e.SetLineMiterLimit="M",e.SetLineWidth="w",e.SetTextMatrix="Tm",e.SetRenderingIntent="ri",e.AppendRectangle="re",e.BeginInlineImage="BI",e.BeginInlineImageData="ID",e.EndInlineImage="EI",e.ClipEvenOdd="W*",e.ClipNonZero="W",e.CloseAndStroke="s",e.CloseFillEvenOddAndStroke="b*",e.CloseFillNonZeroAndStroke="b",e.ClosePath="h",e.AppendBezierCurve="c",e.CurveToReplicateFinalPoint="y",e.CurveToReplicateInitialPoint="v",e.EndPath="n",e.FillEvenOddAndStroke="B*",e.FillEvenOdd="f*",e.FillNonZeroAndStroke="B",e.FillNonZero="f",e.LegacyFillNonZero="F",e.LineTo="l",e.MoveTo="m",e.ShadingFill="sh",e.StrokePath="S",e.BeginText="BT",e.EndText="ET",e.MoveText="Td",e.MoveTextSetLeading="TD",e.NextLine="T*",e.SetCharacterSpacing="Tc",e.SetFontAndSize="Tf",e.SetTextHorizontalScaling="Tz",e.SetTextLineHeight="TL",e.SetTextRenderingMode="Tr",e.SetTextRise="Ts",e.SetWordSpacing="Tw",e.ShowText="Tj",e.ShowTextAdjusted="TJ",e.ShowTextLine="'",e.ShowTextLineAndSpace='"',e.Type3D0="d0",e.Type3D1="d1",e.BeginCompatibilitySection="BX",e.EndCompatibilitySection="EX"})(na||(na={}));const ne=na;var ka=function(e){E(t,e);function t(r,n){var i=e.call(this,r)||this;return i.computeContents=function(){var a=i.getUnencodedContents();return i.encode?ci.deflate(a):a},i.encode=n,n&&r.set(p.of("Filter"),p.of("FlateDecode")),i.contentsCache=pt.populatedBy(i.computeContents),i}return t.prototype.getContents=function(){return this.contentsCache.access()},t.prototype.getContentsSize=function(){return this.contentsCache.access().length},t.prototype.getUnencodedContents=function(){throw new Le(this.constructor.name,"getUnencodedContents")},t}(Ie),qr=function(e){E(t,e);function t(r,n,i){i===void 0&&(i=!0);var a=e.call(this,r,i)||this;return a.operators=n,a}return t.prototype.push=function(){for(var r,n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];(r=this.operators).push.apply(r,n)},t.prototype.clone=function(r){for(var n=new Array(this.operators.length),i=0,a=this.operators.length;i<a;i++)n[i]=this.operators[i].clone(r);var o=this,s=o.dict,f=o.encode;return t.of(s.clone(r),n,f)},t.prototype.getContentsString=function(){for(var r="",n=0,i=this.operators.length;n<i;n++)r+=this.operators[n]+`
`;return r},t.prototype.getUnencodedContents=function(){for(var r=new Uint8Array(this.getUnencodedContentsSize()),n=0,i=0,a=this.operators.length;i<a;i++)n+=this.operators[i].copyBytesInto(r,n),r[n++]=y.Newline;return r},t.prototype.getUnencodedContentsSize=function(){for(var r=0,n=0,i=this.operators.length;n<i;n++)r+=this.operators[n].sizeInBytes()+1;return r},t.of=function(r,n,i){return i===void 0&&(i=!0),new t(r,n,i)},t}(ka),ch=function(){function e(t){this.seed=t}return e.prototype.nextInt=function(){var t=Math.sin(this.seed++)*1e4;return t-Math.floor(t)},e.withSeed=function(t){return new e(t)},e}(),lh=function(e,t){var r=e[0],n=t[0];return r.objectNumber-n.objectNumber},ia=function(){function e(){this.largestObjectNumber=0,this.header=di.forVersion(1,7),this.trailerInfo={},this.indirectObjects=new Map,this.rng=ch.withSeed(1)}return e.prototype.assign=function(t,r){this.indirectObjects.set(t,r),t.objectNumber>this.largestObjectNumber&&(this.largestObjectNumber=t.objectNumber)},e.prototype.nextRef=function(){return this.largestObjectNumber+=1,fe.of(this.largestObjectNumber)},e.prototype.register=function(t){var r=this.nextRef();return this.assign(r,t),r},e.prototype.delete=function(t){return this.indirectObjects.delete(t)},e.prototype.lookupMaybe=function(t){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var i=r.includes(Be),a=t instanceof fe?this.indirectObjects.get(t):t;if(!(!a||a===Be&&!i)){for(var o=0,s=r.length;o<s;o++){var f=r[o];if(f===Be){if(a===Be)return a}else if(a instanceof f)return a}throw new Zn(r,a)}},e.prototype.lookup=function(t){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var i=t instanceof fe?this.indirectObjects.get(t):t;if(r.length===0)return i;for(var a=0,o=r.length;a<o;a++){var s=r[a];if(s===Be){if(i===Be)return i}else if(i instanceof s)return i}throw new Zn(r,i)},e.prototype.getObjectRef=function(t){for(var r=Array.from(this.indirectObjects.entries()),n=0,i=r.length;n<i;n++){var a=r[n],o=a[0],s=a[1];if(s===t)return o}},e.prototype.enumerateIndirectObjects=function(){return Array.from(this.indirectObjects.entries()).sort(lh)},e.prototype.obj=function(t){if(t instanceof De)return t;if(t==null)return Be;if(typeof t=="string")return p.of(t);if(typeof t=="number")return q.of(t);if(typeof t=="boolean")return t?$r.True:$r.False;if(Array.isArray(t)){for(var r=ae.withContext(this),n=0,i=t.length;n<i;n++)r.push(this.obj(t[n]));return r}else{for(var a=Z.withContext(this),o=Object.keys(t),n=0,i=o.length;n<i;n++){var s=o[n],f=t[s];f!==void 0&&a.set(p.of(s),this.obj(f))}return a}},e.prototype.stream=function(t,r){return r===void 0&&(r={}),en.of(this.obj(r),Xi(t))},e.prototype.flateStream=function(t,r){return r===void 0&&(r={}),this.stream(ci.deflate(Xi(t)),H(H({},r),{Filter:"FlateDecode"}))},e.prototype.contentStream=function(t,r){return r===void 0&&(r={}),qr.of(this.obj(r),t)},e.prototype.formXObject=function(t,r){return r===void 0&&(r={}),this.contentStream(t,H(H({BBox:this.obj([0,0,0,0]),Matrix:this.obj([1,0,0,1,0,0])},r),{Type:"XObject",Subtype:"Form"}))},e.prototype.getPushGraphicsStateContentStream=function(){if(this.pushGraphicsStateContentStreamRef)return this.pushGraphicsStateContentStreamRef;var t=this.obj({}),r=ee.of(ne.PushGraphicsState),n=qr.of(t,[r]);return this.pushGraphicsStateContentStreamRef=this.register(n),this.pushGraphicsStateContentStreamRef},e.prototype.getPopGraphicsStateContentStream=function(){if(this.popGraphicsStateContentStreamRef)return this.popGraphicsStateContentStreamRef;var t=this.obj({}),r=ee.of(ne.PopGraphicsState),n=qr.of(t,[r]);return this.popGraphicsStateContentStreamRef=this.register(n),this.popGraphicsStateContentStreamRef},e.prototype.addRandomSuffix=function(t,r){return r===void 0&&(r=4),t+"-"+Math.floor(this.rng.nextInt()*Math.pow(10,r))},e.create=function(){return new e},e}(),gt=function(e){E(t,e);function t(r,n,i){i===void 0&&(i=!0);var a=e.call(this,r,n)||this;return a.normalized=!1,a.autoNormalizeCTM=i,a}return t.prototype.clone=function(r){for(var n=t.fromMapWithContext(new Map,r||this.context,this.autoNormalizeCTM),i=this.entries(),a=0,o=i.length;a<o;a++){var s=i[a],f=s[0],u=s[1];n.set(f,u)}return n},t.prototype.Parent=function(){return this.lookupMaybe(p.Parent,Z)},t.prototype.Contents=function(){return this.lookup(p.of("Contents"))},t.prototype.Annots=function(){return this.lookupMaybe(p.Annots,ae)},t.prototype.BleedBox=function(){return this.lookupMaybe(p.BleedBox,ae)},t.prototype.TrimBox=function(){return this.lookupMaybe(p.TrimBox,ae)},t.prototype.ArtBox=function(){return this.lookupMaybe(p.ArtBox,ae)},t.prototype.Resources=function(){var r=this.getInheritableAttribute(p.Resources);return this.context.lookupMaybe(r,Z)},t.prototype.MediaBox=function(){var r=this.getInheritableAttribute(p.MediaBox);return this.context.lookup(r,ae)},t.prototype.CropBox=function(){var r=this.getInheritableAttribute(p.CropBox);return this.context.lookupMaybe(r,ae)},t.prototype.Rotate=function(){var r=this.getInheritableAttribute(p.Rotate);return this.context.lookupMaybe(r,q)},t.prototype.getInheritableAttribute=function(r){var n;return this.ascend(function(i){n||(n=i.get(r))}),n},t.prototype.setParent=function(r){this.set(p.Parent,r)},t.prototype.addContentStream=function(r){var n=this.normalizedEntries().Contents||this.context.obj([]);this.set(p.Contents,n),n.push(r)},t.prototype.wrapContentStreams=function(r,n){var i=this.Contents();return i instanceof ae?(i.insert(0,r),i.push(n),!0):!1},t.prototype.addAnnot=function(r){var n=this.normalizedEntries().Annots;n.push(r)},t.prototype.removeAnnot=function(r){var n=this.normalizedEntries().Annots,i=n.indexOf(r);i!==void 0&&n.remove(i)},t.prototype.setFontDictionary=function(r,n){var i=this.normalizedEntries().Font;i.set(r,n)},t.prototype.newFontDictionaryKey=function(r){var n=this.normalizedEntries().Font;return n.uniqueKey(r)},t.prototype.newFontDictionary=function(r,n){var i=this.newFontDictionaryKey(r);return this.setFontDictionary(i,n),i},t.prototype.setXObject=function(r,n){var i=this.normalizedEntries().XObject;i.set(r,n)},t.prototype.newXObjectKey=function(r){var n=this.normalizedEntries().XObject;return n.uniqueKey(r)},t.prototype.newXObject=function(r,n){var i=this.newXObjectKey(r);return this.setXObject(i,n),i},t.prototype.setExtGState=function(r,n){var i=this.normalizedEntries().ExtGState;i.set(r,n)},t.prototype.newExtGStateKey=function(r){var n=this.normalizedEntries().ExtGState;return n.uniqueKey(r)},t.prototype.newExtGState=function(r,n){var i=this.newExtGStateKey(r);return this.setExtGState(i,n),i},t.prototype.ascend=function(r){r(this);var n=this.Parent();n&&n.ascend(r)},t.prototype.normalize=function(){if(!this.normalized){var r=this.context,n=this.get(p.Contents),i=this.context.lookup(n);i instanceof Ie&&this.set(p.Contents,r.obj([n])),this.autoNormalizeCTM&&this.wrapContentStreams(this.context.getPushGraphicsStateContentStream(),this.context.getPopGraphicsStateContentStream());var a=this.getInheritableAttribute(p.Resources),o=r.lookupMaybe(a,Z)||r.obj({});this.set(p.Resources,o);var s=o.lookupMaybe(p.Font,Z)||r.obj({});o.set(p.Font,s);var f=o.lookupMaybe(p.XObject,Z)||r.obj({});o.set(p.XObject,f);var u=o.lookupMaybe(p.ExtGState,Z)||r.obj({});o.set(p.ExtGState,u);var c=this.Annots()||r.obj([]);this.set(p.Annots,c),this.normalized=!0}},t.prototype.normalizedEntries=function(){this.normalize();var r=this.Annots(),n=this.Resources(),i=this.Contents();return{Annots:r,Resources:n,Contents:i,Font:n.lookup(p.Font,Z),XObject:n.lookup(p.XObject,Z),ExtGState:n.lookup(p.ExtGState,Z)}},t.InheritableEntries=["Resources","MediaBox","CropBox","Rotate"],t.withContextAndParent=function(r,n){var i=new Map;return i.set(p.Type,p.Page),i.set(p.Parent,n),i.set(p.Resources,r.obj({})),i.set(p.MediaBox,r.obj([0,0,612,792])),new t(i,r,!1)},t.fromMapWithContext=function(r,n,i){return i===void 0&&(i=!0),new t(r,n,i)},t}(Z),Lo=function(){function e(t,r){var n=this;this.traversedObjects=new Map,this.copy=function(i){return i instanceof gt?n.copyPDFPage(i):i instanceof Z?n.copyPDFDict(i):i instanceof ae?n.copyPDFArray(i):i instanceof Ie?n.copyPDFStream(i):i instanceof fe?n.copyPDFIndirectObject(i):i.clone()},this.copyPDFPage=function(i){for(var a=i.clone(),o=gt.InheritableEntries,s=0,f=o.length;s<f;s++){var u=p.of(o[s]),c=a.getInheritableAttribute(u);!a.get(u)&&c&&a.set(u,c)}return a.delete(p.of("Parent")),n.copyPDFDict(a)},this.copyPDFDict=function(i){if(n.traversedObjects.has(i))return n.traversedObjects.get(i);var a=i.clone(n.dest);n.traversedObjects.set(i,a);for(var o=i.entries(),s=0,f=o.length;s<f;s++){var u=o[s],c=u[0],l=u[1];a.set(c,n.copy(l))}return a},this.copyPDFArray=function(i){if(n.traversedObjects.has(i))return n.traversedObjects.get(i);var a=i.clone(n.dest);n.traversedObjects.set(i,a);for(var o=0,s=i.size();o<s;o++){var f=i.get(o);a.set(o,n.copy(f))}return a},this.copyPDFStream=function(i){if(n.traversedObjects.has(i))return n.traversedObjects.get(i);var a=i.clone(n.dest);n.traversedObjects.set(i,a);for(var o=i.dict.entries(),s=0,f=o.length;s<f;s++){var u=o[s],c=u[0],l=u[1];a.dict.set(c,n.copy(l))}return a},this.copyPDFIndirectObject=function(i){var a=n.traversedObjects.has(i);if(!a){var o=n.dest.nextRef();n.traversedObjects.set(i,o);var s=n.src.lookup(i);if(s){var f=n.copy(s);n.dest.assign(o,f)}}return n.traversedObjects.get(i)},this.src=t,this.dest=r}return e.for=function(t,r){return new e(t,r)},e}(),cu=function(){function e(t){this.subsections=t?[[t]]:[],this.chunkIdx=0,this.chunkLength=t?1:0}return e.prototype.addEntry=function(t,r){this.append({ref:t,offset:r,deleted:!1})},e.prototype.addDeletedEntry=function(t,r){this.append({ref:t,offset:r,deleted:!0})},e.prototype.toString=function(){for(var t=`xref
`,r=0,n=this.subsections.length;r<n;r++){var i=this.subsections[r];t+=i[0].ref.objectNumber+" "+i.length+`
`;for(var a=0,o=i.length;a<o;a++){var s=i[a];t+=et(String(s.offset),10,"0"),t+=" ",t+=et(String(s.ref.generationNumber),5,"0"),t+=" ",t+=s.deleted?"f":"n",t+=` 
`}}return t},e.prototype.sizeInBytes=function(){for(var t=5,r=0,n=this.subsections.length;r<n;r++){var i=this.subsections[r],a=i.length,o=i[0];t+=2,t+=String(o.ref.objectNumber).length,t+=String(a).length,t+=20*a}return t},e.prototype.copyBytesInto=function(t,r){var n=r;return t[r++]=y.x,t[r++]=y.r,t[r++]=y.e,t[r++]=y.f,t[r++]=y.Newline,r+=this.copySubsectionsIntoBuffer(this.subsections,t,r),r-n},e.prototype.copySubsectionsIntoBuffer=function(t,r,n){for(var i=n,a=t.length,o=0;o<a;o++){var s=this.subsections[o],f=String(s[0].ref.objectNumber);n+=ke(f,r,n),r[n++]=y.Space;var u=String(s.length);n+=ke(u,r,n),r[n++]=y.Newline,n+=this.copyEntriesIntoBuffer(s,r,n)}return n-i},e.prototype.copyEntriesIntoBuffer=function(t,r,n){for(var i=t.length,a=0;a<i;a++){var o=t[a],s=et(String(o.offset),10,"0");n+=ke(s,r,n),r[n++]=y.Space;var f=et(String(o.ref.generationNumber),5,"0");n+=ke(f,r,n),r[n++]=y.Space,r[n++]=o.deleted?y.f:y.n,r[n++]=y.Space,r[n++]=y.Newline}return 20*i},e.prototype.append=function(t){if(this.chunkLength===0){this.subsections.push([t]),this.chunkIdx=0,this.chunkLength=1;return}var r=this.subsections[this.chunkIdx],n=r[this.chunkLength-1];t.ref.objectNumber-n.ref.objectNumber>1?(this.subsections.push([t]),this.chunkIdx+=1,this.chunkLength=1):(r.push(t),this.chunkLength+=1)},e.create=function(){return new e({ref:fe.of(0,65535),offset:0,deleted:!0})},e.createEmpty=function(){return new e},e}(),Ca=function(){function e(t){this.lastXRefOffset=String(t)}return e.prototype.toString=function(){return`startxref
`+this.lastXRefOffset+`
%%EOF`},e.prototype.sizeInBytes=function(){return 16+this.lastXRefOffset.length},e.prototype.copyBytesInto=function(t,r){var n=r;return t[r++]=y.s,t[r++]=y.t,t[r++]=y.a,t[r++]=y.r,t[r++]=y.t,t[r++]=y.x,t[r++]=y.r,t[r++]=y.e,t[r++]=y.f,t[r++]=y.Newline,r+=ke(this.lastXRefOffset,t,r),t[r++]=y.Newline,t[r++]=y.Percent,t[r++]=y.Percent,t[r++]=y.E,t[r++]=y.O,t[r++]=y.F,r-n},e.forLastCrossRefSectionOffset=function(t){return new e(t)},e}(),hh=function(){function e(t){this.dict=t}return e.prototype.toString=function(){return`trailer
`+this.dict.toString()},e.prototype.sizeInBytes=function(){return 8+this.dict.sizeInBytes()},e.prototype.copyBytesInto=function(t,r){var n=r;return t[r++]=y.t,t[r++]=y.r,t[r++]=y.a,t[r++]=y.i,t[r++]=y.l,t[r++]=y.e,t[r++]=y.r,t[r++]=y.Newline,r+=this.dict.copyBytesInto(t,r),r-n},e.of=function(t){return new e(t)},e}(),lu=function(e){E(t,e);function t(r,n,i){i===void 0&&(i=!0);var a=e.call(this,r.obj({}),i)||this;return a.objects=n,a.offsets=a.computeObjectOffsets(),a.offsetsString=a.computeOffsetsString(),a.dict.set(p.of("Type"),p.of("ObjStm")),a.dict.set(p.of("N"),q.of(a.objects.length)),a.dict.set(p.of("First"),q.of(a.offsetsString.length)),a}return t.prototype.getObjectsCount=function(){return this.objects.length},t.prototype.clone=function(r){return t.withContextAndObjects(r||this.dict.context,this.objects.slice(),this.encode)},t.prototype.getContentsString=function(){for(var r=this.offsetsString,n=0,i=this.objects.length;n<i;n++){var a=this.objects[n],o=a[1];r+=o+`
`}return r},t.prototype.getUnencodedContents=function(){for(var r=new Uint8Array(this.getUnencodedContentsSize()),n=ke(this.offsetsString,r,0),i=0,a=this.objects.length;i<a;i++){var o=this.objects[i],s=o[1];n+=s.copyBytesInto(r,n),r[n++]=y.Newline}return r},t.prototype.getUnencodedContentsSize=function(){return this.offsetsString.length+Gn(this.offsets)[1]+Gn(this.objects)[1].sizeInBytes()+1},t.prototype.computeOffsetsString=function(){for(var r="",n=0,i=this.offsets.length;n<i;n++){var a=this.offsets[n],o=a[0],s=a[1];r+=o+" "+s+" "}return r},t.prototype.computeObjectOffsets=function(){for(var r=0,n=new Array(this.objects.length),i=0,a=this.objects.length;i<a;i++){var o=this.objects[i],s=o[0],f=o[1];n[i]=[s.objectNumber,r],r+=f.sizeInBytes()+1}return n},t.withContextAndObjects=function(r,n,i){return i===void 0&&(i=!0),new t(r,n,i)},t}(ka),hu=function(){function e(t,r){var n=this;this.parsedObjects=0,this.shouldWaitForTick=function(i){return n.parsedObjects+=i,n.parsedObjects%n.objectsPerTick===0},this.context=t,this.objectsPerTick=r}return e.prototype.serializeToBuffer=function(){return Y(this,void 0,void 0,function(){var t,r,n,i,a,o,s,f,u,c,l,h,d,v,g,m,b;return J(this,function(F){switch(F.label){case 0:return[4,this.computeBufferSize()];case 1:t=F.sent(),r=t.size,n=t.header,i=t.indirectObjects,a=t.xref,o=t.trailerDict,s=t.trailer,f=0,u=new Uint8Array(r),f+=n.copyBytesInto(u,f),u[f++]=y.Newline,u[f++]=y.Newline,c=0,l=i.length,F.label=2;case 2:return c<l?(h=i[c],d=h[0],v=h[1],g=String(d.objectNumber),f+=ke(g,u,f),u[f++]=y.Space,m=String(d.generationNumber),f+=ke(m,u,f),u[f++]=y.Space,u[f++]=y.o,u[f++]=y.b,u[f++]=y.j,u[f++]=y.Newline,f+=v.copyBytesInto(u,f),u[f++]=y.Newline,u[f++]=y.e,u[f++]=y.n,u[f++]=y.d,u[f++]=y.o,u[f++]=y.b,u[f++]=y.j,u[f++]=y.Newline,u[f++]=y.Newline,b=v instanceof lu?v.getObjectsCount():1,this.shouldWaitForTick(b)?[4,dr()]:[3,4]):[3,5];case 3:F.sent(),F.label=4;case 4:return c++,[3,2];case 5:return a&&(f+=a.copyBytesInto(u,f),u[f++]=y.Newline),o&&(f+=o.copyBytesInto(u,f),u[f++]=y.Newline,u[f++]=y.Newline),f+=s.copyBytesInto(u,f),[2,u]}})})},e.prototype.computeIndirectObjectSize=function(t){var r=t[0],n=t[1],i=r.sizeInBytes()+3,a=n.sizeInBytes()+9;return i+a},e.prototype.createTrailerDict=function(){return this.context.obj({Size:this.context.largestObjectNumber+1,Root:this.context.trailerInfo.Root,Encrypt:this.context.trailerInfo.Encrypt,Info:this.context.trailerInfo.Info,ID:this.context.trailerInfo.ID})},e.prototype.computeBufferSize=function(){return Y(this,void 0,void 0,function(){var t,r,n,i,a,o,s,f,u,c,l;return J(this,function(h){switch(h.label){case 0:t=di.forVersion(1,7),r=t.sizeInBytes()+2,n=cu.create(),i=this.context.enumerateIndirectObjects(),a=0,o=i.length,h.label=1;case 1:return a<o?(s=i[a],f=s[0],n.addEntry(f,r),r+=this.computeIndirectObjectSize(s),this.shouldWaitForTick(1)?[4,dr()]:[3,3]):[3,4];case 2:h.sent(),h.label=3;case 3:return a++,[3,1];case 4:return u=r,r+=n.sizeInBytes()+1,c=hh.of(this.createTrailerDict()),r+=c.sizeInBytes()+2,l=Ca.forLastCrossRefSectionOffset(u),r+=l.sizeInBytes(),[2,{size:r,header:t,indirectObjects:i,xref:n,trailerDict:c,trailer:l}]}})})},e.forContext=function(t,r){return new e(t,r)},e}(),du=function(e){E(t,e);function t(r){var n=e.call(this)||this;return n.data=r,n}return t.prototype.clone=function(){return t.of(this.data.slice())},t.prototype.toString=function(){return"PDFInvalidObject("+this.data.length+" bytes)"},t.prototype.sizeInBytes=function(){return this.data.length},t.prototype.copyBytesInto=function(r,n){for(var i=this.data.length,a=0;a<i;a++)r[n++]=this.data[a];return i},t.of=function(r){return new t(r)},t}(De),Ct;(function(e){e[e.Deleted=0]="Deleted",e[e.Uncompressed=1]="Uncompressed",e[e.Compressed=2]="Compressed"})(Ct||(Ct={}));var dh=function(e){E(t,e);function t(r,n,i){i===void 0&&(i=!0);var a=e.call(this,r,i)||this;return a.computeIndex=function(){for(var o=[],s=0,f=0,u=a.entries.length;f<u;f++){var c=a.entries[f],l=a.entries[f-1];f===0?o.push(c.ref.objectNumber):c.ref.objectNumber-l.ref.objectNumber>1&&(o.push(s),o.push(c.ref.objectNumber),s=0),s+=1}return o.push(s),o},a.computeEntryTuples=function(){for(var o=new Array(a.entries.length),s=0,f=a.entries.length;s<f;s++){var u=a.entries[s];if(u.type===Ct.Deleted){var c=u.type,l=u.nextFreeObjectNumber,h=u.ref;o[s]=[c,l,h.generationNumber]}if(u.type===Ct.Uncompressed){var c=u.type,d=u.offset,h=u.ref;o[s]=[c,d,h.generationNumber]}if(u.type===Ct.Compressed){var c=u.type,v=u.objectStreamRef,g=u.index;o[s]=[c,v.objectNumber,g]}}return o},a.computeMaxEntryByteWidths=function(){for(var o=a.entryTuplesCache.access(),s=[0,0,0],f=0,u=o.length;f<u;f++){var c=o[f],l=c[0],h=c[1],d=c[2],v=Nn(l),g=Nn(h),m=Nn(d);v>s[0]&&(s[0]=v),g>s[1]&&(s[1]=g),m>s[2]&&(s[2]=m)}return s},a.entries=n||[],a.entryTuplesCache=pt.populatedBy(a.computeEntryTuples),a.maxByteWidthsCache=pt.populatedBy(a.computeMaxEntryByteWidths),a.indexCache=pt.populatedBy(a.computeIndex),r.set(p.of("Type"),p.of("XRef")),a}return t.prototype.addDeletedEntry=function(r,n){var i=Ct.Deleted;this.entries.push({type:i,ref:r,nextFreeObjectNumber:n}),this.entryTuplesCache.invalidate(),this.maxByteWidthsCache.invalidate(),this.indexCache.invalidate(),this.contentsCache.invalidate()},t.prototype.addUncompressedEntry=function(r,n){var i=Ct.Uncompressed;this.entries.push({type:i,ref:r,offset:n}),this.entryTuplesCache.invalidate(),this.maxByteWidthsCache.invalidate(),this.indexCache.invalidate(),this.contentsCache.invalidate()},t.prototype.addCompressedEntry=function(r,n,i){var a=Ct.Compressed;this.entries.push({type:a,ref:r,objectStreamRef:n,index:i}),this.entryTuplesCache.invalidate(),this.maxByteWidthsCache.invalidate(),this.indexCache.invalidate(),this.contentsCache.invalidate()},t.prototype.clone=function(r){var n=this,i=n.dict,a=n.entries,o=n.encode;return t.of(i.clone(r),a.slice(),o)},t.prototype.getContentsString=function(){for(var r=this.entryTuplesCache.access(),n=this.maxByteWidthsCache.access(),i="",a=0,o=r.length;a<o;a++){for(var s=r[a],f=s[0],u=s[1],c=s[2],l=er(tr(f)),h=er(tr(u)),d=er(tr(c)),v=n[0]-1;v>=0;v--)i+=(l[v]||0).toString(2);for(var v=n[1]-1;v>=0;v--)i+=(h[v]||0).toString(2);for(var v=n[2]-1;v>=0;v--)i+=(d[v]||0).toString(2)}return i},t.prototype.getUnencodedContents=function(){for(var r=this.entryTuplesCache.access(),n=this.maxByteWidthsCache.access(),i=new Uint8Array(this.getUnencodedContentsSize()),a=0,o=0,s=r.length;o<s;o++){for(var f=r[o],u=f[0],c=f[1],l=f[2],h=er(tr(u)),d=er(tr(c)),v=er(tr(l)),g=n[0]-1;g>=0;g--)i[a++]=h[g]||0;for(var g=n[1]-1;g>=0;g--)i[a++]=d[g]||0;for(var g=n[2]-1;g>=0;g--)i[a++]=v[g]||0}return i},t.prototype.getUnencodedContentsSize=function(){var r=this.maxByteWidthsCache.access(),n=df(r);return n*this.entries.length},t.prototype.updateDict=function(){e.prototype.updateDict.call(this);var r=this.maxByteWidthsCache.access(),n=this.indexCache.access(),i=this.dict.context;this.dict.set(p.of("W"),i.obj(r)),this.dict.set(p.of("Index"),i.obj(n))},t.create=function(r,n){n===void 0&&(n=!0);var i=new t(r,[],n);return i.addDeletedEntry(fe.of(0,65535),0),i},t.of=function(r,n,i){return i===void 0&&(i=!0),new t(r,n,i)},t}(ka),vh=function(e){E(t,e);function t(r,n,i,a){var o=e.call(this,r,n)||this;return o.encodeStreams=i,o.objectsPerStream=a,o}return t.prototype.computeBufferSize=function(){return Y(this,void 0,void 0,function(){var r,n,i,a,o,s,f,u,v,g,c,b,l,h,m,d,v,g,m,b,F,w,C,S;return J(this,function(k){switch(k.label){case 0:r=this.context.largestObjectNumber+1,n=di.forVersion(1,7),i=n.sizeInBytes()+2,a=dh.create(this.createTrailerDict(),this.encodeStreams),o=[],s=[],f=[],u=this.context.enumerateIndirectObjects(),v=0,g=u.length,k.label=1;case 1:return v<g?(c=u[v],b=c[0],l=c[1],h=b===this.context.trailerInfo.Encrypt||l instanceof Ie||l instanceof du||b.generationNumber!==0,h?(o.push(c),a.addUncompressedEntry(b,i),i+=this.computeIndirectObjectSize(c),this.shouldWaitForTick(1)?[4,dr()]:[3,3]):[3,4]):[3,6];case 2:k.sent(),k.label=3;case 3:return[3,5];case 4:m=Gn(s),d=Gn(f),(!m||m.length%this.objectsPerStream===0)&&(m=[],s.push(m),d=fe.of(r++),f.push(d)),a.addCompressedEntry(b,d,m.length),m.push(c),k.label=5;case 5:return v++,[3,1];case 6:v=0,g=s.length,k.label=7;case 7:return v<g?(m=s[v],b=f[v],F=lu.withContextAndObjects(this.context,m,this.encodeStreams),a.addUncompressedEntry(b,i),i+=this.computeIndirectObjectSize([b,F]),o.push([b,F]),this.shouldWaitForTick(m.length)?[4,dr()]:[3,9]):[3,10];case 8:k.sent(),k.label=9;case 9:return v++,[3,7];case 10:return w=fe.of(r++),a.dict.set(p.of("Size"),q.of(r)),a.addUncompressedEntry(w,i),C=i,i+=this.computeIndirectObjectSize([w,a]),o.push([w,a]),S=Ca.forLastCrossRefSectionOffset(C),i+=S.sizeInBytes(),[2,{size:i,header:n,indirectObjects:o,trailer:S}]}})})},t.forContext=function(r,n,i,a){return i===void 0&&(i=!0),a===void 0&&(a=50),new t(r,n,i,a)},t}(hu),U=function(e){E(t,e);function t(r){var n=e.call(this)||this;return n.value=r,n}return t.prototype.asBytes=function(){for(var r=this.value+(this.value.length%2===1?"0":""),n=r.length,i=new Uint8Array(r.length/2),a=0,o=0;a<n;){var s=parseInt(r.substring(a,a+2),16);i[o]=s,a+=2,o+=1}return i},t.prototype.decodeText=function(){var r=this.asBytes();return ys(r)?vs(r):uu(r)},t.prototype.decodeDate=function(){var r=this.decodeText(),n=cs(r);if(!n)throw new fu(r);return n},t.prototype.asString=function(){return this.value},t.prototype.clone=function(){return t.of(this.value)},t.prototype.toString=function(){return"<"+this.value+">"},t.prototype.sizeInBytes=function(){return this.value.length+2},t.prototype.copyBytesInto=function(r,n){return r[n++]=y.LessThan,n+=ke(this.value,r,n),r[n++]=y.GreaterThan,this.value.length+2},t.of=function(r){return new t(r)},t.fromText=function(r){for(var n=yf(r),i="",a=0,o=n.length;a<o;a++)i+=nn(n[a],4);return new t(i)},t}(De),Jn=function(){function e(t,r){this.encoding=t===_r.ZapfDingbats?Cn.ZapfDingbats:t===_r.Symbol?Cn.Symbol:Cn.WinAnsi,this.font=Bl.load(t),this.fontName=this.font.FontName,this.customName=r}return e.prototype.encodeText=function(t){for(var r=this.encodeTextAsGlyphs(t),n=new Array(r.length),i=0,a=r.length;i<a;i++)n[i]=oi(r[i].code);return U.of(n.join(""))},e.prototype.widthOfTextAtSize=function(t,r){for(var n=this.encodeTextAsGlyphs(t),i=0,a=0,o=n.length;a<o;a++){var s=n[a].name,f=(n[a+1]||{}).name,u=this.font.getXAxisKerningForPair(s,f)||0;i+=this.widthOfGlyph(s)+u}var c=r/1e3;return i*c},e.prototype.heightOfFontAtSize=function(t,r){r===void 0&&(r={});var n=r.descender,i=n===void 0?!0:n,a=this.font,o=a.Ascender,s=a.Descender,f=a.FontBBox,u=o||f[3],c=s||f[1],l=u-c;return i||(l+=s||0),l/1e3*t},e.prototype.sizeOfFontAtHeight=function(t){var r=this.font,n=r.Ascender,i=r.Descender,a=r.FontBBox,o=n||a[3],s=i||a[1];return 1e3*t/(o-s)},e.prototype.embedIntoContext=function(t,r){var n=t.obj({Type:"Font",Subtype:"Type1",BaseFont:this.customName||this.fontName,Encoding:this.encoding===Cn.WinAnsi?"WinAnsiEncoding":void 0});return r?(t.assign(r,n),r):t.register(n)},e.prototype.widthOfGlyph=function(t){return this.font.getWidthOfGlyph(t)||250},e.prototype.encodeTextAsGlyphs=function(t){for(var r=Array.from(t),n=new Array(r.length),i=0,a=r.length;i<a;i++){var o=ef(r[i]);n[i]=this.encoding.encodeUnicodeCodePoint(o)}return n},e.for=function(t,r){return new e(t,r)},e}(),ph=function(e,t){for(var r=new Array(e.length),n=0,i=e.length;n<i;n++){var a=e[n],o=Go(Vn(t(a))),s=Go.apply(void 0,a.codePoints.map(yh));r[n]=[o,s]}return gh(r)},gh=function(e){return`/CIDInit /ProcSet findresource begin
12 dict begin
begincmap
/CIDSystemInfo <<
  /Registry (Adobe)
  /Ordering (UCS)
  /Supplement 0
>> def
/CMapName /Adobe-Identity-UCS def
/CMapType 2 def
1 begincodespacerange
<0000><ffff>
endcodespacerange
`+e.length+` beginbfchar
`+e.map(function(t){var r=t[0],n=t[1];return r+" "+n}).join(`
`)+`
endbfchar
endcmap
CMapName currentdict /CMap defineresource pop
end
end`},Go=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return"<"+e.join("")+">"},Vn=function(e){return nn(e,4)},yh=function(e){if(bf(e))return Vn(e);if(mf(e)){var t=hs(e),r=ds(e);return""+Vn(t)+Vn(r)}var n=oi(e),i="0x"+n+" is not a valid UTF-8 or UTF-16 codepoint.";throw new Error(i)},bh=function(e){var t=0,r=function(n){t|=1<<n-1};return e.fixedPitch&&r(1),e.serif&&r(2),e.symbolic&&r(3),e.script&&r(4),e.nonsymbolic&&r(6),e.italic&&r(7),e.allCap&&r(17),e.smallCap&&r(18),e.forceBold&&r(19),t},mh=function(e){var t=e["OS/2"]?e["OS/2"].sFamilyClass:0,r=bh({fixedPitch:e.post.isFixedPitch,serif:1<=t&&t<=7,symbolic:!0,script:t===10,italic:e.head.macStyle.italic});return r},ue=function(e){E(t,e);function t(r){var n=e.call(this)||this;return n.value=r,n}return t.prototype.asBytes=function(){for(var r=[],n="",i=!1,a=function(l){l!==void 0&&r.push(l),i=!1},o=0,s=this.value.length;o<s;o++){var f=this.value[o],u=G(f),c=this.value[o+1];i?u===y.Newline||u===y.CarriageReturn?a():u===y.n?a(y.Newline):u===y.r?a(y.CarriageReturn):u===y.t?a(y.Tab):u===y.b?a(y.Backspace):u===y.f?a(y.FormFeed):u===y.LeftParen?a(y.LeftParen):u===y.RightParen?a(y.RightParen):u===y.Backspace?a(y.BackSlash):u>=y.Zero&&u<=y.Seven?(n+=f,(n.length===3||!(c>="0"&&c<="7"))&&(a(parseInt(n,8)),n="")):a(u):u===y.BackSlash?i=!0:a(u)}return new Uint8Array(r)},t.prototype.decodeText=function(){var r=this.asBytes();return ys(r)?vs(r):uu(r)},t.prototype.decodeDate=function(){var r=this.decodeText(),n=cs(r);if(!n)throw new fu(r);return n},t.prototype.asString=function(){return this.value},t.prototype.clone=function(){return t.of(this.value)},t.prototype.toString=function(){return"("+this.value+")"},t.prototype.sizeInBytes=function(){return this.value.length+2},t.prototype.copyBytesInto=function(r,n){return r[n++]=y.LeftParen,n+=ke(this.value,r,n),r[n++]=y.RightParen,this.value.length+2},t.of=function(r){return new t(r)},t.fromDate=function(r){var n=et(String(r.getUTCFullYear()),4,"0"),i=et(String(r.getUTCMonth()+1),2,"0"),a=et(String(r.getUTCDate()),2,"0"),o=et(String(r.getUTCHours()),2,"0"),s=et(String(r.getUTCMinutes()),2,"0"),f=et(String(r.getUTCSeconds()),2,"0");return new t("D:"+n+i+a+o+s+f+"Z")},t}(De),Ta=function(){function e(t,r,n,i){var a=this;this.allGlyphsInFontSortedById=function(){for(var o=new Array(a.font.characterSet.length),s=0,f=o.length;s<f;s++){var u=a.font.characterSet[s];o[s]=a.font.glyphForCodePoint(u)}return hf(o.sort(lf),function(c){return c.id})},this.font=t,this.scale=1e3/this.font.unitsPerEm,this.fontData=r,this.fontName=this.font.postscriptName||"Font",this.customName=n,this.fontFeatures=i,this.baseFontName="",this.glyphCache=pt.populatedBy(this.allGlyphsInFontSortedById)}return e.for=function(t,r,n,i){return Y(this,void 0,void 0,function(){var a;return J(this,function(o){switch(o.label){case 0:return[4,t.create(r)];case 1:return a=o.sent(),[2,new e(a,r,n,i)]}})})},e.prototype.encodeText=function(t){for(var r=this.font.layout(t,this.fontFeatures).glyphs,n=new Array(r.length),i=0,a=r.length;i<a;i++)n[i]=nn(r[i].id,4);return U.of(n.join(""))},e.prototype.widthOfTextAtSize=function(t,r){for(var n=this.font.layout(t,this.fontFeatures).glyphs,i=0,a=0,o=n.length;a<o;a++)i+=n[a].advanceWidth*this.scale;var s=r/1e3;return i*s},e.prototype.heightOfFontAtSize=function(t,r){r===void 0&&(r={});var n=r.descender,i=n===void 0?!0:n,a=this.font,o=a.ascent,s=a.descent,f=a.bbox,u=(o||f.maxY)*this.scale,c=(s||f.minY)*this.scale,l=u-c;return i||(l-=Math.abs(s)||0),l/1e3*t},e.prototype.sizeOfFontAtHeight=function(t){var r=this.font,n=r.ascent,i=r.descent,a=r.bbox,o=(n||a.maxY)*this.scale,s=(i||a.minY)*this.scale;return 1e3*t/(o-s)},e.prototype.embedIntoContext=function(t,r){return this.baseFontName=this.customName||t.addRandomSuffix(this.fontName),this.embedFontDict(t,r)},e.prototype.embedFontDict=function(t,r){return Y(this,void 0,void 0,function(){var n,i,a;return J(this,function(o){switch(o.label){case 0:return[4,this.embedCIDFontDict(t)];case 1:return n=o.sent(),i=this.embedUnicodeCmap(t),a=t.obj({Type:"Font",Subtype:"Type0",BaseFont:this.baseFontName,Encoding:"Identity-H",DescendantFonts:[n],ToUnicode:i}),r?(t.assign(r,a),[2,r]):[2,t.register(a)]}})})},e.prototype.isCFF=function(){return this.font.cff},e.prototype.embedCIDFontDict=function(t){return Y(this,void 0,void 0,function(){var r,n;return J(this,function(i){switch(i.label){case 0:return[4,this.embedFontDescriptor(t)];case 1:return r=i.sent(),n=t.obj({Type:"Font",Subtype:this.isCFF()?"CIDFontType0":"CIDFontType2",CIDToGIDMap:"Identity",BaseFont:this.baseFontName,CIDSystemInfo:{Registry:ue.of("Adobe"),Ordering:ue.of("Identity"),Supplement:0},FontDescriptor:r,W:this.computeWidths()}),[2,t.register(n)]}})})},e.prototype.embedFontDescriptor=function(t){return Y(this,void 0,void 0,function(){var r,n,i,a,o,s,f,u,c,l,h,d,v,g,m;return J(this,function(b){switch(b.label){case 0:return[4,this.embedFontStream(t)];case 1:return r=b.sent(),n=this.scale,i=this.font,a=i.italicAngle,o=i.ascent,s=i.descent,f=i.capHeight,u=i.xHeight,c=this.font.bbox,l=c.minX,h=c.minY,d=c.maxX,v=c.maxY,g=t.obj((m={Type:"FontDescriptor",FontName:this.baseFontName,Flags:mh(this.font),FontBBox:[l*n,h*n,d*n,v*n],ItalicAngle:a,Ascent:o*n,Descent:s*n,CapHeight:(f||o)*n,XHeight:(u||0)*n,StemV:0},m[this.isCFF()?"FontFile3":"FontFile2"]=r,m)),[2,t.register(g)]}})})},e.prototype.serializeFont=function(){return Y(this,void 0,void 0,function(){return J(this,function(t){return[2,this.fontData]})})},e.prototype.embedFontStream=function(t){return Y(this,void 0,void 0,function(){var r,n,i;return J(this,function(a){switch(a.label){case 0:return i=(n=t).flateStream,[4,this.serializeFont()];case 1:return r=i.apply(n,[a.sent(),{Subtype:this.isCFF()?"CIDFontType0C":void 0}]),[2,t.register(r)]}})})},e.prototype.embedUnicodeCmap=function(t){var r=ph(this.glyphCache.access(),this.glyphId.bind(this)),n=t.flateStream(r);return t.register(n)},e.prototype.glyphId=function(t){return t?t.id:-1},e.prototype.computeWidths=function(){for(var t=this.glyphCache.access(),r=[],n=[],i=0,a=t.length;i<a;i++){var o=t[i],s=t[i-1],f=this.glyphId(o),u=this.glyphId(s);i===0?r.push(f):f-u!==1&&(r.push(n),r.push(f),n=[]),n.push(o.advanceWidth*this.scale)}return r.push(n),r},e}(),xh=function(e){E(t,e);function t(r,n,i,a){var o=e.call(this,r,n,i,a)||this;return o.subset=o.font.createSubset(),o.glyphs=[],o.glyphCache=pt.populatedBy(function(){return o.glyphs}),o.glyphIdMap=new Map,o}return t.for=function(r,n,i,a){return Y(this,void 0,void 0,function(){var o;return J(this,function(s){switch(s.label){case 0:return[4,r.create(n)];case 1:return o=s.sent(),[2,new t(o,n,i,a)]}})})},t.prototype.encodeText=function(r){for(var n=this.font.layout(r,this.fontFeatures).glyphs,i=new Array(n.length),a=0,o=n.length;a<o;a++){var s=n[a],f=this.subset.includeGlyph(s);this.glyphs[f-1]=s,this.glyphIdMap.set(s.id,f),i[a]=nn(f,4)}return this.glyphCache.invalidate(),U.of(i.join(""))},t.prototype.isCFF=function(){return this.subset.cff},t.prototype.glyphId=function(r){return r?this.glyphIdMap.get(r.id):-1},t.prototype.serializeFont=function(){var r=this;return new Promise(function(n,i){var a=[];r.subset.encodeStream().on("data",function(o){return a.push(o)}).on("end",function(){return n(cf(a))}).on("error",function(o){return i(o)})})},t}(Ta),aa;(function(e){e.Source="Source",e.Data="Data",e.Alternative="Alternative",e.Supplement="Supplement",e.EncryptedPayload="EncryptedPayload",e.FormData="EncryptedPayload",e.Schema="Schema",e.Unspecified="Unspecified"})(aa||(aa={}));var wh=function(){function e(t,r,n){n===void 0&&(n={}),this.fileData=t,this.fileName=r,this.options=n}return e.for=function(t,r,n){return n===void 0&&(n={}),new e(t,r,n)},e.prototype.embedIntoContext=function(t,r){return Y(this,void 0,void 0,function(){var n,i,a,o,s,f,u,c,l;return J(this,function(h){return n=this.options,i=n.mimeType,a=n.description,o=n.creationDate,s=n.modificationDate,f=n.afRelationship,u=t.flateStream(this.fileData,{Type:"EmbeddedFile",Subtype:i??void 0,Params:{Size:this.fileData.length,CreationDate:o?ue.fromDate(o):void 0,ModDate:s?ue.fromDate(s):void 0}}),c=t.register(u),l=t.obj({Type:"Filespec",F:ue.of(this.fileName),UF:U.fromText(this.fileName),EF:{F:c},Desc:a?U.fromText(a):void 0,AFRelationship:f??void 0}),r?(t.assign(r,l),[2,r]):[2,t.register(l)]})})},e}(),Ho=[65472,65473,65474,65475,65477,65478,65479,65480,65481,65482,65483,65484,65485,65486,65487],hr;(function(e){e.DeviceGray="DeviceGray",e.DeviceRGB="DeviceRGB",e.DeviceCMYK="DeviceCMYK"})(hr||(hr={}));var Sh={1:hr.DeviceGray,3:hr.DeviceRGB,4:hr.DeviceCMYK},vu=function(){function e(t,r,n,i,a){this.imageData=t,this.bitsPerComponent=r,this.width=n,this.height=i,this.colorSpace=a}return e.for=function(t){return Y(this,void 0,void 0,function(){var r,n,i,a,o,s,f,u,c,l;return J(this,function(h){if(r=new DataView(t.buffer),n=r.getUint16(0),n!==65496)throw new Error("SOI not found in JPEG");for(i=2;i<r.byteLength&&(a=r.getUint16(i),i+=2,!Ho.includes(a));)i+=r.getUint16(i);if(!Ho.includes(a))throw new Error("Invalid JPEG");if(i+=2,o=r.getUint8(i++),s=r.getUint16(i),i+=2,f=r.getUint16(i),i+=2,u=r.getUint8(i++),c=Sh[u],!c)throw new Error("Unknown JPEG channel.");return l=c,[2,new e(t,o,f,s,l)]})})},e.prototype.embedIntoContext=function(t,r){return Y(this,void 0,void 0,function(){var n;return J(this,function(i){return n=t.stream(this.imageData,{Type:"XObject",Subtype:"Image",BitsPerComponent:this.bitsPerComponent,Width:this.width,Height:this.height,ColorSpace:this.colorSpace,Filter:"DCTDecode",Decode:this.colorSpace===hr.DeviceCMYK?[1,0,1,0,1,0,1,0]:void 0}),r?(t.assign(r,n),[2,r]):[2,t.register(n)]})})},e}(),R={};R.toRGBA8=function(e){var t=e.width,r=e.height;if(e.tabs.acTL==null)return[R.toRGBA8.decodeImage(e.data,t,r,e).buffer];var n=[];e.frames[0].data==null&&(e.frames[0].data=e.data);for(var i=t*r*4,a=new Uint8Array(i),o=new Uint8Array(i),s=new Uint8Array(i),f=0;f<e.frames.length;f++){var u=e.frames[f],c=u.rect.x,l=u.rect.y,h=u.rect.width,d=u.rect.height,v=R.toRGBA8.decodeImage(u.data,h,d,e);if(f!=0)for(var g=0;g<i;g++)s[g]=a[g];if(u.blend==0?R._copyTile(v,h,d,a,t,r,c,l,0):u.blend==1&&R._copyTile(v,h,d,a,t,r,c,l,1),n.push(a.buffer.slice(0)),u.dispose!=0){if(u.dispose==1)R._copyTile(o,h,d,a,t,r,c,l,0);else if(u.dispose==2)for(var g=0;g<i;g++)a[g]=s[g]}}return n};R.toRGBA8.decodeImage=function(e,t,r,n){var i=t*r,a=R.decode._getBPP(n),o=Math.ceil(t*a/8),s=new Uint8Array(i*4),f=new Uint32Array(s.buffer),u=n.ctype,c=n.depth,l=R._bin.readUshort;if(u==6){var h=i<<2;if(c==8)for(var d=0;d<h;d+=4)s[d]=e[d],s[d+1]=e[d+1],s[d+2]=e[d+2],s[d+3]=e[d+3];if(c==16)for(var d=0;d<h;d++)s[d]=e[d<<1]}else if(u==2){var v=n.tabs.tRNS;if(v==null){if(c==8)for(var d=0;d<i;d++){var g=d*3;f[d]=255<<24|e[g+2]<<16|e[g+1]<<8|e[g]}if(c==16)for(var d=0;d<i;d++){var g=d*6;f[d]=255<<24|e[g+4]<<16|e[g+2]<<8|e[g]}}else{var m=v[0],b=v[1],F=v[2];if(c==8)for(var d=0;d<i;d++){var w=d<<2,g=d*3;f[d]=255<<24|e[g+2]<<16|e[g+1]<<8|e[g],e[g]==m&&e[g+1]==b&&e[g+2]==F&&(s[w+3]=0)}if(c==16)for(var d=0;d<i;d++){var w=d<<2,g=d*6;f[d]=255<<24|e[g+4]<<16|e[g+2]<<8|e[g],l(e,g)==m&&l(e,g+2)==b&&l(e,g+4)==F&&(s[w+3]=0)}}}else if(u==3){var C=n.tabs.PLTE,S=n.tabs.tRNS,k=S?S.length:0;if(c==1)for(var T=0;T<r;T++)for(var P=T*o,D=T*t,d=0;d<t;d++){var w=D+d<<2,N=e[P+(d>>3)]>>7-((d&7)<<0)&1,A=3*N;s[w]=C[A],s[w+1]=C[A+1],s[w+2]=C[A+2],s[w+3]=N<k?S[N]:255}if(c==2)for(var T=0;T<r;T++)for(var P=T*o,D=T*t,d=0;d<t;d++){var w=D+d<<2,N=e[P+(d>>2)]>>6-((d&3)<<1)&3,A=3*N;s[w]=C[A],s[w+1]=C[A+1],s[w+2]=C[A+2],s[w+3]=N<k?S[N]:255}if(c==4)for(var T=0;T<r;T++)for(var P=T*o,D=T*t,d=0;d<t;d++){var w=D+d<<2,N=e[P+(d>>1)]>>4-((d&1)<<2)&15,A=3*N;s[w]=C[A],s[w+1]=C[A+1],s[w+2]=C[A+2],s[w+3]=N<k?S[N]:255}if(c==8)for(var d=0;d<i;d++){var w=d<<2,N=e[d],A=3*N;s[w]=C[A],s[w+1]=C[A+1],s[w+2]=C[A+2],s[w+3]=N<k?S[N]:255}}else if(u==4){if(c==8)for(var d=0;d<i;d++){var w=d<<2,z=d<<1,B=e[z];s[w]=B,s[w+1]=B,s[w+2]=B,s[w+3]=e[z+1]}if(c==16)for(var d=0;d<i;d++){var w=d<<2,z=d<<2,B=e[z];s[w]=B,s[w+1]=B,s[w+2]=B,s[w+3]=e[z+2]}}else if(u==0)for(var m=n.tabs.tRNS?n.tabs.tRNS:-1,T=0;T<r;T++){var W=T*o,L=T*t;if(c==1)for(var j=0;j<t;j++){var B=255*(e[W+(j>>>3)]>>>7-(j&7)&1),ie=B==m*255?0:255;f[L+j]=ie<<24|B<<16|B<<8|B}else if(c==2)for(var j=0;j<t;j++){var B=85*(e[W+(j>>>2)]>>>6-((j&3)<<1)&3),ie=B==m*85?0:255;f[L+j]=ie<<24|B<<16|B<<8|B}else if(c==4)for(var j=0;j<t;j++){var B=17*(e[W+(j>>>1)]>>>4-((j&1)<<2)&15),ie=B==m*17?0:255;f[L+j]=ie<<24|B<<16|B<<8|B}else if(c==8)for(var j=0;j<t;j++){var B=e[W+j],ie=B==m?0:255;f[L+j]=ie<<24|B<<16|B<<8|B}else if(c==16)for(var j=0;j<t;j++){var B=e[W+(j<<1)],ie=l(e,W+(j<<d))==m?0:255;f[L+j]=ie<<24|B<<16|B<<8|B}}return s};R.decode=function(e){for(var t=new Uint8Array(e),r=8,n=R._bin,i=n.readUshort,a=n.readUint,o={tabs:{},frames:[]},s=new Uint8Array(t.length),f=0,u,c=0,l=[137,80,78,71,13,10,26,10],h=0;h<8;h++)if(t[h]!=l[h])throw"The input is not a PNG file!";for(;r<t.length;){var d=n.readUint(t,r);r+=4;var v=n.readASCII(t,r,4);if(r+=4,v=="IHDR")R.decode._IHDR(t,r,o);else if(v=="IDAT"){for(var h=0;h<d;h++)s[f+h]=t[r+h];f+=d}else if(v=="acTL")o.tabs[v]={num_frames:a(t,r),num_plays:a(t,r+4)},u=new Uint8Array(t.length);else if(v=="fcTL"){if(c!=0){var g=o.frames[o.frames.length-1];g.data=R.decode._decompress(o,u.slice(0,c),g.rect.width,g.rect.height),c=0}var m={x:a(t,r+12),y:a(t,r+16),width:a(t,r+4),height:a(t,r+8)},b=i(t,r+22);b=i(t,r+20)/(b==0?100:b);var F={rect:m,delay:Math.round(b*1e3),dispose:t[r+24],blend:t[r+25]};o.frames.push(F)}else if(v=="fdAT"){for(var h=0;h<d-4;h++)u[c+h]=t[r+h+4];c+=d-4}else if(v=="pHYs")o.tabs[v]=[n.readUint(t,r),n.readUint(t,r+4),t[r+8]];else if(v=="cHRM"){o.tabs[v]=[];for(var h=0;h<8;h++)o.tabs[v].push(n.readUint(t,r+h*4))}else if(v=="tEXt"){o.tabs[v]==null&&(o.tabs[v]={});var w=n.nextZero(t,r),C=n.readASCII(t,r,w-r),S=n.readASCII(t,w+1,r+d-w-1);o.tabs[v][C]=S}else if(v=="iTXt"){o.tabs[v]==null&&(o.tabs[v]={});var w=0,k=r;w=n.nextZero(t,k);var C=n.readASCII(t,k,w-k);k=w+1,t[k],t[k+1],k+=2,w=n.nextZero(t,k),n.readASCII(t,k,w-k),k=w+1,w=n.nextZero(t,k),n.readUTF8(t,k,w-k),k=w+1;var S=n.readUTF8(t,k,d-(k-r));o.tabs[v][C]=S}else if(v=="PLTE")o.tabs[v]=n.readBytes(t,r,d);else if(v=="hIST"){var T=o.tabs.PLTE.length/3;o.tabs[v]=[];for(var h=0;h<T;h++)o.tabs[v].push(i(t,r+h*2))}else if(v=="tRNS")o.ctype==3?o.tabs[v]=n.readBytes(t,r,d):o.ctype==0?o.tabs[v]=i(t,r):o.ctype==2&&(o.tabs[v]=[i(t,r),i(t,r+2),i(t,r+4)]);else if(v=="gAMA")o.tabs[v]=n.readUint(t,r)/1e5;else if(v=="sRGB")o.tabs[v]=t[r];else if(v=="bKGD")o.ctype==0||o.ctype==4?o.tabs[v]=[i(t,r)]:o.ctype==2||o.ctype==6?o.tabs[v]=[i(t,r),i(t,r+2),i(t,r+4)]:o.ctype==3&&(o.tabs[v]=t[r]);else if(v=="IEND")break;r+=d,n.readUint(t,r),r+=4}if(c!=0){var g=o.frames[o.frames.length-1];g.data=R.decode._decompress(o,u.slice(0,c),g.rect.width,g.rect.height),c=0}return o.data=R.decode._decompress(o,s,o.width,o.height),delete o.compress,delete o.interlace,delete o.filter,o};R.decode._decompress=function(e,t,r,n){var i=R.decode._getBPP(e),a=Math.ceil(r*i/8),o=new Uint8Array((a+1+e.interlace)*n);return t=R.decode._inflate(t,o),e.interlace==0?t=R.decode._filterZero(t,e,0,r,n):e.interlace==1&&(t=R.decode._readInterlace(t,e)),t};R.decode._inflate=function(e,t){var r=R.inflateRaw(new Uint8Array(e.buffer,2,e.length-6),t);return r};R.inflateRaw=function(){var e={};return e.H={},e.H.N=function(t,r){var n=Uint8Array,i=0,a=0,o=0,s=0,f=0,u=0,c=0,l=0,h=0,d,v;if(t[0]==3&&t[1]==0)return r||new n(0);var g=e.H,m=g.b,b=g.e,F=g.R,w=g.n,C=g.A,S=g.Z,k=g.m,T=r==null;for(T&&(r=new n(t.length>>>2<<3));i==0;){if(i=m(t,h,1),a=m(t,h+1,2),h+=3,a==0){h&7&&(h+=8-(h&7));var P=(h>>>3)+4,D=t[P-4]|t[P-3]<<8;T&&(r=e.H.W(r,l+D)),r.set(new n(t.buffer,t.byteOffset+P,D),l),h=P+D<<3,l+=D;continue}if(T&&(r=e.H.W(r,l+(1<<17))),a==1&&(d=k.J,v=k.h,u=512-1,c=32-1),a==2){o=b(t,h,5)+257,s=b(t,h+5,5)+1,f=b(t,h+10,4)+4,h+=14;for(var N=1,A=0;A<38;A+=2)k.Q[A]=0,k.Q[A+1]=0;for(var A=0;A<f;A++){var z=b(t,h+A*3,3);k.Q[(k.X[A]<<1)+1]=z,z>N&&(N=z)}h+=3*f,w(k.Q,N),C(k.Q,N,k.u),d=k.w,v=k.d,h=F(k.u,(1<<N)-1,o+s,t,h,k.v);var B=g.V(k.v,0,o,k.C);u=(1<<B)-1;var W=g.V(k.v,o,s,k.D);c=(1<<W)-1,w(k.C,B),C(k.C,B,d),w(k.D,W),C(k.D,W,v)}for(;;){var L=d[S(t,h)&u];h+=L&15;var j=L>>>4;if(!(j>>>8))r[l++]=j;else{if(j==256)break;var ie=l+j-254;if(j>264){var Fe=k.q[j-257];ie=l+(Fe>>>3)+b(t,h,Fe&7),h+=Fe&7}var Oe=v[S(t,h)&c];h+=Oe&15;var xt=Oe>>>4,Ee=k.c[xt],Ve=(Ee>>>4)+m(t,h,Ee&15);for(h+=Ee&15;l<ie;)r[l]=r[l++-Ve],r[l]=r[l++-Ve],r[l]=r[l++-Ve],r[l]=r[l++-Ve];l=ie}}}return r.length==l?r:r.slice(0,l)},e.H.W=function(t,r){var n=t.length;if(r<=n)return t;var i=new Uint8Array(n<<1);return i.set(t,0),i},e.H.R=function(t,r,n,i,a,o){for(var s=e.H.e,f=e.H.Z,u=0;u<n;){var c=t[f(i,a)&r];a+=c&15;var l=c>>>4;if(l<=15)o[u]=l,u++;else{var h=0,d=0;l==16?(d=3+s(i,a,2),a+=2,h=o[u-1]):l==17?(d=3+s(i,a,3),a+=3):l==18&&(d=11+s(i,a,7),a+=7);for(var v=u+d;u<v;)o[u]=h,u++}}return a},e.H.V=function(t,r,n,i){for(var a=0,o=0,s=i.length>>>1;o<n;){var f=t[o+r];i[o<<1]=0,i[(o<<1)+1]=f,f>a&&(a=f),o++}for(;o<s;)i[o<<1]=0,i[(o<<1)+1]=0,o++;return a},e.H.n=function(t,r){for(var n=e.H.m,i=t.length,a,o,s,f,u,c=n.j,f=0;f<=r;f++)c[f]=0;for(f=1;f<i;f+=2)c[t[f]]++;var l=n.K;for(a=0,c[0]=0,o=1;o<=r;o++)a=a+c[o-1]<<1,l[o]=a;for(s=0;s<i;s+=2)u=t[s+1],u!=0&&(t[s]=l[u],l[u]++)},e.H.A=function(t,r,n){for(var i=t.length,a=e.H.m,o=a.r,s=0;s<i;s+=2)if(t[s+1]!=0)for(var f=s>>1,u=t[s+1],c=f<<4|u,l=r-u,h=t[s]<<l,d=h+(1<<l);h!=d;){var v=o[h]>>>15-r;n[v]=c,h++}},e.H.l=function(t,r){for(var n=e.H.m.r,i=15-r,a=0;a<t.length;a+=2){var o=t[a]<<r-t[a+1];t[a]=n[o]>>>i}},e.H.M=function(t,r,n){n=n<<(r&7);var i=r>>>3;t[i]|=n,t[i+1]|=n>>>8},e.H.I=function(t,r,n){n=n<<(r&7);var i=r>>>3;t[i]|=n,t[i+1]|=n>>>8,t[i+2]|=n>>>16},e.H.e=function(t,r,n){return(t[r>>>3]|t[(r>>>3)+1]<<8)>>>(r&7)&(1<<n)-1},e.H.b=function(t,r,n){return(t[r>>>3]|t[(r>>>3)+1]<<8|t[(r>>>3)+2]<<16)>>>(r&7)&(1<<n)-1},e.H.Z=function(t,r){return(t[r>>>3]|t[(r>>>3)+1]<<8|t[(r>>>3)+2]<<16)>>>(r&7)},e.H.i=function(t,r){return(t[r>>>3]|t[(r>>>3)+1]<<8|t[(r>>>3)+2]<<16|t[(r>>>3)+3]<<24)>>>(r&7)},e.H.m=function(){var t=Uint16Array,r=Uint32Array;return{K:new t(16),j:new t(16),X:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],S:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],T:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],q:new t(32),p:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],z:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],c:new r(32),J:new t(512),_:[],h:new t(32),$:[],w:new t(32768),C:[],v:[],d:new t(32768),D:[],u:new t(512),Q:[],r:new t(32768),s:new r(286),Y:new r(30),a:new r(19),t:new r(15e3),k:new t(65536),g:new t(32768)}}(),function(){for(var t=e.H.m,r=32768,n=0;n<r;n++){var i=n;i=(i&2863311530)>>>1|(i&1431655765)<<1,i=(i&3435973836)>>>2|(i&858993459)<<2,i=(i&4042322160)>>>4|(i&252645135)<<4,i=(i&4278255360)>>>8|(i&16711935)<<8,t.r[n]=(i>>>16|i<<16)>>>17}function a(o,s,f){for(;s--!=0;)o.push(0,f)}for(var n=0;n<32;n++)t.q[n]=t.S[n]<<3|t.T[n],t.c[n]=t.p[n]<<4|t.z[n];a(t._,144,8),a(t._,255-143,9),a(t._,279-255,7),a(t._,287-279,8),e.H.n(t._,9),e.H.A(t._,9,t.J),e.H.l(t._,9),a(t.$,32,5),e.H.n(t.$,5),e.H.A(t.$,5,t.h),e.H.l(t.$,5),a(t.Q,19,0),a(t.C,286,0),a(t.D,30,0),a(t.v,320,0)}(),e.H.N}();R.decode._readInterlace=function(e,t){for(var r=t.width,n=t.height,i=R.decode._getBPP(t),a=i>>3,o=Math.ceil(r*i/8),s=new Uint8Array(n*o),f=0,u=[0,0,4,0,2,0,1],c=[0,4,0,2,0,1,0],l=[8,8,8,4,4,2,2],h=[8,8,4,4,2,2,1],d=0;d<7;){for(var v=l[d],g=h[d],m=0,b=0,F=u[d];F<n;)F+=v,b++;for(var w=c[d];w<r;)w+=g,m++;var C=Math.ceil(m*i/8);R.decode._filterZero(e,t,f,m,b);for(var S=0,k=u[d];k<n;){for(var T=c[d],P=f+S*C<<3;T<r;){if(i==1){var D=e[P>>3];D=D>>7-(P&7)&1,s[k*o+(T>>3)]|=D<<7-((T&7)<<0)}if(i==2){var D=e[P>>3];D=D>>6-(P&7)&3,s[k*o+(T>>2)]|=D<<6-((T&3)<<1)}if(i==4){var D=e[P>>3];D=D>>4-(P&7)&15,s[k*o+(T>>1)]|=D<<4-((T&1)<<2)}if(i>=8)for(var N=k*o+T*a,A=0;A<a;A++)s[N+A]=e[(P>>3)+A];P+=i,T+=g}S++,k+=v}m*b!=0&&(f+=b*(1+C)),d=d+1}return s};R.decode._getBPP=function(e){var t=[1,null,3,1,2,null,4][e.ctype];return t*e.depth};R.decode._filterZero=function(e,t,r,n,i){var a=R.decode._getBPP(t),o=Math.ceil(n*a/8),s=R.decode._paeth;a=Math.ceil(a/8);var f=0,u=1,c=e[r],l=0;if(c>1&&(e[r]=[0,0,1][c-2]),c==3)for(l=a;l<o;l++)e[l+1]=e[l+1]+(e[l+1-a]>>>1)&255;for(var h=0;h<i;h++)if(f=r+h*o,u=f+h+1,c=e[u-1],l=0,c==0)for(;l<o;l++)e[f+l]=e[u+l];else if(c==1){for(;l<a;l++)e[f+l]=e[u+l];for(;l<o;l++)e[f+l]=e[u+l]+e[f+l-a]}else if(c==2)for(;l<o;l++)e[f+l]=e[u+l]+e[f+l-o];else if(c==3){for(;l<a;l++)e[f+l]=e[u+l]+(e[f+l-o]>>>1);for(;l<o;l++)e[f+l]=e[u+l]+(e[f+l-o]+e[f+l-a]>>>1)}else{for(;l<a;l++)e[f+l]=e[u+l]+s(0,e[f+l-o],0);for(;l<o;l++)e[f+l]=e[u+l]+s(e[f+l-a],e[f+l-o],e[f+l-a-o])}return e};R.decode._paeth=function(e,t,r){var n=e+t-r,i=n-e,a=n-t,o=n-r;return i*i<=a*a&&i*i<=o*o?e:a*a<=o*o?t:r};R.decode._IHDR=function(e,t,r){var n=R._bin;r.width=n.readUint(e,t),t+=4,r.height=n.readUint(e,t),t+=4,r.depth=e[t],t++,r.ctype=e[t],t++,r.compress=e[t],t++,r.filter=e[t],t++,r.interlace=e[t],t++};R._bin={nextZero:function(e,t){for(;e[t]!=0;)t++;return t},readUshort:function(e,t){return e[t]<<8|e[t+1]},writeUshort:function(e,t,r){e[t]=r>>8&255,e[t+1]=r&255},readUint:function(e,t){return e[t]*(256*256*256)+(e[t+1]<<16|e[t+2]<<8|e[t+3])},writeUint:function(e,t,r){e[t]=r>>24&255,e[t+1]=r>>16&255,e[t+2]=r>>8&255,e[t+3]=r&255},readASCII:function(e,t,r){for(var n="",i=0;i<r;i++)n+=String.fromCharCode(e[t+i]);return n},writeASCII:function(e,t,r){for(var n=0;n<r.length;n++)e[t+n]=r.charCodeAt(n)},readBytes:function(e,t,r){for(var n=[],i=0;i<r;i++)n.push(e[t+i]);return n},pad:function(e){return e.length<2?"0"+e:e},readUTF8:function(e,t,r){for(var n="",i,a=0;a<r;a++)n+="%"+R._bin.pad(e[t+a].toString(16));try{i=decodeURIComponent(n)}catch{return R._bin.readASCII(e,t,r)}return i}};R._copyTile=function(e,t,r,n,i,a,o,s,f){for(var u=Math.min(t,i),c=Math.min(r,a),l=0,h=0,d=0;d<c;d++)for(var v=0;v<u;v++)if(o>=0&&s>=0?(l=d*t+v<<2,h=(s+d)*i+o+v<<2):(l=(-s+d)*t-o+v<<2,h=d*i+v<<2),f==0)n[h]=e[l],n[h+1]=e[l+1],n[h+2]=e[l+2],n[h+3]=e[l+3];else if(f==1){var g=e[l+3]*.00392156862745098,m=e[l]*g,b=e[l+1]*g,F=e[l+2]*g,w=n[h+3]*(1/255),C=n[h]*w,S=n[h+1]*w,k=n[h+2]*w,T=1-g,P=g+w*T,D=P==0?0:1/P;n[h+3]=255*P,n[h+0]=(m+C*T)*D,n[h+1]=(b+S*T)*D,n[h+2]=(F+k*T)*D}else if(f==2){var g=e[l+3],m=e[l],b=e[l+1],F=e[l+2],w=n[h+3],C=n[h],S=n[h+1],k=n[h+2];g==w&&m==C&&b==S&&F==k?(n[h]=0,n[h+1]=0,n[h+2]=0,n[h+3]=0):(n[h]=m,n[h+1]=b,n[h+2]=F,n[h+3]=g)}else if(f==3){var g=e[l+3],m=e[l],b=e[l+1],F=e[l+2],w=n[h+3],C=n[h],S=n[h+1],k=n[h+2];if(g==w&&m==C&&b==S&&F==k)continue;if(g<220&&w>20)return!1}return!0};R.encode=function(e,t,r,n,i,a,o){n==null&&(n=0),o==null&&(o=!1);var s=R.encode.compress(e,t,r,n,[!1,!1,!1,0,o]);return R.encode.compressPNG(s,-1),R.encode._main(s,t,r,i,a)};R.encodeLL=function(e,t,r,n,i,a,o,s){for(var f={ctype:0+(n==1?0:2)+(i==0?0:4),depth:a,frames:[]},u=(n+i)*a,c=u*t,l=0;l<e.length;l++)f.frames.push({rect:{x:0,y:0,width:t,height:r},img:new Uint8Array(e[l]),blend:0,dispose:1,bpp:Math.ceil(u/8),bpl:Math.ceil(c/8)});R.encode.compressPNG(f,0,!0);var h=R.encode._main(f,t,r,o,s);return h};R.encode._main=function(e,t,r,n,i){i==null&&(i={});var a=R.crc.crc,o=R._bin.writeUint,s=R._bin.writeUshort,f=R._bin.writeASCII,u=8,c=e.frames.length>1,l=!1,h=8+(16+5+4)+(c?20:0);if(i.sRGB!=null&&(h+=8+1+4),i.pHYs!=null&&(h+=8+9+4),e.ctype==3){for(var d=e.plte.length,v=0;v<d;v++)e.plte[v]>>>24!=255&&(l=!0);h+=8+d*3+4+(l?8+d*1+4:0)}for(var g=0;g<e.frames.length;g++){var m=e.frames[g];c&&(h+=38),h+=m.cimg.length+12,g!=0&&(h+=4)}h+=12;for(var b=new Uint8Array(h),F=[137,80,78,71,13,10,26,10],v=0;v<8;v++)b[v]=F[v];if(o(b,u,13),u+=4,f(b,u,"IHDR"),u+=4,o(b,u,t),u+=4,o(b,u,r),u+=4,b[u]=e.depth,u++,b[u]=e.ctype,u++,b[u]=0,u++,b[u]=0,u++,b[u]=0,u++,o(b,u,a(b,u-17,17)),u+=4,i.sRGB!=null&&(o(b,u,1),u+=4,f(b,u,"sRGB"),u+=4,b[u]=i.sRGB,u++,o(b,u,a(b,u-5,5)),u+=4),i.pHYs!=null&&(o(b,u,9),u+=4,f(b,u,"pHYs"),u+=4,o(b,u,i.pHYs[0]),u+=4,o(b,u,i.pHYs[1]),u+=4,b[u]=i.pHYs[2],u++,o(b,u,a(b,u-13,13)),u+=4),c&&(o(b,u,8),u+=4,f(b,u,"acTL"),u+=4,o(b,u,e.frames.length),u+=4,o(b,u,i.loop!=null?i.loop:0),u+=4,o(b,u,a(b,u-12,12)),u+=4),e.ctype==3){var d=e.plte.length;o(b,u,d*3),u+=4,f(b,u,"PLTE"),u+=4;for(var v=0;v<d;v++){var w=v*3,C=e.plte[v],S=C&255,k=C>>>8&255,T=C>>>16&255;b[u+w+0]=S,b[u+w+1]=k,b[u+w+2]=T}if(u+=d*3,o(b,u,a(b,u-d*3-4,d*3+4)),u+=4,l){o(b,u,d),u+=4,f(b,u,"tRNS"),u+=4;for(var v=0;v<d;v++)b[u+v]=e.plte[v]>>>24&255;u+=d,o(b,u,a(b,u-d-4,d+4)),u+=4}}for(var P=0,g=0;g<e.frames.length;g++){var m=e.frames[g];c&&(o(b,u,26),u+=4,f(b,u,"fcTL"),u+=4,o(b,u,P++),u+=4,o(b,u,m.rect.width),u+=4,o(b,u,m.rect.height),u+=4,o(b,u,m.rect.x),u+=4,o(b,u,m.rect.y),u+=4,s(b,u,n[g]),u+=2,s(b,u,1e3),u+=2,b[u]=m.dispose,u++,b[u]=m.blend,u++,o(b,u,a(b,u-30,30)),u+=4);var D=m.cimg,d=D.length;o(b,u,d+(g==0?0:4)),u+=4;var N=u;f(b,u,g==0?"IDAT":"fdAT"),u+=4,g!=0&&(o(b,u,P++),u+=4),b.set(D,u),u+=d,o(b,u,a(b,N,u-N)),u+=4}return o(b,u,0),u+=4,f(b,u,"IEND"),u+=4,o(b,u,a(b,u-4,4)),u+=4,b.buffer};R.encode.compressPNG=function(e,t,r){for(var n=0;n<e.frames.length;n++){var i=e.frames[n];i.rect.width;var a=i.rect.height,o=new Uint8Array(a*i.bpl+a);i.cimg=R.encode._filterZero(i.img,a,i.bpp,i.bpl,o,t,r)}};R.encode.compress=function(e,t,r,n,i){for(var a=i[0],o=i[1],s=i[2],f=i[3],u=i[4],c=6,l=8,h=255,d=0;d<e.length;d++)for(var v=new Uint8Array(e[d]),g=v.length,m=0;m<g;m+=4)h&=v[m+3];var b=h!=255,F=R.encode.framize(e,t,r,a,o,s),w={},C=[],S=[];if(n!=0){for(var k=[],m=0;m<F.length;m++)k.push(F[m].img.buffer);for(var T=R.encode.concatRGBA(k),P=R.quantize(T,n),D=0,N=new Uint8Array(P.abuf),m=0;m<F.length;m++){var A=F[m].img,z=A.length;S.push(new Uint8Array(P.inds.buffer,D>>2,z>>2));for(var d=0;d<z;d+=4)A[d]=N[D+d],A[d+1]=N[D+d+1],A[d+2]=N[D+d+2],A[d+3]=N[D+d+3];D+=z}for(var m=0;m<P.plte.length;m++)C.push(P.plte[m].est.rgba)}else for(var d=0;d<F.length;d++){var B=F[d],W=new Uint32Array(B.img.buffer),L=B.rect.width,g=W.length,j=new Uint8Array(g);S.push(j);for(var m=0;m<g;m++){var ie=W[m];if(m!=0&&ie==W[m-1])j[m]=j[m-1];else if(m>L&&ie==W[m-L])j[m]=j[m-L];else{var Fe=w[ie];if(Fe==null&&(w[ie]=Fe=C.length,C.push(ie),C.length>=300))break;j[m]=Fe}}}var Oe=C.length;Oe<=256&&u==!1&&(Oe<=2?l=1:Oe<=4?l=2:Oe<=16?l=4:l=8,l=Math.max(l,f));for(var d=0;d<F.length;d++){var B=F[d];B.rect.x,B.rect.y;var L=B.rect.width,xt=B.rect.height,Ee=B.img;new Uint32Array(Ee.buffer);var Ve=4*L,Ti=4;if(Oe<=256&&u==!1){Ve=Math.ceil(l*L/8);for(var nt=new Uint8Array(Ve*xt),pn=S[d],gn=0;gn<xt;gn++){var m=gn*Ve,yn=gn*L;if(l==8)for(var ve=0;ve<L;ve++)nt[m+ve]=pn[yn+ve];else if(l==4)for(var ve=0;ve<L;ve++)nt[m+(ve>>1)]|=pn[yn+ve]<<4-(ve&1)*4;else if(l==2)for(var ve=0;ve<L;ve++)nt[m+(ve>>2)]|=pn[yn+ve]<<6-(ve&3)*2;else if(l==1)for(var ve=0;ve<L;ve++)nt[m+(ve>>3)]|=pn[yn+ve]<<7-(ve&7)*1}Ee=nt,c=3,Ti=1}else if(b==!1&&F.length==1){for(var nt=new Uint8Array(L*xt*3),Zu=L*xt,m=0;m<Zu;m++){var A=m*3,Pi=m*4;nt[A]=Ee[Pi],nt[A+1]=Ee[Pi+1],nt[A+2]=Ee[Pi+2]}Ee=nt,c=2,Ti=3,Ve=3*L}B.img=Ee,B.bpl=Ve,B.bpp=Ti}return{ctype:c,depth:l,plte:C,frames:F}};R.encode.framize=function(e,t,r,n,i,a){for(var o=[],s=0;s<e.length;s++){var f=new Uint8Array(e[s]),u=new Uint32Array(f.buffer),c,l=0,h=0,d=t,v=r,g=n?1:0;if(s!=0){for(var m=a||n||s==1||o[s-2].dispose!=0?1:2,b=0,F=1e9,w=0;w<m;w++){for(var B=new Uint8Array(e[s-1-w]),C=new Uint32Array(e[s-1-w]),S=t,k=r,T=-1,P=-1,D=0;D<r;D++)for(var N=0;N<t;N++){var A=D*t+N;u[A]!=C[A]&&(N<S&&(S=N),N>T&&(T=N),D<k&&(k=D),D>P&&(P=D))}T==-1&&(S=k=T=P=0),i&&((S&1)==1&&S--,(k&1)==1&&k--);var z=(T-S+1)*(P-k+1);z<F&&(F=z,b=w,l=S,h=k,d=T-S+1,v=P-k+1)}var B=new Uint8Array(e[s-1-b]);b==1&&(o[s-1].dispose=2),c=new Uint8Array(d*v*4),R._copyTile(B,t,r,c,d,v,-l,-h,0),g=R._copyTile(f,t,r,c,d,v,-l,-h,3)?1:0,g==1?R.encode._prepareDiff(f,t,r,c,{x:l,y:h,width:d,height:v}):R._copyTile(f,t,r,c,d,v,-l,-h,0)}else c=f.slice(0);o.push({rect:{x:l,y:h,width:d,height:v},img:c,blend:g,dispose:0})}if(n)for(var s=0;s<o.length;s++){var W=o[s];if(W.blend!=1){var L=W.rect,j=o[s-1].rect,ie=Math.min(L.x,j.x),Fe=Math.min(L.y,j.y),Oe=Math.max(L.x+L.width,j.x+j.width),xt=Math.max(L.y+L.height,j.y+j.height),Ee={x:ie,y:Fe,width:Oe-ie,height:xt-Fe};o[s-1].dispose=1,s-1!=0&&R.encode._updateFrame(e,t,r,o,s-1,Ee,i),R.encode._updateFrame(e,t,r,o,s,Ee,i)}}var Ve=0;if(e.length!=1)for(var A=0;A<o.length;A++){var W=o[A];Ve+=W.rect.width*W.rect.height}return o};R.encode._updateFrame=function(e,t,r,n,i,a,o){for(var s=Uint8Array,f=Uint32Array,u=new s(e[i-1]),c=new f(e[i-1]),l=i+1<e.length?new s(e[i+1]):null,h=new s(e[i]),d=new f(h.buffer),v=t,g=r,m=-1,b=-1,F=0;F<a.height;F++)for(var w=0;w<a.width;w++){var C=a.x+w,S=a.y+F,k=S*t+C,T=d[k];T==0||n[i-1].dispose==0&&c[k]==T&&(l==null||l[k*4+3]!=0)||(C<v&&(v=C),C>m&&(m=C),S<g&&(g=S),S>b&&(b=S))}m==-1&&(v=g=m=b=0),o&&((v&1)==1&&v--,(g&1)==1&&g--),a={x:v,y:g,width:m-v+1,height:b-g+1};var P=n[i];P.rect=a,P.blend=1,P.img=new Uint8Array(a.width*a.height*4),n[i-1].dispose==0?(R._copyTile(u,t,r,P.img,a.width,a.height,-a.x,-a.y,0),R.encode._prepareDiff(h,t,r,P.img,a)):R._copyTile(h,t,r,P.img,a.width,a.height,-a.x,-a.y,0)};R.encode._prepareDiff=function(e,t,r,n,i){R._copyTile(e,t,r,n,i.width,i.height,-i.x,-i.y,2)};R.encode._filterZero=function(e,t,r,n,i,a,o){var s=[],f=[0,1,2,3,4];a!=-1?f=[a]:(t*n>5e5||r==1)&&(f=[0]);var u;o&&(u={level:0});for(var c=o&&UZIP!=null?UZIP:ci,l=0;l<f.length;l++){for(var h=0;h<t;h++)R.encode._filterLine(i,e,h,n,r,f[l]);s.push(c.deflate(i,u))}for(var d,v=1e9,l=0;l<s.length;l++)s[l].length<v&&(d=l,v=s[l].length);return s[d]};R.encode._filterLine=function(e,t,r,n,i,a){var o=r*n,s=o+r,f=R.decode._paeth;if(e[s]=a,s++,a==0)if(n<500)for(var u=0;u<n;u++)e[s+u]=t[o+u];else e.set(new Uint8Array(t.buffer,o,n),s);else if(a==1){for(var u=0;u<i;u++)e[s+u]=t[o+u];for(var u=i;u<n;u++)e[s+u]=t[o+u]-t[o+u-i]+256&255}else if(r==0){for(var u=0;u<i;u++)e[s+u]=t[o+u];if(a==2)for(var u=i;u<n;u++)e[s+u]=t[o+u];if(a==3)for(var u=i;u<n;u++)e[s+u]=t[o+u]-(t[o+u-i]>>1)+256&255;if(a==4)for(var u=i;u<n;u++)e[s+u]=t[o+u]-f(t[o+u-i],0,0)+256&255}else{if(a==2)for(var u=0;u<n;u++)e[s+u]=t[o+u]+256-t[o+u-n]&255;if(a==3){for(var u=0;u<i;u++)e[s+u]=t[o+u]+256-(t[o+u-n]>>1)&255;for(var u=i;u<n;u++)e[s+u]=t[o+u]+256-(t[o+u-n]+t[o+u-i]>>1)&255}if(a==4){for(var u=0;u<i;u++)e[s+u]=t[o+u]+256-f(0,t[o+u-n],0)&255;for(var u=i;u<n;u++)e[s+u]=t[o+u]+256-f(t[o+u-i],t[o+u-n],t[o+u-i-n])&255}}};R.crc={table:function(){for(var e=new Uint32Array(256),t=0;t<256;t++){for(var r=t,n=0;n<8;n++)r&1?r=3988292384^r>>>1:r=r>>>1;e[t]=r}return e}(),update:function(e,t,r,n){for(var i=0;i<n;i++)e=R.crc.table[(e^t[r+i])&255]^e>>>8;return e},crc:function(e,t,r){return R.crc.update(4294967295,e,t,r)^4294967295}};R.quantize=function(e,t){var r=new Uint8Array(e),n=r.slice(0),i=new Uint32Array(n.buffer),a=R.quantize.getKDtree(n,t),o=a[0],s=a[1];R.quantize.planeDst;for(var f=r,u=i,c=f.length,l=new Uint8Array(r.length>>2),h=0;h<c;h+=4){var d=f[h]*.00392156862745098,v=f[h+1]*(1/255),g=f[h+2]*(1/255),m=f[h+3]*(1/255),b=R.quantize.getNearest(o,d,v,g,m);l[h>>2]=b.ind,u[h>>2]=b.est.rgba}return{abuf:n.buffer,inds:l,plte:s}};R.quantize.getKDtree=function(e,t,r){r==null&&(r=1e-4);var n=new Uint32Array(e.buffer),i={i0:0,i1:e.length,bst:null,est:null,tdst:0,left:null,right:null};i.bst=R.quantize.stats(e,i.i0,i.i1),i.est=R.quantize.estats(i.bst);for(var a=[i];a.length<t;){for(var o=0,s=0,f=0;f<a.length;f++)a[f].est.L>o&&(o=a[f].est.L,s=f);if(o<r)break;var u=a[s],c=R.quantize.splitPixels(e,n,u.i0,u.i1,u.est.e,u.est.eMq255),l=u.i0>=c||u.i1<=c;if(l){u.est.L=0;continue}var h={i0:u.i0,i1:c,bst:null,est:null,tdst:0,left:null,right:null};h.bst=R.quantize.stats(e,h.i0,h.i1),h.est=R.quantize.estats(h.bst);var d={i0:c,i1:u.i1,bst:null,est:null,tdst:0,left:null,right:null};d.bst={R:[],m:[],N:u.bst.N-h.bst.N};for(var f=0;f<16;f++)d.bst.R[f]=u.bst.R[f]-h.bst.R[f];for(var f=0;f<4;f++)d.bst.m[f]=u.bst.m[f]-h.bst.m[f];d.est=R.quantize.estats(d.bst),u.left=h,u.right=d,a[s]=h,a.push(d)}a.sort(function(v,g){return g.bst.N-v.bst.N});for(var f=0;f<a.length;f++)a[f].ind=f;return[i,a]};R.quantize.getNearest=function(e,t,r,n,i){if(e.left==null)return e.tdst=R.quantize.dist(e.est.q,t,r,n,i),e;var a=R.quantize.planeDst(e.est,t,r,n,i),o=e.left,s=e.right;a>0&&(o=e.right,s=e.left);var f=R.quantize.getNearest(o,t,r,n,i);if(f.tdst<=a*a)return f;var u=R.quantize.getNearest(s,t,r,n,i);return u.tdst<f.tdst?u:f};R.quantize.planeDst=function(e,t,r,n,i){var a=e.e;return a[0]*t+a[1]*r+a[2]*n+a[3]*i-e.eMq};R.quantize.dist=function(e,t,r,n,i){var a=t-e[0],o=r-e[1],s=n-e[2],f=i-e[3];return a*a+o*o+s*s+f*f};R.quantize.splitPixels=function(e,t,r,n,i,a){var o=R.quantize.vecDot;for(n-=4;r<n;){for(;o(e,r,i)<=a;)r+=4;for(;o(e,n,i)>a;)n-=4;if(r>=n)break;var s=t[r>>2];t[r>>2]=t[n>>2],t[n>>2]=s,r+=4,n-=4}for(;o(e,r,i)>a;)r-=4;return r+4};R.quantize.vecDot=function(e,t,r){return e[t]*r[0]+e[t+1]*r[1]+e[t+2]*r[2]+e[t+3]*r[3]};R.quantize.stats=function(e,t,r){for(var n=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],i=[0,0,0,0],a=r-t>>2,o=t;o<r;o+=4){var s=e[o]*.00392156862745098,f=e[o+1]*(1/255),u=e[o+2]*(1/255),c=e[o+3]*(1/255);i[0]+=s,i[1]+=f,i[2]+=u,i[3]+=c,n[0]+=s*s,n[1]+=s*f,n[2]+=s*u,n[3]+=s*c,n[5]+=f*f,n[6]+=f*u,n[7]+=f*c,n[10]+=u*u,n[11]+=u*c,n[15]+=c*c}return n[4]=n[1],n[8]=n[2],n[9]=n[6],n[12]=n[3],n[13]=n[7],n[14]=n[11],{R:n,m:i,N:a}};R.quantize.estats=function(e){var t=e.R,r=e.m,n=e.N,i=r[0],a=r[1],o=r[2],s=r[3],f=n==0?0:1/n,u=[t[0]-i*i*f,t[1]-i*a*f,t[2]-i*o*f,t[3]-i*s*f,t[4]-a*i*f,t[5]-a*a*f,t[6]-a*o*f,t[7]-a*s*f,t[8]-o*i*f,t[9]-o*a*f,t[10]-o*o*f,t[11]-o*s*f,t[12]-s*i*f,t[13]-s*a*f,t[14]-s*o*f,t[15]-s*s*f],c=u,l=R.M4,h=[.5,.5,.5,.5],d=0,v=0;if(n!=0)for(var g=0;g<10&&(h=l.multVec(c,h),v=Math.sqrt(l.dot(h,h)),h=l.sml(1/v,h),!(Math.abs(v-d)<1e-9));g++)d=v;var m=[i*f,a*f,o*f,s*f],b=l.dot(l.sml(255,m),h);return{Cov:u,q:m,e:h,L:d,eMq255:b,eMq:l.dot(h,m),rgba:(Math.round(255*m[3])<<24|Math.round(255*m[2])<<16|Math.round(255*m[1])<<8|Math.round(255*m[0])<<0)>>>0}};R.M4={multVec:function(e,t){return[e[0]*t[0]+e[1]*t[1]+e[2]*t[2]+e[3]*t[3],e[4]*t[0]+e[5]*t[1]+e[6]*t[2]+e[7]*t[3],e[8]*t[0]+e[9]*t[1]+e[10]*t[2]+e[11]*t[3],e[12]*t[0]+e[13]*t[1]+e[14]*t[2]+e[15]*t[3]]},dot:function(e,t){return e[0]*t[0]+e[1]*t[1]+e[2]*t[2]+e[3]*t[3]},sml:function(e,t){return[e*t[0],e*t[1],e*t[2],e*t[3]]}};R.encode.concatRGBA=function(e){for(var t=0,r=0;r<e.length;r++)t+=e[r].byteLength;for(var n=new Uint8Array(t),i=0,r=0;r<e.length;r++){for(var a=new Uint8Array(e[r]),o=a.length,s=0;s<o;s+=4){var f=a[s],u=a[s+1],c=a[s+2],l=a[s+3];l==0&&(f=u=c=0),n[i+s]=f,n[i+s+1]=u,n[i+s+2]=c,n[i+s+3]=l}i+=o}return n.buffer};var Fh=function(e){if(e===0)return Ut.Greyscale;if(e===2)return Ut.Truecolour;if(e===3)return Ut.IndexedColour;if(e===4)return Ut.GreyscaleWithAlpha;if(e===6)return Ut.TruecolourWithAlpha;throw new Error("Unknown color type: "+e)},kh=function(e){for(var t=Math.floor(e.length/4),r=new Uint8Array(t*3),n=new Uint8Array(t*1),i=0,a=0,o=0;i<e.length;)r[a++]=e[i++],r[a++]=e[i++],r[a++]=e[i++],n[o++]=e[i++];return{rgbChannel:r,alphaChannel:n}},Ut;(function(e){e.Greyscale="Greyscale",e.Truecolour="Truecolour",e.IndexedColour="IndexedColour",e.GreyscaleWithAlpha="GreyscaleWithAlpha",e.TruecolourWithAlpha="TruecolourWithAlpha"})(Ut||(Ut={}));var Ch=function(){function e(t){var r=R.decode(t),n=R.toRGBA8(r);if(n.length>1)throw new Error("Animated PNGs are not supported");var i=new Uint8Array(n[0]),a=kh(i),o=a.rgbChannel,s=a.alphaChannel;this.rgbChannel=o;var f=s.some(function(u){return u<255});f&&(this.alphaChannel=s),this.type=Fh(r.ctype),this.width=r.width,this.height=r.height,this.bitsPerComponent=8}return e.load=function(t){return new e(t)},e}(),pu=function(){function e(t){this.image=t,this.bitsPerComponent=t.bitsPerComponent,this.width=t.width,this.height=t.height,this.colorSpace="DeviceRGB"}return e.for=function(t){return Y(this,void 0,void 0,function(){var r;return J(this,function(n){return r=Ch.load(t),[2,new e(r)]})})},e.prototype.embedIntoContext=function(t,r){return Y(this,void 0,void 0,function(){var n,i;return J(this,function(a){return n=this.embedAlphaChannel(t),i=t.flateStream(this.image.rgbChannel,{Type:"XObject",Subtype:"Image",BitsPerComponent:this.image.bitsPerComponent,Width:this.image.width,Height:this.image.height,ColorSpace:this.colorSpace,SMask:n}),r?(t.assign(r,i),[2,r]):[2,t.register(i)]})})},e.prototype.embedAlphaChannel=function(t){if(this.image.alphaChannel){var r=t.flateStream(this.image.alphaChannel,{Type:"XObject",Subtype:"Image",Height:this.image.height,Width:this.image.width,BitsPerComponent:this.image.bitsPerComponent,ColorSpace:"DeviceGray",Decode:[0,1]});return t.register(r)}},e}(),gu=function(){function e(t,r,n){this.bytes=t,this.start=r||0,this.pos=this.start,this.end=r&&n?r+n:this.bytes.length}return Object.defineProperty(e.prototype,"length",{get:function(){return this.end-this.start},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isEmpty",{get:function(){return this.length===0},enumerable:!1,configurable:!0}),e.prototype.getByte=function(){return this.pos>=this.end?-1:this.bytes[this.pos++]},e.prototype.getUint16=function(){var t=this.getByte(),r=this.getByte();return t===-1||r===-1?-1:(t<<8)+r},e.prototype.getInt32=function(){var t=this.getByte(),r=this.getByte(),n=this.getByte(),i=this.getByte();return(t<<24)+(r<<16)+(n<<8)+i},e.prototype.getBytes=function(t,r){r===void 0&&(r=!1);var n=this.bytes,i=this.pos,a=this.end;if(t){var s=i+t;s>a&&(s=a),this.pos=s;var o=n.subarray(i,s);return r?new Uint8ClampedArray(o):o}else{var o=n.subarray(i,a);return r?new Uint8ClampedArray(o):o}},e.prototype.peekByte=function(){var t=this.getByte();return this.pos--,t},e.prototype.peekBytes=function(t,r){r===void 0&&(r=!1);var n=this.getBytes(t,r);return this.pos-=n.length,n},e.prototype.skip=function(t){t||(t=1),this.pos+=t},e.prototype.reset=function(){this.pos=this.start},e.prototype.moveStart=function(){this.start=this.pos},e.prototype.makeSubStream=function(t,r){return new e(this.bytes,t,r)},e.prototype.decode=function(){return this.bytes},e}(),Th=new Uint8Array(0),ln=function(){function e(t){if(this.pos=0,this.bufferLength=0,this.eof=!1,this.buffer=Th,this.minBufferLength=512,t)for(;this.minBufferLength<t;)this.minBufferLength*=2}return Object.defineProperty(e.prototype,"isEmpty",{get:function(){for(;!this.eof&&this.bufferLength===0;)this.readBlock();return this.bufferLength===0},enumerable:!1,configurable:!0}),e.prototype.getByte=function(){for(var t=this.pos;this.bufferLength<=t;){if(this.eof)return-1;this.readBlock()}return this.buffer[this.pos++]},e.prototype.getUint16=function(){var t=this.getByte(),r=this.getByte();return t===-1||r===-1?-1:(t<<8)+r},e.prototype.getInt32=function(){var t=this.getByte(),r=this.getByte(),n=this.getByte(),i=this.getByte();return(t<<24)+(r<<16)+(n<<8)+i},e.prototype.getBytes=function(t,r){r===void 0&&(r=!1);var n,i=this.pos;if(t){for(this.ensureBuffer(i+t),n=i+t;!this.eof&&this.bufferLength<n;)this.readBlock();var a=this.bufferLength;n>a&&(n=a)}else{for(;!this.eof;)this.readBlock();n=this.bufferLength}this.pos=n;var o=this.buffer.subarray(i,n);return r&&!(o instanceof Uint8ClampedArray)?new Uint8ClampedArray(o):o},e.prototype.peekByte=function(){var t=this.getByte();return this.pos--,t},e.prototype.peekBytes=function(t,r){r===void 0&&(r=!1);var n=this.getBytes(t,r);return this.pos-=n.length,n},e.prototype.skip=function(t){t||(t=1),this.pos+=t},e.prototype.reset=function(){this.pos=0},e.prototype.makeSubStream=function(t,r){for(var n=t+r;this.bufferLength<=n&&!this.eof;)this.readBlock();return new gu(this.buffer,t,r)},e.prototype.decode=function(){for(;!this.eof;)this.readBlock();return this.buffer.subarray(0,this.bufferLength)},e.prototype.readBlock=function(){throw new Le(this.constructor.name,"readBlock")},e.prototype.ensureBuffer=function(t){var r=this.buffer;if(t<=r.byteLength)return r;for(var n=this.minBufferLength;n<t;)n*=2;var i=new Uint8Array(n);return i.set(r),this.buffer=i},e}(),Xo=function(e){return e===32||e===9||e===13||e===10},Ph=function(e){E(t,e);function t(r,n){var i=e.call(this,n)||this;return i.stream=r,i.input=new Uint8Array(5),n&&(n=.8*n),i}return t.prototype.readBlock=function(){for(var r=126,n=122,i=-1,a=this.stream,o=a.getByte();Xo(o);)o=a.getByte();if(o===i||o===r){this.eof=!0;return}var s=this.bufferLength,f,u;if(o===n){for(f=this.ensureBuffer(s+4),u=0;u<4;++u)f[s+u]=0;this.bufferLength+=4}else{var c=this.input;for(c[0]=o,u=1;u<5;++u){for(o=a.getByte();Xo(o);)o=a.getByte();if(c[u]=o,o===i||o===r)break}if(f=this.ensureBuffer(s+u-1),this.bufferLength+=u-1,u<5){for(;u<5;++u)c[u]=33+84;this.eof=!0}var l=0;for(u=0;u<5;++u)l=l*85+(c[u]-33);for(u=3;u>=0;--u)f[s+u]=l&255,l>>=8}},t}(ln),Ah=function(e){E(t,e);function t(r,n){var i=e.call(this,n)||this;return i.stream=r,i.firstDigit=-1,n&&(n=.5*n),i}return t.prototype.readBlock=function(){var r=8e3,n=this.stream.getBytes(r);if(!n.length){this.eof=!0;return}for(var i=n.length+1>>1,a=this.ensureBuffer(this.bufferLength+i),o=this.bufferLength,s=this.firstDigit,f=0,u=n.length;f<u;f++){var c=n[f],l=void 0;if(c>=48&&c<=57)l=c&15;else if(c>=65&&c<=70||c>=97&&c<=102)l=(c&15)+9;else if(c===62){this.eof=!0;break}else continue;s<0?s=l:(a[o++]=s<<4|l,s=-1)}s>=0&&this.eof&&(a[o++]=s<<4,s=-1),this.firstDigit=s,this.bufferLength=o},t}(ln),Zo=new Int32Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),Dh=new Int32Array([3,4,5,6,7,8,9,10,65547,65549,65551,65553,131091,131095,131099,131103,196643,196651,196659,196667,262211,262227,262243,262259,327811,327843,327875,327907,258,258,258]),Rh=new Int32Array([1,2,3,4,65541,65543,131081,131085,196625,196633,262177,262193,327745,327777,393345,393409,459009,459137,524801,525057,590849,591361,657409,658433,724993,727041,794625,798721,868353,876545]),Oh=[new Int32Array([459008,524368,524304,524568,459024,524400,524336,590016,459016,524384,524320,589984,524288,524416,524352,590048,459012,524376,524312,589968,459028,524408,524344,590032,459020,524392,524328,59e4,524296,524424,524360,590064,459010,524372,524308,524572,459026,524404,524340,590024,459018,524388,524324,589992,524292,524420,524356,590056,459014,524380,524316,589976,459030,524412,524348,590040,459022,524396,524332,590008,524300,524428,524364,590072,459009,524370,524306,524570,459025,524402,524338,590020,459017,524386,524322,589988,524290,524418,524354,590052,459013,524378,524314,589972,459029,524410,524346,590036,459021,524394,524330,590004,524298,524426,524362,590068,459011,524374,524310,524574,459027,524406,524342,590028,459019,524390,524326,589996,524294,524422,524358,590060,459015,524382,524318,589980,459031,524414,524350,590044,459023,524398,524334,590012,524302,524430,524366,590076,459008,524369,524305,524569,459024,524401,524337,590018,459016,524385,524321,589986,524289,524417,524353,590050,459012,524377,524313,589970,459028,524409,524345,590034,459020,524393,524329,590002,524297,524425,524361,590066,459010,524373,524309,524573,459026,524405,524341,590026,459018,524389,524325,589994,524293,524421,524357,590058,459014,524381,524317,589978,459030,524413,524349,590042,459022,524397,524333,590010,524301,524429,524365,590074,459009,524371,524307,524571,459025,524403,524339,590022,459017,524387,524323,589990,524291,524419,524355,590054,459013,524379,524315,589974,459029,524411,524347,590038,459021,524395,524331,590006,524299,524427,524363,590070,459011,524375,524311,524575,459027,524407,524343,590030,459019,524391,524327,589998,524295,524423,524359,590062,459015,524383,524319,589982,459031,524415,524351,590046,459023,524399,524335,590014,524303,524431,524367,590078,459008,524368,524304,524568,459024,524400,524336,590017,459016,524384,524320,589985,524288,524416,524352,590049,459012,524376,524312,589969,459028,524408,524344,590033,459020,524392,524328,590001,524296,524424,524360,590065,459010,524372,524308,524572,459026,524404,524340,590025,459018,524388,524324,589993,524292,524420,524356,590057,459014,524380,524316,589977,459030,524412,524348,590041,459022,524396,524332,590009,524300,524428,524364,590073,459009,524370,524306,524570,459025,524402,524338,590021,459017,524386,524322,589989,524290,524418,524354,590053,459013,524378,524314,589973,459029,524410,524346,590037,459021,524394,524330,590005,524298,524426,524362,590069,459011,524374,524310,524574,459027,524406,524342,590029,459019,524390,524326,589997,524294,524422,524358,590061,459015,524382,524318,589981,459031,524414,524350,590045,459023,524398,524334,590013,524302,524430,524366,590077,459008,524369,524305,524569,459024,524401,524337,590019,459016,524385,524321,589987,524289,524417,524353,590051,459012,524377,524313,589971,459028,524409,524345,590035,459020,524393,524329,590003,524297,524425,524361,590067,459010,524373,524309,524573,459026,524405,524341,590027,459018,524389,524325,589995,524293,524421,524357,590059,459014,524381,524317,589979,459030,524413,524349,590043,459022,524397,524333,590011,524301,524429,524365,590075,459009,524371,524307,524571,459025,524403,524339,590023,459017,524387,524323,589991,524291,524419,524355,590055,459013,524379,524315,589975,459029,524411,524347,590039,459021,524395,524331,590007,524299,524427,524363,590071,459011,524375,524311,524575,459027,524407,524343,590031,459019,524391,524327,589999,524295,524423,524359,590063,459015,524383,524319,589983,459031,524415,524351,590047,459023,524399,524335,590015,524303,524431,524367,590079]),9],Eh=[new Int32Array([327680,327696,327688,327704,327684,327700,327692,327708,327682,327698,327690,327706,327686,327702,327694,0,327681,327697,327689,327705,327685,327701,327693,327709,327683,327699,327691,327707,327687,327703,327695,0]),5],Bh=function(e){E(t,e);function t(r,n){var i=e.call(this,n)||this;i.stream=r;var a=r.getByte(),o=r.getByte();if(a===-1||o===-1)throw new Error("Invalid header in flate stream: "+a+", "+o);if((a&15)!==8)throw new Error("Unknown compression method in flate stream: "+a+", "+o);if(((a<<8)+o)%31!==0)throw new Error("Bad FCHECK in flate stream: "+a+", "+o);if(o&32)throw new Error("FDICT bit set in flate stream: "+a+", "+o);return i.codeSize=0,i.codeBuf=0,i}return t.prototype.readBlock=function(){var r,n,i=this.stream,a=this.getBits(3);if(a&1&&(this.eof=!0),a>>=1,a===0){var o=void 0;if((o=i.getByte())===-1)throw new Error("Bad block header in flate stream");var s=o;if((o=i.getByte())===-1)throw new Error("Bad block header in flate stream");if(s|=o<<8,(o=i.getByte())===-1)throw new Error("Bad block header in flate stream");var f=o;if((o=i.getByte())===-1)throw new Error("Bad block header in flate stream");if(f|=o<<8,f!==(~s&65535)&&(s!==0||f!==0))throw new Error("Bad uncompressed block length in flate stream");this.codeBuf=0,this.codeSize=0;var u=this.bufferLength;r=this.ensureBuffer(u+s);var c=u+s;if(this.bufferLength=c,s===0)i.peekByte()===-1&&(this.eof=!0);else for(var l=u;l<c;++l){if((o=i.getByte())===-1){this.eof=!0;break}r[l]=o}return}var h,d;if(a===1)h=Oh,d=Eh;else if(a===2){var v=this.getBits(5)+257,g=this.getBits(5)+1,m=this.getBits(4)+4,b=new Uint8Array(Zo.length),F=void 0;for(F=0;F<m;++F)b[Zo[F]]=this.getBits(3);var w=this.generateHuffmanTable(b);n=0,F=0;for(var C=v+g,S=new Uint8Array(C),k=void 0,T=void 0,P=void 0;F<C;){var D=this.getCode(w);if(D===16)k=2,T=3,P=n;else if(D===17)k=3,T=3,P=n=0;else if(D===18)k=7,T=11,P=n=0;else{S[F++]=n=D;continue}for(var N=this.getBits(k)+T;N-- >0;)S[F++]=P}h=this.generateHuffmanTable(S.subarray(0,v)),d=this.generateHuffmanTable(S.subarray(v,C))}else throw new Error("Unknown block type in flate stream");r=this.buffer;for(var A=r?r.length:0,z=this.bufferLength;;){var B=this.getCode(h);if(B<256){z+1>=A&&(r=this.ensureBuffer(z+1),A=r.length),r[z++]=B;continue}if(B===256){this.bufferLength=z;return}B-=257,B=Dh[B];var W=B>>16;W>0&&(W=this.getBits(W)),n=(B&65535)+W,B=this.getCode(d),B=Rh[B],W=B>>16,W>0&&(W=this.getBits(W));var L=(B&65535)+W;z+n>=A&&(r=this.ensureBuffer(z+n),A=r.length);for(var j=0;j<n;++j,++z)r[z]=r[z-L]}},t.prototype.getBits=function(r){for(var n=this.stream,i=this.codeSize,a=this.codeBuf,o;i<r;){if((o=n.getByte())===-1)throw new Error("Bad encoding in flate stream");a|=o<<i,i+=8}return o=a&(1<<r)-1,this.codeBuf=a>>r,this.codeSize=i-=r,o},t.prototype.getCode=function(r){for(var n=this.stream,i=r[0],a=r[1],o=this.codeSize,s=this.codeBuf,f;o<a&&(f=n.getByte())!==-1;)s|=f<<o,o+=8;var u=i[s&(1<<a)-1];typeof i=="number"&&console.log("FLATE:",u);var c=u>>16,l=u&65535;if(c<1||o<c)throw new Error("Bad encoding in flate stream");return this.codeBuf=s>>c,this.codeSize=o-c,l},t.prototype.generateHuffmanTable=function(r){var n=r.length,i=0,a;for(a=0;a<n;++a)r[a]>i&&(i=r[a]);for(var o=1<<i,s=new Int32Array(o),f=1,u=0,c=2;f<=i;++f,u<<=1,c<<=1)for(var l=0;l<n;++l)if(r[l]===f){var h=0,d=u;for(a=0;a<f;++a)h=h<<1|d&1,d>>=1;for(a=h;a<o;a+=c)s[a]=f<<16|l;++u}return[s,i]},t}(ln),Nh=function(e){E(t,e);function t(r,n,i){var a=e.call(this,n)||this;a.stream=r,a.cachedData=0,a.bitsCached=0;for(var o=4096,s={earlyChange:i,codeLength:9,nextCode:258,dictionaryValues:new Uint8Array(o),dictionaryLengths:new Uint16Array(o),dictionaryPrevCodes:new Uint16Array(o),currentSequence:new Uint8Array(o),currentSequenceLength:0},f=0;f<256;++f)s.dictionaryValues[f]=f,s.dictionaryLengths[f]=1;return a.lzwState=s,a}return t.prototype.readBlock=function(){var r=512,n=r*2,i=r,a,o,s,f=this.lzwState;if(f){var u=f.earlyChange,c=f.nextCode,l=f.dictionaryValues,h=f.dictionaryLengths,d=f.dictionaryPrevCodes,v=f.codeLength,g=f.prevCode,m=f.currentSequence,b=f.currentSequenceLength,F=0,w=this.bufferLength,C=this.ensureBuffer(this.bufferLength+n);for(a=0;a<r;a++){var S=this.readBits(v),k=b>0;if(!S||S<256)m[0]=S,b=1;else if(S>=258)if(S<c)for(b=h[S],o=b-1,s=S;o>=0;o--)m[o]=l[s],s=d[s];else m[b++]=m[0];else if(S===256){v=9,c=258,b=0;continue}else{this.eof=!0,delete this.lzwState;break}if(k&&(d[c]=g,h[c]=h[g]+1,l[c]=m[0],c++,v=c+u&c+u-1?v:Math.min(Math.log(c+u)/.6931471805599453+1,12)|0),g=S,F+=b,n<F){do n+=i;while(n<F);C=this.ensureBuffer(this.bufferLength+n)}for(o=0;o<b;o++)C[w++]=m[o]}f.nextCode=c,f.codeLength=v,f.prevCode=g,f.currentSequenceLength=b,this.bufferLength=w}},t.prototype.readBits=function(r){for(var n=this.bitsCached,i=this.cachedData;n<r;){var a=this.stream.getByte();if(a===-1)return this.eof=!0,null;i=i<<8|a,n+=8}return this.bitsCached=n-=r,this.cachedData=i,i>>>n&(1<<r)-1},t}(ln),zh=function(e){E(t,e);function t(r,n){var i=e.call(this,n)||this;return i.stream=r,i}return t.prototype.readBlock=function(){var r=this.stream.getBytes(2);if(!r||r.length<2||r[0]===128){this.eof=!0;return}var n,i=this.bufferLength,a=r[0];if(a<128){if(n=this.ensureBuffer(i+a+1),n[i++]=r[1],a>0){var o=this.stream.getBytes(a);n.set(o,i),i+=a}}else{a=257-a;var s=r[1];n=this.ensureBuffer(i+a+1);for(var f=0;f<a;f++)n[i++]=s}this.bufferLength=i},t}(ln),Yo=function(e,t,r){if(t===p.of("FlateDecode"))return new Bh(e);if(t===p.of("LZWDecode")){var n=1;if(r instanceof Z){var i=r.lookup(p.of("EarlyChange"));i instanceof q&&(n=i.asNumber())}return new Nh(e,void 0,n)}if(t===p.of("ASCII85Decode"))return new Ph(e);if(t===p.of("ASCIIHexDecode"))return new Ah(e);if(t===p.of("RunLengthDecode"))return new zh(e);throw new Ll(t.asString())},yu=function(e){var t=e.dict,r=e.contents,n=new gu(r),i=t.lookup(p.of("Filter")),a=t.lookup(p.of("DecodeParms"));if(i instanceof p)n=Yo(n,i,a);else if(i instanceof ae)for(var o=0,s=i.size();o<s;o++)n=Yo(n,i.lookup(o,p),a&&a.lookupMaybe(o,Z));else if(i)throw new Zn([p,ae],i);return n},jh=function(e){var t=e.MediaBox(),r=t.lookup(2,q).asNumber()-t.lookup(0,q).asNumber(),n=t.lookup(3,q).asNumber()-t.lookup(1,q).asNumber();return{left:0,bottom:0,right:r,top:n}},Mh=function(e){return[1,0,0,1,-e.left,-e.bottom]},bu=function(){function e(t,r,n){this.page=t;var i=r??jh(t);this.width=i.right-i.left,this.height=i.top-i.bottom,this.boundingBox=i,this.transformationMatrix=n??Mh(i)}return e.for=function(t,r,n){return Y(this,void 0,void 0,function(){return J(this,function(i){return[2,new e(t,r,n)]})})},e.prototype.embedIntoContext=function(t,r){return Y(this,void 0,void 0,function(){var n,i,a,o,s,f,u,c,l,h;return J(this,function(d){if(n=this.page.normalizedEntries(),i=n.Contents,a=n.Resources,!i)throw new Gl;return o=this.decodeContents(i),s=this.boundingBox,f=s.left,u=s.bottom,c=s.right,l=s.top,h=t.flateStream(o,{Type:"XObject",Subtype:"Form",FormType:1,BBox:[f,u,c,l],Matrix:this.transformationMatrix,Resources:a}),r?(t.assign(r,h),[2,r]):[2,t.register(h)]})})},e.prototype.decodeContents=function(t){for(var r=Uint8Array.of(y.Newline),n=[],i=0,a=t.size();i<a;i++){var o=t.lookup(i,Ie),s=void 0;if(o instanceof en)s=yu(o).decode();else if(o instanceof qr)s=o.getUnencodedContents();else throw new Hl(o);n.push(s,r)}return ff.apply(void 0,n)},e}(),An=function(e,t){if(e!==void 0)return t[e]},Kr;(function(e){e.UseNone="UseNone",e.UseOutlines="UseOutlines",e.UseThumbs="UseThumbs",e.UseOC="UseOC"})(Kr||(Kr={}));var Lr;(function(e){e.L2R="L2R",e.R2L="R2L"})(Lr||(Lr={}));var Gr;(function(e){e.None="None",e.AppDefault="AppDefault"})(Gr||(Gr={}));var Qn;(function(e){e.Simplex="Simplex",e.DuplexFlipShortEdge="DuplexFlipShortEdge",e.DuplexFlipLongEdge="DuplexFlipLongEdge"})(Qn||(Qn={}));var Jo=function(){function e(t){this.dict=t}return e.prototype.lookupBool=function(t){var r=this.dict.lookup(p.of(t));if(r instanceof $r)return r},e.prototype.lookupName=function(t){var r=this.dict.lookup(p.of(t));if(r instanceof p)return r},e.prototype.HideToolbar=function(){return this.lookupBool("HideToolbar")},e.prototype.HideMenubar=function(){return this.lookupBool("HideMenubar")},e.prototype.HideWindowUI=function(){return this.lookupBool("HideWindowUI")},e.prototype.FitWindow=function(){return this.lookupBool("FitWindow")},e.prototype.CenterWindow=function(){return this.lookupBool("CenterWindow")},e.prototype.DisplayDocTitle=function(){return this.lookupBool("DisplayDocTitle")},e.prototype.NonFullScreenPageMode=function(){return this.lookupName("NonFullScreenPageMode")},e.prototype.Direction=function(){return this.lookupName("Direction")},e.prototype.PrintScaling=function(){return this.lookupName("PrintScaling")},e.prototype.Duplex=function(){return this.lookupName("Duplex")},e.prototype.PickTrayByPDFSize=function(){return this.lookupBool("PickTrayByPDFSize")},e.prototype.PrintPageRange=function(){var t=this.dict.lookup(p.of("PrintPageRange"));if(t instanceof ae)return t},e.prototype.NumCopies=function(){var t=this.dict.lookup(p.of("NumCopies"));if(t instanceof q)return t},e.prototype.getHideToolbar=function(){var t,r;return(r=(t=this.HideToolbar())===null||t===void 0?void 0:t.asBoolean())!==null&&r!==void 0?r:!1},e.prototype.getHideMenubar=function(){var t,r;return(r=(t=this.HideMenubar())===null||t===void 0?void 0:t.asBoolean())!==null&&r!==void 0?r:!1},e.prototype.getHideWindowUI=function(){var t,r;return(r=(t=this.HideWindowUI())===null||t===void 0?void 0:t.asBoolean())!==null&&r!==void 0?r:!1},e.prototype.getFitWindow=function(){var t,r;return(r=(t=this.FitWindow())===null||t===void 0?void 0:t.asBoolean())!==null&&r!==void 0?r:!1},e.prototype.getCenterWindow=function(){var t,r;return(r=(t=this.CenterWindow())===null||t===void 0?void 0:t.asBoolean())!==null&&r!==void 0?r:!1},e.prototype.getDisplayDocTitle=function(){var t,r;return(r=(t=this.DisplayDocTitle())===null||t===void 0?void 0:t.asBoolean())!==null&&r!==void 0?r:!1},e.prototype.getNonFullScreenPageMode=function(){var t,r,n=(t=this.NonFullScreenPageMode())===null||t===void 0?void 0:t.decodeText();return(r=An(n,Kr))!==null&&r!==void 0?r:Kr.UseNone},e.prototype.getReadingDirection=function(){var t,r,n=(t=this.Direction())===null||t===void 0?void 0:t.decodeText();return(r=An(n,Lr))!==null&&r!==void 0?r:Lr.L2R},e.prototype.getPrintScaling=function(){var t,r,n=(t=this.PrintScaling())===null||t===void 0?void 0:t.decodeText();return(r=An(n,Gr))!==null&&r!==void 0?r:Gr.AppDefault},e.prototype.getDuplex=function(){var t,r=(t=this.Duplex())===null||t===void 0?void 0:t.decodeText();return An(r,Qn)},e.prototype.getPickTrayByPDFSize=function(){var t;return(t=this.PickTrayByPDFSize())===null||t===void 0?void 0:t.asBoolean()},e.prototype.getPrintPageRange=function(){var t=this.PrintPageRange();if(!t)return[];for(var r=[],n=0;n<t.size();n+=2){var i=t.lookup(n,q).asNumber(),a=t.lookup(n+1,q).asNumber();r.push({start:i,end:a})}return r},e.prototype.getNumCopies=function(){var t,r;return(r=(t=this.NumCopies())===null||t===void 0?void 0:t.asNumber())!==null&&r!==void 0?r:1},e.prototype.setHideToolbar=function(t){var r=this.dict.context.obj(t);this.dict.set(p.of("HideToolbar"),r)},e.prototype.setHideMenubar=function(t){var r=this.dict.context.obj(t);this.dict.set(p.of("HideMenubar"),r)},e.prototype.setHideWindowUI=function(t){var r=this.dict.context.obj(t);this.dict.set(p.of("HideWindowUI"),r)},e.prototype.setFitWindow=function(t){var r=this.dict.context.obj(t);this.dict.set(p.of("FitWindow"),r)},e.prototype.setCenterWindow=function(t){var r=this.dict.context.obj(t);this.dict.set(p.of("CenterWindow"),r)},e.prototype.setDisplayDocTitle=function(t){var r=this.dict.context.obj(t);this.dict.set(p.of("DisplayDocTitle"),r)},e.prototype.setNonFullScreenPageMode=function(t){Dt(t,"nonFullScreenPageMode",Kr);var r=p.of(t);this.dict.set(p.of("NonFullScreenPageMode"),r)},e.prototype.setReadingDirection=function(t){Dt(t,"readingDirection",Lr);var r=p.of(t);this.dict.set(p.of("Direction"),r)},e.prototype.setPrintScaling=function(t){Dt(t,"printScaling",Gr);var r=p.of(t);this.dict.set(p.of("PrintScaling"),r)},e.prototype.setDuplex=function(t){Dt(t,"duplex",Qn);var r=p.of(t);this.dict.set(p.of("Duplex"),r)},e.prototype.setPickTrayByPDFSize=function(t){var r=this.dict.context.obj(t);this.dict.set(p.of("PickTrayByPDFSize"),r)},e.prototype.setPrintPageRange=function(t){Array.isArray(t)||(t=[t]);for(var r=[],n=0,i=t.length;n<i;n++)r.push(t[n].start),r.push(t[n].end);ou(r,"printPageRange",["number"]);var a=this.dict.context.obj(r);this.dict.set(p.of("PrintPageRange"),a)},e.prototype.setNumCopies=function(t){je(t,"numCopies",1,Number.MAX_VALUE),Kl(t,"numCopies");var r=this.dict.context.obj(t);this.dict.set(p.of("NumCopies"),r)},e.fromDict=function(t){return new e(t)},e.create=function(t){var r=t.obj({});return new e(r)},e}(),Ih=/\/([^\0\t\n\f\r\ ]+)[\0\t\n\f\r\ ]*(\d*\.\d+|\d+)?[\0\t\n\f\r\ ]+Tf/,mu=function(){function e(t,r){this.dict=t,this.ref=r}return e.prototype.T=function(){return this.dict.lookupMaybe(p.of("T"),ue,U)},e.prototype.Ff=function(){var t=this.getInheritableAttribute(p.of("Ff"));return this.dict.context.lookupMaybe(t,q)},e.prototype.V=function(){var t=this.getInheritableAttribute(p.of("V"));return this.dict.context.lookup(t)},e.prototype.Kids=function(){return this.dict.lookupMaybe(p.of("Kids"),ae)},e.prototype.DA=function(){var t=this.dict.lookup(p.of("DA"));if(t instanceof ue||t instanceof U)return t},e.prototype.setKids=function(t){this.dict.set(p.of("Kids"),this.dict.context.obj(t))},e.prototype.getParent=function(){var t=this.dict.get(p.of("Parent"));if(t instanceof fe){var r=this.dict.lookup(p.of("Parent"),Z);return new e(r,t)}},e.prototype.setParent=function(t){t?this.dict.set(p.of("Parent"),t):this.dict.delete(p.of("Parent"))},e.prototype.getFullyQualifiedName=function(){var t=this.getParent();return t?t.getFullyQualifiedName()+"."+this.getPartialName():this.getPartialName()},e.prototype.getPartialName=function(){var t;return(t=this.T())===null||t===void 0?void 0:t.decodeText()},e.prototype.setPartialName=function(t){t?this.dict.set(p.of("T"),U.fromText(t)):this.dict.delete(p.of("T"))},e.prototype.setDefaultAppearance=function(t){this.dict.set(p.of("DA"),ue.of(t))},e.prototype.getDefaultAppearance=function(){var t=this.DA();return t instanceof U?t.decodeText():t==null?void 0:t.asString()},e.prototype.setFontSize=function(t){var r,n=(r=this.getFullyQualifiedName())!==null&&r!==void 0?r:"",i=this.getDefaultAppearance();if(!i)throw new Jl(n);var a=ha(i,Ih);if(!a.match)throw new Ql(n);var o=i.slice(0,a.pos-a.match[0].length),s=a.pos<=i.length?i.slice(a.pos):"",f=a.match[1],u=o+" /"+f+" "+t+" Tf "+s;this.setDefaultAppearance(u)},e.prototype.getFlags=function(){var t,r;return(r=(t=this.Ff())===null||t===void 0?void 0:t.asNumber())!==null&&r!==void 0?r:0},e.prototype.setFlags=function(t){this.dict.set(p.of("Ff"),q.of(t))},e.prototype.hasFlag=function(t){var r=this.getFlags();return(r&t)!==0},e.prototype.setFlag=function(t){var r=this.getFlags();this.setFlags(r|t)},e.prototype.clearFlag=function(t){var r=this.getFlags();this.setFlags(r&~t)},e.prototype.setFlagTo=function(t,r){r?this.setFlag(t):this.clearFlag(t)},e.prototype.getInheritableAttribute=function(t){var r;return this.ascend(function(n){r||(r=n.dict.get(t))}),r},e.prototype.ascend=function(t){t(this);var r=this.getParent();r&&r.ascend(t)},e}(),Li=function(){function e(t){this.dict=t}return e.prototype.W=function(){var t=this.dict.lookup(p.of("W"));if(t instanceof q)return t},e.prototype.getWidth=function(){var t,r;return(r=(t=this.W())===null||t===void 0?void 0:t.asNumber())!==null&&r!==void 0?r:1},e.prototype.setWidth=function(t){var r=this.dict.context.obj(t);this.dict.set(p.of("W"),r)},e.fromDict=function(t){return new e(t)},e}(),Uh=function(){function e(t){this.dict=t}return e.prototype.Rect=function(){return this.dict.lookup(p.of("Rect"),ae)},e.prototype.AP=function(){return this.dict.lookupMaybe(p.of("AP"),Z)},e.prototype.F=function(){var t=this.dict.lookup(p.of("F"));return this.dict.context.lookupMaybe(t,q)},e.prototype.getRectangle=function(){var t,r=this.Rect();return(t=r==null?void 0:r.asRectangle())!==null&&t!==void 0?t:{x:0,y:0,width:0,height:0}},e.prototype.setRectangle=function(t){var r=t.x,n=t.y,i=t.width,a=t.height,o=this.dict.context.obj([r,n,r+i,n+a]);this.dict.set(p.of("Rect"),o)},e.prototype.getAppearanceState=function(){var t=this.dict.lookup(p.of("AS"));if(t instanceof p)return t},e.prototype.setAppearanceState=function(t){this.dict.set(p.of("AS"),t)},e.prototype.setAppearances=function(t){this.dict.set(p.of("AP"),t)},e.prototype.ensureAP=function(){var t=this.AP();return t||(t=this.dict.context.obj({}),this.dict.set(p.of("AP"),t)),t},e.prototype.getNormalAppearance=function(){var t=this.ensureAP(),r=t.get(p.of("N"));if(r instanceof fe||r instanceof Z)return r;throw new Error("Unexpected N type: "+(r==null?void 0:r.constructor.name))},e.prototype.setNormalAppearance=function(t){var r=this.ensureAP();r.set(p.of("N"),t)},e.prototype.setRolloverAppearance=function(t){var r=this.ensureAP();r.set(p.of("R"),t)},e.prototype.setDownAppearance=function(t){var r=this.ensureAP();r.set(p.of("D"),t)},e.prototype.removeRolloverAppearance=function(){var t=this.AP();t==null||t.delete(p.of("R"))},e.prototype.removeDownAppearance=function(){var t=this.AP();t==null||t.delete(p.of("D"))},e.prototype.getAppearances=function(){var t=this.AP();if(t){var r=t.lookup(p.of("N"),Z,Ie),n=t.lookupMaybe(p.of("R"),Z,Ie),i=t.lookupMaybe(p.of("D"),Z,Ie);return{normal:r,rollover:n,down:i}}},e.prototype.getFlags=function(){var t,r;return(r=(t=this.F())===null||t===void 0?void 0:t.asNumber())!==null&&r!==void 0?r:0},e.prototype.setFlags=function(t){this.dict.set(p.of("F"),q.of(t))},e.prototype.hasFlag=function(t){var r=this.getFlags();return(r&t)!==0},e.prototype.setFlag=function(t){var r=this.getFlags();this.setFlags(r|t)},e.prototype.clearFlag=function(t){var r=this.getFlags();this.setFlags(r&~t)},e.prototype.setFlagTo=function(t,r){r?this.setFlag(t):this.clearFlag(t)},e.fromDict=function(t){return new e(t)},e}(),Gi=function(){function e(t){this.dict=t}return e.prototype.R=function(){var t=this.dict.lookup(p.of("R"));if(t instanceof q)return t},e.prototype.BC=function(){var t=this.dict.lookup(p.of("BC"));if(t instanceof ae)return t},e.prototype.BG=function(){var t=this.dict.lookup(p.of("BG"));if(t instanceof ae)return t},e.prototype.CA=function(){var t=this.dict.lookup(p.of("CA"));if(t instanceof U||t instanceof ue)return t},e.prototype.RC=function(){var t=this.dict.lookup(p.of("RC"));if(t instanceof U||t instanceof ue)return t},e.prototype.AC=function(){var t=this.dict.lookup(p.of("AC"));if(t instanceof U||t instanceof ue)return t},e.prototype.getRotation=function(){var t;return(t=this.R())===null||t===void 0?void 0:t.asNumber()},e.prototype.getBorderColor=function(){var t=this.BC();if(t){for(var r=[],n=0,i=t==null?void 0:t.size();n<i;n++){var a=t.get(n);a instanceof q&&r.push(a.asNumber())}return r}},e.prototype.getBackgroundColor=function(){var t=this.BG();if(t){for(var r=[],n=0,i=t==null?void 0:t.size();n<i;n++){var a=t.get(n);a instanceof q&&r.push(a.asNumber())}return r}},e.prototype.getCaptions=function(){var t=this.CA(),r=this.RC(),n=this.AC();return{normal:t==null?void 0:t.decodeText(),rollover:r==null?void 0:r.decodeText(),down:n==null?void 0:n.decodeText()}},e.prototype.setRotation=function(t){var r=this.dict.context.obj(t);this.dict.set(p.of("R"),r)},e.prototype.setBorderColor=function(t){var r=this.dict.context.obj(t);this.dict.set(p.of("BC"),r)},e.prototype.setBackgroundColor=function(t){var r=this.dict.context.obj(t);this.dict.set(p.of("BG"),r)},e.prototype.setCaptions=function(t){var r=U.fromText(t.normal);if(this.dict.set(p.of("CA"),r),t.rollover){var n=U.fromText(t.rollover);this.dict.set(p.of("RC"),n)}else this.dict.delete(p.of("RC"));if(t.down){var i=U.fromText(t.down);this.dict.set(p.of("AC"),i)}else this.dict.delete(p.of("AC"))},e.fromDict=function(t){return new e(t)},e}(),oa=function(e){E(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.MK=function(){var r=this.dict.lookup(p.of("MK"));if(r instanceof Z)return r},t.prototype.BS=function(){var r=this.dict.lookup(p.of("BS"));if(r instanceof Z)return r},t.prototype.DA=function(){var r=this.dict.lookup(p.of("DA"));if(r instanceof ue||r instanceof U)return r},t.prototype.P=function(){var r=this.dict.get(p.of("P"));if(r instanceof fe)return r},t.prototype.setP=function(r){this.dict.set(p.of("P"),r)},t.prototype.setDefaultAppearance=function(r){this.dict.set(p.of("DA"),ue.of(r))},t.prototype.getDefaultAppearance=function(){var r=this.DA();return r instanceof U?r.decodeText():r==null?void 0:r.asString()},t.prototype.getAppearanceCharacteristics=function(){var r=this.MK();if(r)return Gi.fromDict(r)},t.prototype.getOrCreateAppearanceCharacteristics=function(){var r=this.MK();if(r)return Gi.fromDict(r);var n=Gi.fromDict(this.dict.context.obj({}));return this.dict.set(p.of("MK"),n.dict),n},t.prototype.getBorderStyle=function(){var r=this.BS();if(r)return Li.fromDict(r)},t.prototype.getOrCreateBorderStyle=function(){var r=this.BS();if(r)return Li.fromDict(r);var n=Li.fromDict(this.dict.context.obj({}));return this.dict.set(p.of("BS"),n.dict),n},t.prototype.getOnValue=function(){var r,n=(r=this.getAppearances())===null||r===void 0?void 0:r.normal;if(n instanceof Z)for(var i=n.keys(),a=0,o=i.length;a<o;a++){var s=i[a];if(s!==p.of("Off"))return s}},t.fromDict=function(r){return new t(r)},t.create=function(r,n){var i=r.obj({Type:"Annot",Subtype:"Widget",Rect:[0,0,0,0],Parent:n});return new t(i)},t}(Uh),br=function(e){E(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.FT=function(){var r=this.getInheritableAttribute(p.of("FT"));return this.dict.context.lookup(r,p)},t.prototype.getWidgets=function(){var r=this.Kids();if(!r)return[oa.fromDict(this.dict)];for(var n=new Array(r.size()),i=0,a=r.size();i<a;i++){var o=r.lookup(i,Z);n[i]=oa.fromDict(o)}return n},t.prototype.addWidget=function(r){var n=this.normalizedEntries().Kids;n.push(r)},t.prototype.removeWidget=function(r){var n=this.Kids();if(n){if(r<0||r>n.size())throw new Yn(r,0,n.size());n.remove(r)}else{if(r!==0)throw new Yn(r,0,0);this.setKids([])}},t.prototype.normalizedEntries=function(){var r=this.Kids();return r||(r=this.dict.context.obj([this.ref]),this.dict.set(p.of("Kids"),r)),{Kids:r}},t.fromDict=function(r,n){return new t(r,n)},t}(mu),Pa=function(e){E(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.Opt=function(){return this.dict.lookupMaybe(p.of("Opt"),ue,U,ae)},t.prototype.setOpt=function(r){this.dict.set(p.of("Opt"),this.dict.context.obj(r))},t.prototype.getExportValues=function(){var r=this.Opt();if(r){if(r instanceof ue||r instanceof U)return[r];for(var n=[],i=0,a=r.size();i<a;i++){var o=r.lookup(i);(o instanceof ue||o instanceof U)&&n.push(o)}return n}},t.prototype.removeExportValue=function(r){var n=this.Opt();if(n)if(n instanceof ue||n instanceof U){if(r!==0)throw new Yn(r,0,0);this.setOpt([])}else{if(r<0||r>n.size())throw new Yn(r,0,n.size());n.remove(r)}},t.prototype.normalizeExportValues=function(){for(var r,n,i,a,o=(r=this.getExportValues())!==null&&r!==void 0?r:[],s=[],f=this.getWidgets(),u=0,c=f.length;u<c;u++){var l=f[u],h=(n=o[u])!==null&&n!==void 0?n:U.fromText((a=(i=l.getOnValue())===null||i===void 0?void 0:i.decodeText())!==null&&a!==void 0?a:"");s.push(h)}this.setOpt(s)},t.prototype.addOpt=function(r,n){var i;this.normalizeExportValues();var a=r.decodeText(),o;if(n)for(var s=(i=this.getExportValues())!==null&&i!==void 0?i:[],f=0,u=s.length;f<u;f++){var c=s[f];c.decodeText()===a&&(o=f)}var l=this.Opt();return l.push(r),o??l.size()-1},t.prototype.addWidgetWithOpt=function(r,n,i){var a=this.addOpt(n,i),o=p.of(String(a));return this.addWidget(r),o},t}(br),vi=function(e){E(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.setValue=function(r){var n,i=(n=this.getOnValue())!==null&&n!==void 0?n:p.of("Yes");if(r!==i&&r!==p.of("Off"))throw new Sa;this.dict.set(p.of("V"),r);for(var a=this.getWidgets(),o=0,s=a.length;o<s;o++){var f=a[o],u=f.getOnValue()===r?r:p.of("Off");f.setAppearanceState(u)}},t.prototype.getValue=function(){var r=this.V();return r instanceof p?r:p.of("Off")},t.prototype.getOnValue=function(){var r=this.getWidgets()[0];return r==null?void 0:r.getOnValue()},t.fromDict=function(r,n){return new t(r,n)},t.create=function(r){var n=r.obj({FT:"Btn",Kids:[]}),i=r.register(n);return new t(n,i)},t}(Pa),ge=function(e){return 1<<e},$e;(function(e){e[e.ReadOnly=ge(1-1)]="ReadOnly",e[e.Required=ge(2-1)]="Required",e[e.NoExport=ge(3-1)]="NoExport"})($e||($e={}));var qe;(function(e){e[e.NoToggleToOff=ge(15-1)]="NoToggleToOff",e[e.Radio=ge(16-1)]="Radio",e[e.PushButton=ge(17-1)]="PushButton",e[e.RadiosInUnison=ge(26-1)]="RadiosInUnison"})(qe||(qe={}));var de;(function(e){e[e.Multiline=ge(13-1)]="Multiline",e[e.Password=ge(14-1)]="Password",e[e.FileSelect=ge(21-1)]="FileSelect",e[e.DoNotSpellCheck=ge(23-1)]="DoNotSpellCheck",e[e.DoNotScroll=ge(24-1)]="DoNotScroll",e[e.Comb=ge(25-1)]="Comb",e[e.RichText=ge(26-1)]="RichText"})(de||(de={}));var se;(function(e){e[e.Combo=ge(18-1)]="Combo",e[e.Edit=ge(19-1)]="Edit",e[e.Sort=ge(20-1)]="Sort",e[e.MultiSelect=ge(22-1)]="MultiSelect",e[e.DoNotSpellCheck=ge(23-1)]="DoNotSpellCheck",e[e.CommitOnSelChange=ge(27-1)]="CommitOnSelChange"})(se||(se={}));var xu=function(e){E(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.setValues=function(r){if(this.hasFlag(se.Combo)&&!this.hasFlag(se.Edit)&&!this.valuesAreValid(r))throw new Sa;if(r.length===0&&this.dict.delete(p.of("V")),r.length===1&&this.dict.set(p.of("V"),r[0]),r.length>1){if(!this.hasFlag(se.MultiSelect))throw new Yl;this.dict.set(p.of("V"),this.dict.context.obj(r))}this.updateSelectedIndices(r)},t.prototype.valuesAreValid=function(r){for(var n=this.getOptions(),i=function(f,u){var c=r[f].decodeText();if(!n.find(function(l){return c===(l.display||l.value).decodeText()}))return{value:!1}},a=0,o=r.length;a<o;a++){var s=i(a);if(typeof s=="object")return s.value}return!0},t.prototype.updateSelectedIndices=function(r){if(r.length>1){for(var n=new Array(r.length),i=this.getOptions(),a=function(f,u){var c=r[f].decodeText();n[f]=i.findIndex(function(l){return c===(l.display||l.value).decodeText()})},o=0,s=r.length;o<s;o++)a(o,s);this.dict.set(p.of("I"),this.dict.context.obj(n.sort()))}else this.dict.delete(p.of("I"))},t.prototype.getValues=function(){var r=this.V();if(r instanceof ue||r instanceof U)return[r];if(r instanceof ae){for(var n=[],i=0,a=r.size();i<a;i++){var o=r.lookup(i);(o instanceof ue||o instanceof U)&&n.push(o)}return n}return[]},t.prototype.Opt=function(){return this.dict.lookupMaybe(p.of("Opt"),ue,U,ae)},t.prototype.setOptions=function(r){for(var n=new Array(r.length),i=0,a=r.length;i<a;i++){var o=r[i],s=o.value,f=o.display;n[i]=this.dict.context.obj([s,f||s])}this.dict.set(p.of("Opt"),this.dict.context.obj(n))},t.prototype.getOptions=function(){var r=this.Opt();if(r instanceof ue||r instanceof U)return[{value:r,display:r}];if(r instanceof ae){for(var n=[],i=0,a=r.size();i<a;i++){var o=r.lookup(i);if((o instanceof ue||o instanceof U)&&n.push({value:o,display:o}),o instanceof ae&&o.size()>0){var s=o.lookup(0,ue,U),f=o.lookupMaybe(1,ue,U);n.push({value:s,display:f||s})}}return n}return[]},t}(br),pi=function(e){E(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.fromDict=function(r,n){return new t(r,n)},t.create=function(r){var n=r.obj({FT:"Ch",Ff:se.Combo,Kids:[]}),i=r.register(n);return new t(n,i)},t}(xu),_n=function(e){E(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.addField=function(r){var n=this.normalizedEntries().Kids;n==null||n.push(r)},t.prototype.normalizedEntries=function(){var r=this.Kids();return r||(r=this.dict.context.obj([]),this.dict.set(p.of("Kids"),r)),{Kids:r}},t.fromDict=function(r,n){return new t(r,n)},t.create=function(r){var n=r.obj({}),i=r.register(n);return new t(n,i)},t}(mu),Aa=function(e){E(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.fromDict=function(r,n){return new t(r,n)},t}(br),gi=function(e){E(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.MaxLen=function(){var r=this.dict.lookup(p.of("MaxLen"));if(r instanceof q)return r},t.prototype.Q=function(){var r=this.dict.lookup(p.of("Q"));if(r instanceof q)return r},t.prototype.setMaxLength=function(r){this.dict.set(p.of("MaxLen"),q.of(r))},t.prototype.removeMaxLength=function(){this.dict.delete(p.of("MaxLen"))},t.prototype.getMaxLength=function(){var r;return(r=this.MaxLen())===null||r===void 0?void 0:r.asNumber()},t.prototype.setQuadding=function(r){this.dict.set(p.of("Q"),q.of(r))},t.prototype.getQuadding=function(){var r;return(r=this.Q())===null||r===void 0?void 0:r.asNumber()},t.prototype.setValue=function(r){this.dict.set(p.of("V"),r)},t.prototype.removeValue=function(){this.dict.delete(p.of("V"))},t.prototype.getValue=function(){var r=this.V();if(r instanceof ue||r instanceof U)return r},t.fromDict=function(r,n){return new t(r,n)},t.create=function(r){var n=r.obj({FT:"Tx",Kids:[]}),i=r.register(n);return new t(n,i)},t}(br),yi=function(e){E(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.fromDict=function(r,n){return new t(r,n)},t.create=function(r){var n=r.obj({FT:"Btn",Ff:qe.PushButton,Kids:[]}),i=r.register(n);return new t(n,i)},t}(Pa),bi=function(e){E(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.setValue=function(r){var n=this.getOnValues();if(!n.includes(r)&&r!==p.of("Off"))throw new Sa;this.dict.set(p.of("V"),r);for(var i=this.getWidgets(),a=0,o=i.length;a<o;a++){var s=i[a],f=s.getOnValue()===r?r:p.of("Off");s.setAppearanceState(f)}},t.prototype.getValue=function(){var r=this.V();return r instanceof p?r:p.of("Off")},t.prototype.getOnValues=function(){for(var r=this.getWidgets(),n=[],i=0,a=r.length;i<a;i++){var o=r[i].getOnValue();o&&n.push(o)}return n},t.fromDict=function(r,n){return new t(r,n)},t.create=function(r){var n=r.obj({FT:"Btn",Ff:qe.Radio,Kids:[]}),i=r.register(n);return new t(n,i)},t}(Pa),mi=function(e){E(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.fromDict=function(r,n){return new t(r,n)},t.create=function(r){var n=r.obj({FT:"Ch",Kids:[]}),i=r.register(n);return new t(n,i)},t}(xu),Da=function(e){if(!e)return[];for(var t=[],r=0,n=e.size();r<n;r++){var i=e.get(r),a=e.lookup(r);i instanceof fe&&a instanceof Z&&t.push([wu(a,i),i])}return t},wu=function(e,t){var r=Vh(e);return r?_n.fromDict(e,t):Wh(e,t)},Vh=function(e){var t=e.lookup(p.of("Kids"));if(t instanceof ae)for(var r=0,n=t.size();r<n;r++){var i=t.lookup(r),a=i instanceof Z&&i.has(p.of("T"));if(a)return!0}return!1},Wh=function(e,t){var r=Ra(e,p.of("FT")),n=e.context.lookup(r,p);return n===p.of("Btn")?qh(e,t):n===p.of("Ch")?Kh(e,t):n===p.of("Tx")?gi.fromDict(e,t):n===p.of("Sig")?Aa.fromDict(e,t):br.fromDict(e,t)},qh=function(e,t){var r,n=Ra(e,p.of("Ff")),i=e.context.lookupMaybe(n,q),a=(r=i==null?void 0:i.asNumber())!==null&&r!==void 0?r:0;return sa(a,qe.PushButton)?yi.fromDict(e,t):sa(a,qe.Radio)?bi.fromDict(e,t):vi.fromDict(e,t)},Kh=function(e,t){var r,n=Ra(e,p.of("Ff")),i=e.context.lookupMaybe(n,q),a=(r=i==null?void 0:i.asNumber())!==null&&r!==void 0?r:0;return sa(a,se.Combo)?pi.fromDict(e,t):mi.fromDict(e,t)},sa=function(e,t){return(e&t)!==0},Ra=function(e,t){var r;return Su(e,function(n){r||(r=n.get(t))}),r},Su=function(e,t){t(e);var r=e.lookupMaybe(p.of("Parent"),Z);r&&Su(r,t)},$n=function(){function e(t){this.dict=t}return e.prototype.Fields=function(){var t=this.dict.lookup(p.of("Fields"));if(t instanceof ae)return t},e.prototype.getFields=function(){for(var t=this.normalizedEntries().Fields,r=new Array(t.size()),n=0,i=t.size();n<i;n++){var a=t.get(n),o=t.lookup(n,Z);r[n]=[wu(o,a),a]}return r},e.prototype.getAllFields=function(){var t=[],r=function(n){if(n)for(var i=0,a=n.length;i<a;i++){var o=n[i];t.push(o);var s=o[0];s instanceof _n&&r(Da(s.Kids()))}};return r(this.getFields()),t},e.prototype.addField=function(t){var r=this.normalizedEntries().Fields;r==null||r.push(t)},e.prototype.removeField=function(t){var r=t.getParent(),n=r===void 0?this.normalizedEntries().Fields:r.Kids(),i=n==null?void 0:n.indexOf(t.ref);if(n===void 0||i===void 0)throw new Error("Tried to remove inexistent field "+t.getFullyQualifiedName());n.remove(i),r!==void 0&&n.size()===0&&this.removeField(r)},e.prototype.normalizedEntries=function(){var t=this.Fields();return t||(t=this.dict.context.obj([]),this.dict.set(p.of("Fields"),t)),{Fields:t}},e.fromDict=function(t){return new e(t)},e.create=function(t){var r=t.obj({Fields:[]});return new e(r)},e}(),Fu=function(e){E(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.Pages=function(){return this.lookup(p.of("Pages"),Z)},t.prototype.AcroForm=function(){return this.lookupMaybe(p.of("AcroForm"),Z)},t.prototype.getAcroForm=function(){var r=this.AcroForm();if(r)return $n.fromDict(r)},t.prototype.getOrCreateAcroForm=function(){var r=this.getAcroForm();if(!r){r=$n.create(this.context);var n=this.context.register(r.dict);this.set(p.of("AcroForm"),n)}return r},t.prototype.ViewerPreferences=function(){return this.lookupMaybe(p.of("ViewerPreferences"),Z)},t.prototype.getViewerPreferences=function(){var r=this.ViewerPreferences();if(r)return Jo.fromDict(r)},t.prototype.getOrCreateViewerPreferences=function(){var r=this.getViewerPreferences();if(!r){r=Jo.create(this.context);var n=this.context.register(r.dict);this.set(p.of("ViewerPreferences"),n)}return r},t.prototype.insertLeafNode=function(r,n){var i=this.get(p.of("Pages")),a=this.Pages().insertLeafNode(r,n);return a||i},t.prototype.removeLeafNode=function(r){this.Pages().removeLeafNode(r)},t.withContextAndPages=function(r,n){var i=new Map;return i.set(p.of("Type"),p.of("Catalog")),i.set(p.of("Pages"),n),new t(i,r)},t.fromMapWithContext=function(r,n){return new t(r,n)},t}(Z),ku=function(e){E(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.Parent=function(){return this.lookup(p.of("Parent"))},t.prototype.Kids=function(){return this.lookup(p.of("Kids"),ae)},t.prototype.Count=function(){return this.lookup(p.of("Count"),q)},t.prototype.pushTreeNode=function(r){var n=this.Kids();n.push(r)},t.prototype.pushLeafNode=function(r){var n=this.Kids();this.insertLeafKid(n.size(),r)},t.prototype.insertLeafNode=function(r,n){var i=this.Kids(),a=this.Count().asNumber();if(n>a)throw new Mo(n,a);for(var o=n,s=0,f=i.size();s<f;s++){if(o===0){this.insertLeafKid(s,r);return}var u=i.get(s),c=this.context.lookup(u);if(c instanceof t){if(c.Count().asNumber()>o)return c.insertLeafNode(r,o)||u;o-=c.Count().asNumber()}c instanceof gt&&(o-=1)}if(o===0){this.insertLeafKid(i.size(),r);return}throw new Io(n,"insertLeafNode")},t.prototype.removeLeafNode=function(r,n){n===void 0&&(n=!0);var i=this.Kids(),a=this.Count().asNumber();if(r>=a)throw new Mo(r,a);for(var o=r,s=0,f=i.size();s<f;s++){var u=i.get(s),c=this.context.lookup(u);if(c instanceof t)if(c.Count().asNumber()>o){c.removeLeafNode(o,n),n&&c.Kids().size()===0&&i.remove(s);return}else o-=c.Count().asNumber();if(c instanceof gt)if(o===0){this.removeKid(s);return}else o-=1}throw new Io(r,"removeLeafNode")},t.prototype.ascend=function(r){r(this);var n=this.Parent();n&&n.ascend(r)},t.prototype.traverse=function(r){for(var n=this.Kids(),i=0,a=n.size();i<a;i++){var o=n.get(i),s=this.context.lookup(o);s instanceof t&&s.traverse(r),r(s,o)}},t.prototype.insertLeafKid=function(r,n){var i=this.Kids();this.ascend(function(a){var o=a.Count().asNumber()+1;a.set(p.of("Count"),q.of(o))}),i.insert(r,n)},t.prototype.removeKid=function(r){var n=this.Kids(),i=n.lookup(r);i instanceof gt&&this.ascend(function(a){var o=a.Count().asNumber()-1;a.set(p.of("Count"),q.of(o))}),n.remove(r)},t.withContext=function(r,n){var i=new Map;return i.set(p.of("Type"),p.of("Pages")),i.set(p.of("Kids"),r.obj([])),i.set(p.of("Count"),r.obj(0)),n&&i.set(p.of("Parent"),n),new t(i,r)},t.fromMapWithContext=function(r,n){return new t(r,n)},t}(Z),Pe=new Uint8Array(256);Pe[y.Zero]=1;Pe[y.One]=1;Pe[y.Two]=1;Pe[y.Three]=1;Pe[y.Four]=1;Pe[y.Five]=1;Pe[y.Six]=1;Pe[y.Seven]=1;Pe[y.Eight]=1;Pe[y.Nine]=1;var xi=new Uint8Array(256);xi[y.Period]=1;xi[y.Plus]=1;xi[y.Minus]=1;var Oa=new Uint8Array(256);for(var Tr=0,Lh=256;Tr<Lh;Tr++)Oa[Tr]=Pe[Tr]||xi[Tr]?1:0;var Qo=y.Newline,_o=y.CarriageReturn,Gh=function(){function e(t,r){r===void 0&&(r=!1),this.bytes=t,this.capNumbers=r}return e.prototype.parseRawInt=function(){for(var t="";!this.bytes.done();){var r=this.bytes.peek();if(!Pe[r])break;t+=vt(this.bytes.next())}var n=Number(t);if(!t||!isFinite(n))throw new Uo(this.bytes.position(),t);return n},e.prototype.parseRawNumber=function(){for(var t="";!this.bytes.done();){var r=this.bytes.peek();if(!Oa[r]||(t+=vt(this.bytes.next()),r===y.Period))break}for(;!this.bytes.done();){var r=this.bytes.peek();if(!Pe[r])break;t+=vt(this.bytes.next())}var n=Number(t);if(!t||!isFinite(n))throw new Uo(this.bytes.position(),t);if(n>Number.MAX_SAFE_INTEGER)if(this.capNumbers){var i="Parsed number that is too large for some PDF readers: "+t+", using Number.MAX_SAFE_INTEGER instead.";return console.warn(i),Number.MAX_SAFE_INTEGER}else{var i="Parsed number that is too large for some PDF readers: "+t+", not capping.";console.warn(i)}return n},e.prototype.skipWhitespace=function(){for(;!this.bytes.done()&&bt[this.bytes.peek()];)this.bytes.next()},e.prototype.skipLine=function(){for(;!this.bytes.done();){var t=this.bytes.peek();if(t===Qo||t===_o)return;this.bytes.next()}},e.prototype.skipComment=function(){if(this.bytes.peek()!==y.Percent)return!1;for(;!this.bytes.done();){var t=this.bytes.peek();if(t===Qo||t===_o)return!0;this.bytes.next()}return!0},e.prototype.skipWhitespaceAndComments=function(){for(this.skipWhitespace();this.skipComment();)this.skipWhitespace()},e.prototype.matchKeyword=function(t){for(var r=this.bytes.offset(),n=0,i=t.length;n<i;n++)if(this.bytes.done()||this.bytes.next()!==t[n])return this.bytes.moveTo(r),!1;return!0},e}(),wi=function(){function e(t){this.idx=0,this.line=0,this.column=0,this.bytes=t,this.length=this.bytes.length}return e.prototype.moveTo=function(t){this.idx=t},e.prototype.next=function(){var t=this.bytes[this.idx++];return t===y.Newline?(this.line+=1,this.column=0):this.column+=1,t},e.prototype.assertNext=function(t){if(this.peek()!==t)throw new _l(this.position(),t,this.peek());return this.next()},e.prototype.peek=function(){return this.bytes[this.idx]},e.prototype.peekAhead=function(t){return this.bytes[this.idx+t]},e.prototype.peekAt=function(t){return this.bytes[t]},e.prototype.done=function(){return this.idx>=this.length},e.prototype.offset=function(){return this.idx},e.prototype.slice=function(t,r){return this.bytes.slice(t,r)},e.prototype.position=function(){return{line:this.line,column:this.column,offset:this.idx}},e.of=function(t){return new e(t)},e.fromPDFRawStream=function(t){return e.of(yu(t).decode())},e}(),Hh=y.Space,Pr=y.CarriageReturn,Ar=y.Newline,Dr=[y.s,y.t,y.r,y.e,y.a,y.m],Dn=[y.e,y.n,y.d,y.s,y.t,y.r,y.e,y.a,y.m],le={header:[y.Percent,y.P,y.D,y.F,y.Dash],eof:[y.Percent,y.Percent,y.E,y.O,y.F],obj:[y.o,y.b,y.j],endobj:[y.e,y.n,y.d,y.o,y.b,y.j],xref:[y.x,y.r,y.e,y.f],trailer:[y.t,y.r,y.a,y.i,y.l,y.e,y.r],startxref:[y.s,y.t,y.a,y.r,y.t,y.x,y.r,y.e,y.f],true:[y.t,y.r,y.u,y.e],false:[y.f,y.a,y.l,y.s,y.e],null:[y.n,y.u,y.l,y.l],stream:Dr,streamEOF1:re(Dr,[Hh,Pr,Ar]),streamEOF2:re(Dr,[Pr,Ar]),streamEOF3:re(Dr,[Pr]),streamEOF4:re(Dr,[Ar]),endstream:Dn,EOF1endstream:re([Pr,Ar],Dn),EOF2endstream:re([Pr],Dn),EOF3endstream:re([Ar],Dn)},Cu=function(e){E(t,e);function t(r,n,i){i===void 0&&(i=!1);var a=e.call(this,r,i)||this;return a.context=n,a}return t.prototype.parseObject=function(){if(this.skipWhitespaceAndComments(),this.matchKeyword(le.true))return $r.True;if(this.matchKeyword(le.false))return $r.False;if(this.matchKeyword(le.null))return Be;var r=this.bytes.peek();if(r===y.LessThan&&this.bytes.peekAhead(1)===y.LessThan)return this.parseDictOrStream();if(r===y.LessThan)return this.parseHexString();if(r===y.LeftParen)return this.parseString();if(r===y.ForwardSlash)return this.parseName();if(r===y.LeftSquareBracket)return this.parseArray();if(Oa[r])return this.parseNumberOrRef();throw new $l(this.bytes.position(),r)},t.prototype.parseNumberOrRef=function(){var r=this.parseRawNumber();this.skipWhitespaceAndComments();var n=this.bytes.offset();if(Pe[this.bytes.peek()]){var i=this.parseRawNumber();if(this.skipWhitespaceAndComments(),this.bytes.peek()===y.R)return this.bytes.assertNext(y.R),fe.of(r,i)}return this.bytes.moveTo(n),q.of(r)},t.prototype.parseHexString=function(){var r="";for(this.bytes.assertNext(y.LessThan);!this.bytes.done()&&this.bytes.peek()!==y.GreaterThan;)r+=vt(this.bytes.next());return this.bytes.assertNext(y.GreaterThan),U.of(r)},t.prototype.parseString=function(){for(var r=0,n=!1,i="";!this.bytes.done();){var a=this.bytes.next();if(i+=vt(a),n||(a===y.LeftParen&&(r+=1),a===y.RightParen&&(r-=1)),a===y.BackSlash?n=!n:n&&(n=!1),r===0)return ue.of(i.substring(1,i.length-1))}throw new rh(this.bytes.position())},t.prototype.parseName=function(){this.bytes.assertNext(y.ForwardSlash);for(var r="";!this.bytes.done();){var n=this.bytes.peek();if(bt[n]||Qe[n])break;r+=vt(n),this.bytes.next()}return p.of(r)},t.prototype.parseArray=function(){this.bytes.assertNext(y.LeftSquareBracket),this.skipWhitespaceAndComments();for(var r=ae.withContext(this.context);this.bytes.peek()!==y.RightSquareBracket;){var n=this.parseObject();r.push(n),this.skipWhitespaceAndComments()}return this.bytes.assertNext(y.RightSquareBracket),r},t.prototype.parseDict=function(){this.bytes.assertNext(y.LessThan),this.bytes.assertNext(y.LessThan),this.skipWhitespaceAndComments();for(var r=new Map;!this.bytes.done()&&this.bytes.peek()!==y.GreaterThan&&this.bytes.peekAhead(1)!==y.GreaterThan;){var n=this.parseName(),i=this.parseObject();r.set(n,i),this.skipWhitespaceAndComments()}this.skipWhitespaceAndComments(),this.bytes.assertNext(y.GreaterThan),this.bytes.assertNext(y.GreaterThan);var a=r.get(p.of("Type"));return a===p.of("Catalog")?Fu.fromMapWithContext(r,this.context):a===p.of("Pages")?ku.fromMapWithContext(r,this.context):a===p.of("Page")?gt.fromMapWithContext(r,this.context):Z.fromMapWithContext(r,this.context)},t.prototype.parseDictOrStream=function(){var r=this.bytes.position(),n=this.parseDict();if(this.skipWhitespaceAndComments(),!this.matchKeyword(le.streamEOF1)&&!this.matchKeyword(le.streamEOF2)&&!this.matchKeyword(le.streamEOF3)&&!this.matchKeyword(le.streamEOF4)&&!this.matchKeyword(le.stream))return n;var i=this.bytes.offset(),a,o=n.get(p.of("Length"));o instanceof q?(a=i+o.asNumber(),this.bytes.moveTo(a),this.skipWhitespaceAndComments(),this.matchKeyword(le.endstream)||(this.bytes.moveTo(i),a=this.findEndOfStreamFallback(r))):a=this.findEndOfStreamFallback(r);var s=this.bytes.slice(i,a);return en.of(n,s)},t.prototype.findEndOfStreamFallback=function(r){for(var n=1,i=this.bytes.offset();!this.bytes.done()&&(i=this.bytes.offset(),this.matchKeyword(le.stream)?n+=1:this.matchKeyword(le.EOF1endstream)||this.matchKeyword(le.EOF2endstream)||this.matchKeyword(le.EOF3endstream)||this.matchKeyword(le.endstream)?n-=1:this.bytes.next(),n!==0););if(n!==0)throw new th(r);return i},t.forBytes=function(r,n,i){return new t(wi.of(r),n,i)},t.forByteStream=function(r,n,i){return i===void 0&&(i=!1),new t(r,n,i)},t}(Gh),Xh=function(e){E(t,e);function t(r,n){var i=e.call(this,wi.fromPDFRawStream(r),r.dict.context)||this,a=r.dict;return i.alreadyParsed=!1,i.shouldWaitForTick=n||function(){return!1},i.firstOffset=a.lookup(p.of("First"),q).asNumber(),i.objectCount=a.lookup(p.of("N"),q).asNumber(),i}return t.prototype.parseIntoContext=function(){return Y(this,void 0,void 0,function(){var r,n,i,a,o,s,f,u;return J(this,function(c){switch(c.label){case 0:if(this.alreadyParsed)throw new wa("PDFObjectStreamParser","parseIntoContext");this.alreadyParsed=!0,r=this.parseOffsetsAndObjectNumbers(),n=0,i=r.length,c.label=1;case 1:return n<i?(a=r[n],o=a.objectNumber,s=a.offset,this.bytes.moveTo(this.firstOffset+s),f=this.parseObject(),u=fe.of(o,0),this.context.assign(u,f),this.shouldWaitForTick()?[4,dr()]:[3,3]):[3,4];case 2:c.sent(),c.label=3;case 3:return n++,[3,1];case 4:return[2]}})})},t.prototype.parseOffsetsAndObjectNumbers=function(){for(var r=[],n=0,i=this.objectCount;n<i;n++){this.skipWhitespaceAndComments();var a=this.parseRawInt();this.skipWhitespaceAndComments();var o=this.parseRawInt();r.push({objectNumber:a,offset:o})}return r},t.forStream=function(r,n){return new t(r,n)},t}(Cu),Zh=function(){function e(t){this.alreadyParsed=!1,this.dict=t.dict,this.bytes=wi.fromPDFRawStream(t),this.context=this.dict.context;var r=this.dict.lookup(p.of("Size"),q),n=this.dict.lookup(p.of("Index"));if(n instanceof ae){this.subsections=[];for(var i=0,a=n.size();i<a;i+=2){var o=n.lookup(i+0,q).asNumber(),s=n.lookup(i+1,q).asNumber();this.subsections.push({firstObjectNumber:o,length:s})}}else this.subsections=[{firstObjectNumber:0,length:r.asNumber()}];var f=this.dict.lookup(p.of("W"),ae);this.byteWidths=[-1,-1,-1];for(var i=0,a=f.size();i<a;i++)this.byteWidths[i]=f.lookup(i,q).asNumber()}return e.prototype.parseIntoContext=function(){if(this.alreadyParsed)throw new wa("PDFXRefStreamParser","parseIntoContext");this.alreadyParsed=!0,this.context.trailerInfo={Root:this.dict.get(p.of("Root")),Encrypt:this.dict.get(p.of("Encrypt")),Info:this.dict.get(p.of("Info")),ID:this.dict.get(p.of("ID"))};var t=this.parseEntries();return t},e.prototype.parseEntries=function(){for(var t=[],r=this.byteWidths,n=r[0],i=r[1],a=r[2],o=0,s=this.subsections.length;o<s;o++)for(var f=this.subsections[o],u=f.firstObjectNumber,c=f.length,l=0;l<c;l++){for(var h=0,d=0,v=n;d<v;d++)h=h<<8|this.bytes.next();for(var g=0,d=0,v=i;d<v;d++)g=g<<8|this.bytes.next();for(var m=0,d=0,v=a;d<v;d++)m=m<<8|this.bytes.next();n===0&&(h=1);var b=u+l,F={ref:fe.of(b,m),offset:g,deleted:h===0,inObjectStream:h===2};t.push(F)}return t},e.forStream=function(t){return new e(t)},e}(),Yh=function(e){E(t,e);function t(r,n,i,a){n===void 0&&(n=1/0),i===void 0&&(i=!1),a===void 0&&(a=!1);var o=e.call(this,wi.of(r),ia.create(),a)||this;return o.alreadyParsed=!1,o.parsedObjects=0,o.shouldWaitForTick=function(){return o.parsedObjects+=1,o.parsedObjects%o.objectsPerTick===0},o.objectsPerTick=n,o.throwOnInvalidObject=i,o}return t.prototype.parseDocument=function(){return Y(this,void 0,void 0,function(){var r,n;return J(this,function(i){switch(i.label){case 0:if(this.alreadyParsed)throw new wa("PDFParser","parseDocument");this.alreadyParsed=!0,this.context.header=this.parseHeader(),i.label=1;case 1:return this.bytes.done()?[3,3]:[4,this.parseDocumentSection()];case 2:if(i.sent(),n=this.bytes.offset(),n===r)throw new nh(this.bytes.position());return r=n,[3,1];case 3:return this.maybeRecoverRoot(),this.context.lookup(fe.of(0))&&(console.warn("Removing parsed object: 0 0 R"),this.context.delete(fe.of(0))),[2,this.context]}})})},t.prototype.maybeRecoverRoot=function(){var r=function(c){return c instanceof Z&&c.lookup(p.of("Type"))===p.of("Catalog")},n=this.context.lookup(this.context.trailerInfo.Root);if(!r(n))for(var i=this.context.enumerateIndirectObjects(),a=0,o=i.length;a<o;a++){var s=i[a],f=s[0],u=s[1];r(u)&&(this.context.trailerInfo.Root=f)}},t.prototype.parseHeader=function(){for(;!this.bytes.done();){if(this.matchKeyword(le.header)){var r=this.parseRawInt();this.bytes.assertNext(y.Period);var n=this.parseRawInt(),i=di.forVersion(r,n);return this.skipBinaryHeaderComment(),i}this.bytes.next()}throw new ih(this.bytes.position())},t.prototype.parseIndirectObjectHeader=function(){this.skipWhitespaceAndComments();var r=this.parseRawInt();this.skipWhitespaceAndComments();var n=this.parseRawInt();if(this.skipWhitespaceAndComments(),!this.matchKeyword(le.obj))throw new ah(this.bytes.position(),le.obj);return fe.of(r,n)},t.prototype.matchIndirectObjectHeader=function(){var r=this.bytes.offset();try{return this.parseIndirectObjectHeader(),!0}catch{return this.bytes.moveTo(r),!1}},t.prototype.parseIndirectObject=function(){return Y(this,void 0,void 0,function(){var r,n;return J(this,function(i){switch(i.label){case 0:return r=this.parseIndirectObjectHeader(),this.skipWhitespaceAndComments(),n=this.parseObject(),this.skipWhitespaceAndComments(),this.matchKeyword(le.endobj),n instanceof en&&n.dict.lookup(p.of("Type"))===p.of("ObjStm")?[4,Xh.forStream(n,this.shouldWaitForTick).parseIntoContext()]:[3,2];case 1:return i.sent(),[3,3];case 2:n instanceof en&&n.dict.lookup(p.of("Type"))===p.of("XRef")?Zh.forStream(n).parseIntoContext():this.context.assign(r,n),i.label=3;case 3:return[2,r]}})})},t.prototype.tryToParseInvalidIndirectObject=function(){var r=this.bytes.position(),n="Trying to parse invalid object: "+JSON.stringify(r)+")";if(this.throwOnInvalidObject)throw new Error(n);console.warn(n);var i=this.parseIndirectObjectHeader();console.warn("Invalid object ref: "+i),this.skipWhitespaceAndComments();for(var a=this.bytes.offset(),o=!0;!this.bytes.done()&&(this.matchKeyword(le.endobj)&&(o=!1),!!o);)this.bytes.next();if(o)throw new eh(r);var s=this.bytes.offset()-le.endobj.length,f=du.of(this.bytes.slice(a,s));return this.context.assign(i,f),i},t.prototype.parseIndirectObjects=function(){return Y(this,void 0,void 0,function(){var r;return J(this,function(n){switch(n.label){case 0:this.skipWhitespaceAndComments(),n.label=1;case 1:if(!(!this.bytes.done()&&Pe[this.bytes.peek()]))return[3,8];r=this.bytes.offset(),n.label=2;case 2:return n.trys.push([2,4,,5]),[4,this.parseIndirectObject()];case 3:return n.sent(),[3,5];case 4:return n.sent(),this.bytes.moveTo(r),this.tryToParseInvalidIndirectObject(),[3,5];case 5:return this.skipWhitespaceAndComments(),this.skipJibberish(),this.shouldWaitForTick()?[4,dr()]:[3,7];case 6:n.sent(),n.label=7;case 7:return[3,1];case 8:return[2]}})})},t.prototype.maybeParseCrossRefSection=function(){if(this.skipWhitespaceAndComments(),!!this.matchKeyword(le.xref)){this.skipWhitespaceAndComments();for(var r=-1,n=cu.createEmpty();!this.bytes.done()&&Pe[this.bytes.peek()];){var i=this.parseRawInt();this.skipWhitespaceAndComments();var a=this.parseRawInt();this.skipWhitespaceAndComments();var o=this.bytes.peek();if(o===y.n||o===y.f){var s=fe.of(r,a);this.bytes.next()===y.n?n.addEntry(s,i):n.addDeletedEntry(s,i),r+=1}else r=i;this.skipWhitespaceAndComments()}return n}},t.prototype.maybeParseTrailerDict=function(){if(this.skipWhitespaceAndComments(),!!this.matchKeyword(le.trailer)){this.skipWhitespaceAndComments();var r=this.parseDict(),n=this.context;n.trailerInfo={Root:r.get(p.of("Root"))||n.trailerInfo.Root,Encrypt:r.get(p.of("Encrypt"))||n.trailerInfo.Encrypt,Info:r.get(p.of("Info"))||n.trailerInfo.Info,ID:r.get(p.of("ID"))||n.trailerInfo.ID}}},t.prototype.maybeParseTrailer=function(){if(this.skipWhitespaceAndComments(),!!this.matchKeyword(le.startxref)){this.skipWhitespaceAndComments();var r=this.parseRawInt();return this.skipWhitespace(),this.matchKeyword(le.eof),this.skipWhitespaceAndComments(),this.matchKeyword(le.eof),this.skipWhitespaceAndComments(),Ca.forLastCrossRefSectionOffset(r)}},t.prototype.parseDocumentSection=function(){return Y(this,void 0,void 0,function(){return J(this,function(r){switch(r.label){case 0:return[4,this.parseIndirectObjects()];case 1:return r.sent(),this.maybeParseCrossRefSection(),this.maybeParseTrailerDict(),this.maybeParseTrailer(),this.skipJibberish(),[2]}})})},t.prototype.skipJibberish=function(){for(this.skipWhitespaceAndComments();!this.bytes.done();){var r=this.bytes.offset(),n=this.bytes.peek(),i=n>=y.Space&&n<=y.Tilde;if(i&&(this.matchKeyword(le.xref)||this.matchKeyword(le.trailer)||this.matchKeyword(le.startxref)||this.matchIndirectObjectHeader())){this.bytes.moveTo(r);break}this.bytes.next()}},t.prototype.skipBinaryHeaderComment=function(){this.skipWhitespaceAndComments();try{var r=this.bytes.offset();this.parseIndirectObjectHeader(),this.bytes.moveTo(r)}catch{this.bytes.next(),this.skipWhitespaceAndComments()}},t.forBytesWithOptions=function(r,n,i,a){return new t(r,n,i,a)},t}(Cu),ot=function(e){return 1<<e},Hr;(function(e){e[e.Invisible=ot(1-1)]="Invisible",e[e.Hidden=ot(2-1)]="Hidden",e[e.Print=ot(3-1)]="Print",e[e.NoZoom=ot(4-1)]="NoZoom",e[e.NoRotate=ot(5-1)]="NoRotate",e[e.NoView=ot(6-1)]="NoView",e[e.ReadOnly=ot(7-1)]="ReadOnly",e[e.Locked=ot(8-1)]="Locked",e[e.ToggleNoView=ot(9-1)]="ToggleNoView",e[e.LockedContents=ot(10-1)]="LockedContents"})(Hr||(Hr={}));var Si=function(e){return e instanceof p?e:p.of(e)},K=function(e){return e instanceof q?e:q.of(e)},Q=function(e){return e instanceof q?e.asNumber():e},tn;(function(e){e.Degrees="degrees",e.Radians="radians"})(tn||(tn={}));var V=function(e){return x(e,"degreeAngle",["number"]),{type:tn.Degrees,angle:e}},Tu=tn.Radians,Pu=tn.Degrees,Au=function(e){return e*Math.PI/180},Jh=function(e){return e*180/Math.PI},Te=function(e){return e.type===Tu?e.angle:e.type===Pu?Au(e.angle):on("Invalid rotation: "+JSON.stringify(e))},Du=function(e){return e.type===Tu?Jh(e.angle):e.type===Pu?e.angle:on("Invalid rotation: "+JSON.stringify(e))},mt=function(e){e===void 0&&(e=0);var t=e/90%4;return t===0?0:t===1?90:t===2?180:t===3?270:0},Jt=function(e,t){t===void 0&&(t=0);var r=mt(t);return r===90||r===270?{width:e.height,height:e.width}:{width:e.width,height:e.height}},Qh=function(e,t,r){t===void 0&&(t=0),r===void 0&&(r=0);var n=e.x,i=e.y,a=e.width,o=e.height,s=mt(r),f=t/2;return s===0?{x:n-f,y:i-f,width:a,height:o}:s===90?{x:n-o+f,y:i-f,width:o,height:a}:s===180?{x:n-a+f,y:i-o+f,width:a,height:o}:s===270?{x:n-f,y:i-a+f,width:o,height:a}:{x:n-f,y:i-f,width:a,height:o}},Ru=function(){return ee.of(ne.ClipNonZero)},ei=Math.cos,ti=Math.sin,ri=Math.tan,Fi=function(e,t,r,n,i,a){return ee.of(ne.ConcatTransformationMatrix,[K(e),K(t),K(r),K(n),K(i),K(a)])},Ge=function(e,t){return Fi(1,0,0,1,e,t)},rn=function(e,t){return Fi(e,0,0,t,0,0)},mr=function(e){return Fi(ei(Q(e)),ti(Q(e)),-ti(Q(e)),ei(Q(e)),0,0)},Rn=function(e){return mr(Au(Q(e)))},Ea=function(e,t){return Fi(1,ri(Q(e)),ri(Q(t)),1,0,0)},ki=function(e,t){return ee.of(ne.SetLineDashPattern,["["+e.map(K).join(" ")+"]",K(t)])},ur;(function(e){e[e.Butt=0]="Butt",e[e.Round=1]="Round",e[e.Projecting=2]="Projecting"})(ur||(ur={}));var Ci=function(e){return ee.of(ne.SetLineCapStyle,[K(e)])},$o;(function(e){e[e.Miter=0]="Miter",e[e.Round=1]="Round",e[e.Bevel=2]="Bevel"})($o||($o={}));var Qt=function(e){return ee.of(ne.SetGraphicsStateParams,[Si(e)])},xe=function(){return ee.of(ne.PushGraphicsState)},we=function(){return ee.of(ne.PopGraphicsState)},hn=function(e){return ee.of(ne.SetLineWidth,[K(e)])},Me=function(e,t,r,n,i,a){return ee.of(ne.AppendBezierCurve,[K(e),K(t),K(r),K(n),K(i),K(a)])},On=function(e,t,r,n){return ee.of(ne.CurveToReplicateInitialPoint,[K(e),K(t),K(r),K(n)])},Bt=function(){return ee.of(ne.ClosePath)},ft=function(e,t){return ee.of(ne.MoveTo,[K(e),K(t)])},Se=function(e,t){return ee.of(ne.LineTo,[K(e),K(t)])},dn=function(){return ee.of(ne.StrokePath)},Ba=function(){return ee.of(ne.FillNonZero)},Na=function(){return ee.of(ne.FillNonZeroAndStroke)},Ou=function(){return ee.of(ne.EndPath)},_h=function(){return ee.of(ne.NextLine)},Eu=function(e){return ee.of(ne.ShowText,[e])},Bu=function(){return ee.of(ne.BeginText)},Nu=function(){return ee.of(ne.EndText)},za=function(e,t){return ee.of(ne.SetFontAndSize,[Si(e),K(t)])},$h=function(e){return ee.of(ne.SetTextLineHeight,[K(e)])},es;(function(e){e[e.Fill=0]="Fill",e[e.Outline=1]="Outline",e[e.FillAndOutline=2]="FillAndOutline",e[e.Invisible=3]="Invisible",e[e.FillAndClip=4]="FillAndClip",e[e.OutlineAndClip=5]="OutlineAndClip",e[e.FillAndOutlineAndClip=6]="FillAndOutlineAndClip",e[e.Clip=7]="Clip"})(es||(es={}));var e0=function(e,t,r,n,i,a){return ee.of(ne.SetTextMatrix,[K(e),K(t),K(r),K(n),K(i),K(a)])},zu=function(e,t,r,n,i){return e0(ei(Q(e)),ti(Q(e))+ri(Q(t)),-ti(Q(e))+ri(Q(r)),ei(Q(e)),n,i)},ja=function(e){return ee.of(ne.DrawObject,[Si(e)])},t0=function(e){return ee.of(ne.NonStrokingColorGray,[K(e)])},r0=function(e){return ee.of(ne.StrokingColorGray,[K(e)])},n0=function(e,t,r){return ee.of(ne.NonStrokingColorRgb,[K(e),K(t),K(r)])},i0=function(e,t,r){return ee.of(ne.StrokingColorRgb,[K(e),K(t),K(r)])},a0=function(e,t,r,n){return ee.of(ne.NonStrokingColorCmyk,[K(e),K(t),K(r),K(n)])},o0=function(e,t,r,n){return ee.of(ne.StrokingColorCmyk,[K(e),K(t),K(r),K(n)])},ju=function(e){return ee.of(ne.BeginMarkedContent,[Si(e)])},Mu=function(){return ee.of(ne.EndMarkedContent)},Nt;(function(e){e.Grayscale="Grayscale",e.RGB="RGB",e.CMYK="CMYK"})(Nt||(Nt={}));var Iu=function(e){return je(e,"gray",0,1),{type:Nt.Grayscale,gray:e}},ce=function(e,t,r){return je(e,"red",0,1),je(t,"green",0,1),je(r,"blue",0,1),{type:Nt.RGB,red:e,green:t,blue:r}},Uu=function(e,t,r,n){return je(e,"cyan",0,1),je(t,"magenta",0,1),je(r,"yellow",0,1),je(n,"key",0,1),{type:Nt.CMYK,cyan:e,magenta:t,yellow:r,key:n}},Ma=Nt.Grayscale,Ia=Nt.RGB,Ua=Nt.CMYK,_t=function(e){return e.type===Ma?t0(e.gray):e.type===Ia?n0(e.red,e.green,e.blue):e.type===Ua?a0(e.cyan,e.magenta,e.yellow,e.key):on("Invalid color: "+JSON.stringify(e))},vn=function(e){return e.type===Ma?r0(e.gray):e.type===Ia?i0(e.red,e.green,e.blue):e.type===Ua?o0(e.cyan,e.magenta,e.yellow,e.key):on("Invalid color: "+JSON.stringify(e))},Re=function(e,t){return t===void 0&&(t=1),(e==null?void 0:e.length)===1?Iu(e[0]*t):(e==null?void 0:e.length)===3?ce(e[0]*t,e[1]*t,e[2]*t):(e==null?void 0:e.length)===4?Uu(e[0]*t,e[1]*t,e[2]*t,e[3]*t):void 0},ts=function(e){return e.type===Ma?[e.gray]:e.type===Ia?[e.red,e.green,e.blue]:e.type===Ua?[e.cyan,e.magenta,e.yellow,e.key]:on("Invalid color: "+JSON.stringify(e))},M=0,I=0,_=0,$=0,Br=0,Nr=0,rs=new Map([["A",7],["a",7],["C",6],["c",6],["H",1],["h",1],["L",2],["l",2],["M",2],["m",2],["Q",4],["q",4],["S",4],["s",4],["T",2],["t",2],["V",1],["v",1],["Z",0],["z",0]]),s0=function(e){for(var t,r=[],n=[],i="",a=!1,o=0,s=0,f=e;s<f.length;s++){var u=f[s];if(rs.has(u))o=rs.get(u),t&&(i.length>0&&(n[n.length]=+i),r[r.length]={cmd:t,args:n},n=[],i="",a=!1),t=u;else if([" ",","].includes(u)||u==="-"&&i.length>0&&i[i.length-1]!=="e"||u==="."&&a){if(i.length===0)continue;n.length===o?(r[r.length]={cmd:t,args:n},n=[+i],t==="M"&&(t="L"),t==="m"&&(t="l")):n[n.length]=+i,a=u===".",i=["-","."].includes(u)?u:""}else i+=u,u==="."&&(a=!0)}return i.length>0&&(n.length===o?(r[r.length]={cmd:t,args:n},n=[+i],t==="M"&&(t="L"),t==="m"&&(t="l")):n[n.length]=+i),r[r.length]={cmd:t,args:n},r},u0=function(e){M=I=_=$=Br=Nr=0;for(var t=[],r=0;r<e.length;r++){var n=e[r];if(n.cmd&&typeof ns[n.cmd]=="function"){var i=ns[n.cmd](n.args);Array.isArray(i)?t=t.concat(i):t.push(i)}}return t},ns={M:function(e){return M=e[0],I=e[1],_=$=null,Br=M,Nr=I,ft(M,I)},m:function(e){return M+=e[0],I+=e[1],_=$=null,Br=M,Nr=I,ft(M,I)},C:function(e){return M=e[4],I=e[5],_=e[2],$=e[3],Me(e[0],e[1],e[2],e[3],e[4],e[5])},c:function(e){var t=Me(e[0]+M,e[1]+I,e[2]+M,e[3]+I,e[4]+M,e[5]+I);return _=M+e[2],$=I+e[3],M+=e[4],I+=e[5],t},S:function(e){(_===null||$===null)&&(_=M,$=I);var t=Me(M-(_-M),I-($-I),e[0],e[1],e[2],e[3]);return _=e[0],$=e[1],M=e[2],I=e[3],t},s:function(e){(_===null||$===null)&&(_=M,$=I);var t=Me(M-(_-M),I-($-I),M+e[0],I+e[1],M+e[2],I+e[3]);return _=M+e[0],$=I+e[1],M+=e[2],I+=e[3],t},Q:function(e){return _=e[0],$=e[1],M=e[2],I=e[3],On(e[0],e[1],M,I)},q:function(e){var t=On(e[0]+M,e[1]+I,e[2]+M,e[3]+I);return _=M+e[0],$=I+e[1],M+=e[2],I+=e[3],t},T:function(e){_===null||$===null?(_=M,$=I):(_=M-(_-M),$=I-($-I));var t=On(_,$,e[0],e[1]);return _=M-(_-M),$=I-($-I),M=e[0],I=e[1],t},t:function(e){_===null||$===null?(_=M,$=I):(_=M-(_-M),$=I-($-I));var t=On(_,$,M+e[0],I+e[1]);return M+=e[0],I+=e[1],t},A:function(e){var t=is(M,I,e);return M=e[5],I=e[6],t},a:function(e){e[5]+=M,e[6]+=I;var t=is(M,I,e);return M=e[5],I=e[6],t},L:function(e){return M=e[0],I=e[1],_=$=null,Se(M,I)},l:function(e){return M+=e[0],I+=e[1],_=$=null,Se(M,I)},H:function(e){return M=e[0],_=$=null,Se(M,I)},h:function(e){return M+=e[0],_=$=null,Se(M,I)},V:function(e){return I=e[0],_=$=null,Se(M,I)},v:function(e){return I+=e[0],_=$=null,Se(M,I)},Z:function(){var e=Bt();return M=Br,I=Nr,e},z:function(){var e=Bt();return M=Br,I=Nr,e}},is=function(e,t,r){for(var n=r[0],i=r[1],a=r[2],o=r[3],s=r[4],f=r[5],u=r[6],c=f0(f,u,n,i,o,s,a,e,t),l=[],h=0,d=c;h<d.length;h++){var v=d[h],g=c0.apply(void 0,v);l.push(Me.apply(void 0,g))}return l},f0=function(e,t,r,n,i,a,o,s,f){var u=o*(Math.PI/180),c=Math.sin(u),l=Math.cos(u);r=Math.abs(r),n=Math.abs(n),_=l*(s-e)*.5+c*(f-t)*.5,$=l*(f-t)*.5-c*(s-e)*.5;var h=_*_/(r*r)+$*$/(n*n);h>1&&(h=Math.sqrt(h),r*=h,n*=h);var d=l/r,v=c/r,g=-c/n,m=l/n,b=d*s+v*f,F=g*s+m*f,w=d*e+v*t,C=g*e+m*t,S=(w-b)*(w-b)+(C-F)*(C-F),k=1/S-.25;k<0&&(k=0);var T=Math.sqrt(k);a===i&&(T=-T);var P=.5*(b+w)-T*(C-F),D=.5*(F+C)+T*(w-b),N=Math.atan2(F-D,b-P),A=Math.atan2(C-D,w-P),z=A-N;z<0&&a===1?z+=2*Math.PI:z>0&&a===0&&(z-=2*Math.PI);for(var B=Math.ceil(Math.abs(z/(Math.PI*.5+.001))),W=[],L=0;L<B;L++){var j=N+L*z/B,ie=N+(L+1)*z/B;W[L]=[P,D,j,ie,r,n,c,l]}return W},c0=function(e,t,r,n,i,a,o,s){var f=s*i,u=-o*a,c=o*i,l=s*a,h=.5*(n-r),d=8/3*Math.sin(h*.5)*Math.sin(h*.5)/Math.sin(h),v=e+Math.cos(r)-d*Math.sin(r),g=t+Math.sin(r)+d*Math.cos(r),m=e+Math.cos(n),b=t+Math.sin(n),F=m+d*Math.sin(n),w=b-d*Math.cos(n),C=[f*v+u*g,c*v+l*g,f*F+u*w,c*F+l*w,f*m+u*b,c*m+l*b];return C},l0=function(e){return u0(s0(e))},h0=function(e,t){for(var r=[xe(),t.graphicsState&&Qt(t.graphicsState),Bu(),_t(t.color),za(t.font,t.size),$h(t.lineHeight),zu(Te(t.rotate),Te(t.xSkew),Te(t.ySkew),t.x,t.y)].filter(Boolean),n=0,i=e.length;n<i;n++)r.push(Eu(e[n]),_h());return r.push(Nu(),we()),r},Vu=function(e,t){return[xe(),t.graphicsState&&Qt(t.graphicsState),Ge(t.x,t.y),mr(Te(t.rotate)),rn(t.width,t.height),Ea(Te(t.xSkew),Te(t.ySkew)),ja(e),we()].filter(Boolean)},d0=function(e,t){return[xe(),t.graphicsState&&Qt(t.graphicsState),Ge(t.x,t.y),mr(Te(t.rotate)),rn(t.xScale,t.yScale),Ea(Te(t.xSkew),Te(t.ySkew)),ja(e),we()].filter(Boolean)},v0=function(e){var t,r;return[xe(),e.graphicsState&&Qt(e.graphicsState),e.color&&vn(e.color),hn(e.thickness),ki((t=e.dashArray)!==null&&t!==void 0?t:[],(r=e.dashPhase)!==null&&r!==void 0?r:0),ft(e.start.x,e.start.y),e.lineCap&&Ci(e.lineCap),ft(e.start.x,e.start.y),Se(e.end.x,e.end.y),dn(),we()].filter(Boolean)},vr=function(e){var t,r;return[xe(),e.graphicsState&&Qt(e.graphicsState),e.color&&_t(e.color),e.borderColor&&vn(e.borderColor),hn(e.borderWidth),e.borderLineCap&&Ci(e.borderLineCap),ki((t=e.borderDashArray)!==null&&t!==void 0?t:[],(r=e.borderDashPhase)!==null&&r!==void 0?r:0),Ge(e.x,e.y),mr(Te(e.rotate)),Ea(Te(e.xSkew),Te(e.ySkew)),ft(0,0),Se(0,e.height),Se(e.width,e.height),Se(e.width,0),Bt(),e.color&&e.borderWidth?Na():e.color?Ba():e.borderColor?dn():Bt(),we()].filter(Boolean)},ni=4*((Math.sqrt(2)-1)/3),p0=function(e){var t=Q(e.x),r=Q(e.y),n=Q(e.xScale),i=Q(e.yScale);t-=n,r-=i;var a=n*ni,o=i*ni,s=t+n*2,f=r+i*2,u=t+n,c=r+i;return[xe(),ft(t,c),Me(t,c-o,u-a,r,u,r),Me(u+a,r,s,c-o,s,c),Me(s,c+o,u+a,f,u,f),Me(u-a,f,t,c+o,t,c),we()]},g0=function(e){var t=Q(e.x),r=Q(e.y),n=Q(e.xScale),i=Q(e.yScale),a=-n,o=-i,s=n*ni,f=i*ni,u=a+n*2,c=o+i*2,l=a+n,h=o+i;return[Ge(t,r),mr(Te(e.rotate)),ft(a,h),Me(a,h-f,l-s,o,l,o),Me(l+s,o,u,h-f,u,h),Me(u,h+f,l+s,c,l,c),Me(l-s,c,a,h+f,a,h)]},ua=function(e){var t,r,n;return re([xe(),e.graphicsState&&Qt(e.graphicsState),e.color&&_t(e.color),e.borderColor&&vn(e.borderColor),hn(e.borderWidth),e.borderLineCap&&Ci(e.borderLineCap),ki((t=e.borderDashArray)!==null&&t!==void 0?t:[],(r=e.borderDashPhase)!==null&&r!==void 0?r:0)],e.rotate===void 0?p0({x:e.x,y:e.y,xScale:e.xScale,yScale:e.yScale}):g0({x:e.x,y:e.y,xScale:e.xScale,yScale:e.yScale,rotate:(n=e.rotate)!==null&&n!==void 0?n:V(0)}),[e.color&&e.borderWidth?Na():e.color?Ba():e.borderColor?dn():Bt(),we()]).filter(Boolean)},y0=function(e,t){var r,n,i;return re([xe(),t.graphicsState&&Qt(t.graphicsState),Ge(t.x,t.y),mr(Te((r=t.rotate)!==null&&r!==void 0?r:V(0))),t.scale?rn(t.scale,-t.scale):rn(1,-1),t.color&&_t(t.color),t.borderColor&&vn(t.borderColor),t.borderWidth&&hn(t.borderWidth),t.borderLineCap&&Ci(t.borderLineCap),ki((n=t.borderDashArray)!==null&&n!==void 0?n:[],(i=t.borderDashPhase)!==null&&i!==void 0?i:0)],l0(e),[t.color&&t.borderWidth?Na():t.color?Ba():t.borderColor?dn():Bt(),we()]).filter(Boolean)},b0=function(e){var t=Q(e.size),r=-1+.75,n=-1+.51,i=1-.525,a=1-.31,o=-1+.325,s=-((o-r)*(a-r))/(i-n)+n;return[xe(),e.color&&vn(e.color),hn(e.thickness),Ge(e.x,e.y),ft(o*t,s*t),Se(r*t,n*t),Se(a*t,i*t),dn(),we()].filter(Boolean)},Mt=function(e){return e.rotation===0?[Ge(0,0),Rn(0)]:e.rotation===90?[Ge(e.width,0),Rn(90)]:e.rotation===180?[Ge(e.width,e.height),Rn(180)]:e.rotation===270?[Ge(0,e.height),Rn(270)]:[]},En=function(e){var t=vr({x:e.x,y:e.y,width:e.width,height:e.height,borderWidth:e.borderWidth,color:e.color,borderColor:e.borderColor,rotate:V(0),xSkew:V(0),ySkew:V(0)});if(!e.filled)return t;var r=Q(e.width),n=Q(e.height),i=Math.min(r,n)/2,a=b0({x:r/2,y:n/2,size:i,thickness:e.thickness,color:e.markColor});return re([xe()],t,a,[we()])},Bn=function(e){var t=Q(e.width),r=Q(e.height),n=Math.min(t,r)/2,i=ua({x:e.x,y:e.y,xScale:n,yScale:n,color:e.color,borderColor:e.borderColor,borderWidth:e.borderWidth});if(!e.filled)return i;var a=ua({x:e.x,y:e.y,xScale:n*.45,yScale:n*.45,color:e.dotColor,borderColor:void 0,borderWidth:0});return re([xe()],i,a,[we()])},as=function(e){var t=Q(e.x),r=Q(e.y),n=Q(e.width),i=Q(e.height),a=vr({x:t,y:r,width:n,height:i,borderWidth:e.borderWidth,color:e.color,borderColor:e.borderColor,rotate:V(0),xSkew:V(0),ySkew:V(0)}),o=Va(e.textLines,{color:e.textColor,font:e.font,size:e.fontSize,rotate:V(0),xSkew:V(0),ySkew:V(0)});return re([xe()],a,o,[we()])},Va=function(e,t){for(var r=[Bu(),_t(t.color),za(t.font,t.size)],n=0,i=e.length;n<i;n++){var a=e[n],o=a.encoded,s=a.x,f=a.y;r.push(zu(Te(t.rotate),Te(t.xSkew),Te(t.ySkew),s,f),Eu(o))}return r.push(Nu()),r},Wu=function(e){var t=Q(e.x),r=Q(e.y),n=Q(e.width),i=Q(e.height),a=Q(e.borderWidth),o=Q(e.padding),s=t+a/2+o,f=r+a/2+o,u=n-(a/2+o)*2,c=i-(a/2+o)*2,l=[ft(s,f),Se(s,f+c),Se(s+u,f+c),Se(s+u,f),Bt(),Ru(),Ou()],h=vr({x:t,y:r,width:n,height:i,borderWidth:e.borderWidth,color:e.color,borderColor:e.borderColor,rotate:V(0),xSkew:V(0),ySkew:V(0)}),d=Va(e.textLines,{color:e.textColor,font:e.font,size:e.fontSize,rotate:V(0),xSkew:V(0),ySkew:V(0)}),v=re([ju("Tx"),xe()],d,[we(),Mu()]);return re([xe()],h,l,v,[we()])},m0=function(e){for(var t=Q(e.x),r=Q(e.y),n=Q(e.width),i=Q(e.height),a=Q(e.lineHeight),o=Q(e.borderWidth),s=Q(e.padding),f=t+o/2+s,u=r+o/2+s,c=n-(o/2+s)*2,l=i-(o/2+s)*2,h=[ft(f,u),Se(f,u+l),Se(f+c,u+l),Se(f+c,u),Bt(),Ru(),Ou()],d=vr({x:t,y:r,width:n,height:i,borderWidth:e.borderWidth,color:e.color,borderColor:e.borderColor,rotate:V(0),xSkew:V(0),ySkew:V(0)}),v=[],g=0,m=e.selectedLines.length;g<m;g++){var b=e.textLines[e.selectedLines[g]];v.push.apply(v,vr({x:b.x-s,y:b.y-(a-b.height)/2,width:n-o,height:b.height+(a-b.height)/2,borderWidth:0,color:e.selectedColor,borderColor:void 0,rotate:V(0),xSkew:V(0),ySkew:V(0)}))}var F=Va(e.textLines,{color:e.textColor,font:e.font,size:e.fontSize,rotate:V(0),xSkew:V(0),ySkew:V(0)}),w=re([ju("Tx"),xe()],F,[we(),Mu()]);return re([xe()],d,v,h,w,[we()])},x0=function(e){E(t,e);function t(){var r=this,n="Input document to `PDFDocument.load` is encrypted. You can use `PDFDocument.load(..., { ignoreEncryption: true })` if you wish to load the document anyways.";return r=e.call(this,n)||this,r}return t}(Error),w0=function(e){E(t,e);function t(){var r=this,n="Input to `PDFDocument.embedFont` was a custom font, but no `fontkit` instance was found. You must register a `fontkit` instance with `PDFDocument.registerFontkit(...)` before embedding custom fonts.";return r=e.call(this,n)||this,r}return t}(Error),S0=function(e){E(t,e);function t(){var r=this,n="A `page` passed to `PDFDocument.addPage` or `PDFDocument.insertPage` was from a different (foreign) PDF document. If you want to copy pages from one PDFDocument to another, you must use `PDFDocument.copyPages(...)` to copy the pages before adding or inserting them.";return r=e.call(this,n)||this,r}return t}(Error),F0=function(e){E(t,e);function t(){var r=this,n="PDFDocument has no pages so `PDFDocument.removePage` cannot be called";return r=e.call(this,n)||this,r}return t}(Error),k0=function(e){E(t,e);function t(r){var n=this,i='PDFDocument has no form field with the name "'+r+'"';return n=e.call(this,i)||this,n}return t}(Error),It=function(e){E(t,e);function t(r,n,i){var a,o,s=this,f=n==null?void 0:n.name,u=(o=(a=i==null?void 0:i.constructor)===null||a===void 0?void 0:a.name)!==null&&o!==void 0?o:i,c='Expected field "'+r+'" to be of type '+f+", "+("but it is actually of type "+u);return s=e.call(this,c)||this,s}return t}(Error);(function(e){E(t,e);function t(r){var n=this,i='Failed to select check box due to missing onValue: "'+r+'"';return n=e.call(this,i)||this,n}return t})(Error);var qu=function(e){E(t,e);function t(r){var n=this,i='A field already exists with the specified name: "'+r+'"';return n=e.call(this,i)||this,n}return t}(Error),C0=function(e){E(t,e);function t(r){var n=this,i='Field name contains invalid component: "'+r+'"';return n=e.call(this,i)||this,n}return t}(Error);(function(e){E(t,e);function t(r){var n=this,i='A non-terminal field already exists with the specified name: "'+r+'"';return n=e.call(this,i)||this,n}return t})(Error);var T0=function(e){E(t,e);function t(r){var n=this,i="Reading rich text fields is not supported: Attempted to read rich text field: "+r;return n=e.call(this,i)||this,n}return t}(Error),P0=function(e){E(t,e);function t(r,n){var i=this,a="Failed to layout combed text as lineLength="+r+" is greater than cellCount="+n;return i=e.call(this,a)||this,i}return t}(Error),A0=function(e){E(t,e);function t(r,n,i){var a=this,o="Attempted to set text with length="+r+" for TextField with maxLength="+n+" and name="+i;return a=e.call(this,o)||this,a}return t}(Error),D0=function(e){E(t,e);function t(r,n,i){var a=this,o="Attempted to set maxLength="+n+", which is less than "+r+", the length of this field's current value (name="+i+")";return a=e.call(this,o)||this,a}return t}(Error),be;(function(e){e[e.Left=0]="Left",e[e.Center=1]="Center",e[e.Right=2]="Right"})(be||(be={}));var Ku=4,Lu=500,Gu=function(e,t,r,n){n===void 0&&(n=!1);for(var i=Ku;i<Lu;){for(var a=0,o=0,s=e.length;o<s;o++){a+=1;for(var f=e[o],u=f.split(" "),c=r.width,l=0,h=u.length;l<h;l++){var d=l===h-1,v=d?u[l]:u[l]+" ",g=t.widthOfTextAtSize(v,i);c-=g,c<=0&&(a+=1,c=r.width-g)}}if(!n&&a>e.length)return i-1;var m=t.heightAtSize(i),b=m+m*.2,F=b*a;if(F>Math.abs(r.height))return i-1;i+=1}return i},R0=function(e,t,r,n){for(var i=r.width/n,a=r.height,o=Ku,s=af(e);o<Lu;){for(var f=0,u=s.length;f<u;f++){var c=s[f],l=t.widthOfTextAtSize(c,o)>i*.75;if(l)return o-1}var h=t.heightAtSize(o,{descender:!1});if(h>a)return o-1;o+=1}return o},O0=function(e){for(var t=e.length;t>0;t--)if(/\s/.test(e[t]))return t},E0=function(e,t,r,n){for(var i,a=e.length;a>0;){var o=e.substring(0,a),s=r.encodeText(o),f=r.widthOfTextAtSize(o,n);if(f<t){var u=e.substring(a)||void 0;return{line:o,encoded:s,width:f,remainder:u}}a=(i=O0(o))!==null&&i!==void 0?i:0}return{line:e,encoded:r.encodeText(e),width:r.widthOfTextAtSize(e,n),remainder:void 0}},Hu=function(e,t){var r=t.alignment,n=t.fontSize,i=t.font,a=t.bounds,o=ss(an(e));(n===void 0||n===0)&&(n=Gu(o,i,a,!0));for(var s=i.heightAtSize(n),f=s+s*.2,u=[],c=a.x,l=a.y,h=a.x+a.width,d=a.y+a.height,v=a.y+a.height,g=0,m=o.length;g<m;g++)for(var b=o[g];b!==void 0;){var F=E0(b,a.width,i,n),w=F.line,C=F.encoded,S=F.width,k=F.remainder,T=r===be.Left?a.x:r===be.Center?a.x+a.width/2-S/2:r===be.Right?a.x+a.width-S:a.x;v-=f,T<c&&(c=T),v<l&&(l=v),T+S>h&&(h=T+S),v+s>d&&(d=v+s),u.push({text:w,encoded:C,width:S,height:s,x:T,y:v}),b=k==null?void 0:k.trim()}return{fontSize:n,lineHeight:f,lines:u,bounds:{x:c,y:l,width:h-c,height:d-l}}},B0=function(e,t){var r=t.fontSize,n=t.font,i=t.bounds,a=t.cellCount,o=us(an(e));if(o.length>a)throw new P0(o.length,a);(r===void 0||r===0)&&(r=R0(o,n,i,a));for(var s=i.width/a,f=n.heightAtSize(r,{descender:!1}),u=i.y+(i.height/2-f/2),c=[],l=i.x,h=i.y,d=i.x+i.width,v=i.y+i.height,g=0,m=0;g<a;){var b=fs(o,m),F=b[0],w=b[1],C=n.encodeText(F),S=n.widthOfTextAtSize(F,r),k=i.x+(s*g+s/2),T=k-S/2;T<l&&(l=T),u<h&&(h=u),T+S>d&&(d=T+S),u+f>v&&(v=u+f),c.push({text:o,encoded:C,width:S,height:f,x:T,y:u}),g+=1,m+=w}return{fontSize:r,cells:c,bounds:{x:l,y:h,width:d-l,height:v-h}}},ii=function(e,t){var r=t.alignment,n=t.fontSize,i=t.font,a=t.bounds,o=us(an(e));(n===void 0||n===0)&&(n=Gu([o],i,a));var s=i.encodeText(o),f=i.widthOfTextAtSize(o,n),u=i.heightAtSize(n,{descender:!1}),c=r===be.Left?a.x:r===be.Center?a.x+a.width/2-f/2:r===be.Right?a.x+a.width-f:a.x,l=a.y+(a.height/2-u/2);return{fontSize:n,line:{text:o,encoded:s,width:f,height:u,x:c,y:l},bounds:{x:c,y:l,width:f,height:u}}},xr=function(e){return"normal"in e?e:{normal:e}},N0=/\/([^\0\t\n\f\r\ ]+)[\0\t\n\f\r\ ]+(\d*\.\d+|\d+)[\0\t\n\f\r\ ]+Tf/,zt=function(e){var t,r,n=(t=e.getDefaultAppearance())!==null&&t!==void 0?t:"",i=(r=ha(n,N0).match)!==null&&r!==void 0?r:[],a=Number(i[2]);return isFinite(a)?a:void 0},z0=/(\d*\.\d+|\d+)[\0\t\n\f\r\ ]*(\d*\.\d+|\d+)?[\0\t\n\f\r\ ]*(\d*\.\d+|\d+)?[\0\t\n\f\r\ ]*(\d*\.\d+|\d+)?[\0\t\n\f\r\ ]+(g|rg|k)/,Ye=function(e){var t,r=(t=e.getDefaultAppearance())!==null&&t!==void 0?t:"",n=ha(r,z0).match,i=n??[],a=i[1],o=i[2],s=i[3],f=i[4],u=i[5];if(u==="g"&&a)return Iu(Number(a));if(u==="rg"&&a&&o&&s)return ce(Number(a),Number(o),Number(s));if(u==="k"&&a&&o&&s&&f)return Uu(Number(a),Number(o),Number(s),Number(f))},Je=function(e,t,r,n){var i;n===void 0&&(n=0);var a=[_t(t).toString(),za((i=r==null?void 0:r.name)!==null&&i!==void 0?i:"dummy__noop",n).toString()].join(`
`);e.setDefaultAppearance(a)},j0=function(e,t){var r,n,i,a=Ye(t),o=Ye(e.acroField),s=t.getRectangle(),f=t.getAppearanceCharacteristics(),u=t.getBorderStyle(),c=(r=u==null?void 0:u.getWidth())!==null&&r!==void 0?r:0,l=mt(f==null?void 0:f.getRotation()),h=Jt(s,l),d=h.width,v=h.height,g=Mt(H(H({},s),{rotation:l})),m=ce(0,0,0),b=(n=Re(f==null?void 0:f.getBorderColor()))!==null&&n!==void 0?n:m,F=Re(f==null?void 0:f.getBackgroundColor()),w=Re(f==null?void 0:f.getBackgroundColor(),.8),C=(i=a??o)!==null&&i!==void 0?i:m;Je(a?t:e.acroField,C);var S={x:0+c/2,y:0+c/2,width:d-c,height:v-c,thickness:1.5,borderWidth:c,borderColor:b,markColor:C};return{normal:{on:re(g,En(H(H({},S),{color:F,filled:!0}))),off:re(g,En(H(H({},S),{color:F,filled:!1})))},down:{on:re(g,En(H(H({},S),{color:w,filled:!0}))),off:re(g,En(H(H({},S),{color:w,filled:!1})))}}},M0=function(e,t){var r,n,i,a=Ye(t),o=Ye(e.acroField),s=t.getRectangle(),f=t.getAppearanceCharacteristics(),u=t.getBorderStyle(),c=(r=u==null?void 0:u.getWidth())!==null&&r!==void 0?r:0,l=mt(f==null?void 0:f.getRotation()),h=Jt(s,l),d=h.width,v=h.height,g=Mt(H(H({},s),{rotation:l})),m=ce(0,0,0),b=(n=Re(f==null?void 0:f.getBorderColor()))!==null&&n!==void 0?n:m,F=Re(f==null?void 0:f.getBackgroundColor()),w=Re(f==null?void 0:f.getBackgroundColor(),.8),C=(i=a??o)!==null&&i!==void 0?i:m;Je(a?t:e.acroField,C);var S={x:d/2,y:v/2,width:d-c,height:v-c,borderWidth:c,borderColor:b,dotColor:C};return{normal:{on:re(g,Bn(H(H({},S),{color:F,filled:!0}))),off:re(g,Bn(H(H({},S),{color:F,filled:!1})))},down:{on:re(g,Bn(H(H({},S),{color:w,filled:!0}))),off:re(g,Bn(H(H({},S),{color:w,filled:!1})))}}},I0=function(e,t,r){var n,i,a,o,s,f=Ye(t),u=Ye(e.acroField),c=zt(t),l=zt(e.acroField),h=t.getRectangle(),d=t.getAppearanceCharacteristics(),v=t.getBorderStyle(),g=d==null?void 0:d.getCaptions(),m=(n=g==null?void 0:g.normal)!==null&&n!==void 0?n:"",b=(a=(i=g==null?void 0:g.down)!==null&&i!==void 0?i:m)!==null&&a!==void 0?a:"",F=(o=v==null?void 0:v.getWidth())!==null&&o!==void 0?o:0,w=mt(d==null?void 0:d.getRotation()),C=Jt(h,w),S=C.width,k=C.height,T=Mt(H(H({},h),{rotation:w})),P=ce(0,0,0),D=Re(d==null?void 0:d.getBorderColor()),N=Re(d==null?void 0:d.getBackgroundColor()),A=Re(d==null?void 0:d.getBackgroundColor(),.8),z={x:F,y:F,width:S-F*2,height:k-F*2},B=ii(m,{alignment:be.Center,fontSize:c??l,font:r,bounds:z}),W=ii(b,{alignment:be.Center,fontSize:c??l,font:r,bounds:z}),L=Math.min(B.fontSize,W.fontSize),j=(s=f??u)!==null&&s!==void 0?s:P;Je(f||c!==void 0?t:e.acroField,j,r,L);var ie={x:0+F/2,y:0+F/2,width:S-F,height:k-F,borderWidth:F,borderColor:D,textColor:j,font:r.name,fontSize:L};return{normal:re(T,as(H(H({},ie),{color:N,textLines:[B.line]}))),down:re(T,as(H(H({},ie),{color:A,textLines:[W.line]})))}},U0=function(e,t,r){var n,i,a,o,s=Ye(t),f=Ye(e.acroField),u=zt(t),c=zt(e.acroField),l=t.getRectangle(),h=t.getAppearanceCharacteristics(),d=t.getBorderStyle(),v=(n=e.getText())!==null&&n!==void 0?n:"",g=(i=d==null?void 0:d.getWidth())!==null&&i!==void 0?i:0,m=mt(h==null?void 0:h.getRotation()),b=Jt(l,m),F=b.width,w=b.height,C=Mt(H(H({},l),{rotation:m})),S=ce(0,0,0),k=Re(h==null?void 0:h.getBorderColor()),T=Re(h==null?void 0:h.getBackgroundColor()),P,D,N=e.isCombed()?0:1,A={x:g+N,y:g+N,width:F-(g+N)*2,height:w-(g+N)*2};if(e.isMultiline()){var z=Hu(v,{alignment:e.getAlignment(),fontSize:u??c,font:r,bounds:A});P=z.lines,D=z.fontSize}else if(e.isCombed()){var z=B0(v,{fontSize:u??c,font:r,bounds:A,cellCount:(a=e.getMaxLength())!==null&&a!==void 0?a:0});P=z.cells,D=z.fontSize}else{var z=ii(v,{alignment:e.getAlignment(),fontSize:u??c,font:r,bounds:A});P=[z.line],D=z.fontSize}var B=(o=s??f)!==null&&o!==void 0?o:S;Je(s||u!==void 0?t:e.acroField,B,r,D);var W={x:0+g/2,y:0+g/2,width:F-g,height:w-g,borderWidth:g??0,borderColor:k,textColor:B,font:r.name,fontSize:D,color:T,textLines:P,padding:N};return re(C,Wu(W))},V0=function(e,t,r){var n,i,a,o=Ye(t),s=Ye(e.acroField),f=zt(t),u=zt(e.acroField),c=t.getRectangle(),l=t.getAppearanceCharacteristics(),h=t.getBorderStyle(),d=(n=e.getSelected()[0])!==null&&n!==void 0?n:"",v=(i=h==null?void 0:h.getWidth())!==null&&i!==void 0?i:0,g=mt(l==null?void 0:l.getRotation()),m=Jt(c,g),b=m.width,F=m.height,w=Mt(H(H({},c),{rotation:g})),C=ce(0,0,0),S=Re(l==null?void 0:l.getBorderColor()),k=Re(l==null?void 0:l.getBackgroundColor()),T=1,P={x:v+T,y:v+T,width:b-(v+T)*2,height:F-(v+T)*2},D=ii(d,{alignment:be.Left,fontSize:f??u,font:r,bounds:P}),N=D.line,A=D.fontSize,z=(a=o??s)!==null&&a!==void 0?a:C;Je(o||f!==void 0?t:e.acroField,z,r,A);var B={x:0+v/2,y:0+v/2,width:b-v,height:F-v,borderWidth:v??0,borderColor:S,textColor:z,font:r.name,fontSize:A,color:k,textLines:[N],padding:T};return re(w,Wu(B))},W0=function(e,t,r){var n,i,a=Ye(t),o=Ye(e.acroField),s=zt(t),f=zt(e.acroField),u=t.getRectangle(),c=t.getAppearanceCharacteristics(),l=t.getBorderStyle(),h=(n=l==null?void 0:l.getWidth())!==null&&n!==void 0?n:0,d=mt(c==null?void 0:c.getRotation()),v=Jt(u,d),g=v.width,m=v.height,b=Mt(H(H({},u),{rotation:d})),F=ce(0,0,0),w=Re(c==null?void 0:c.getBorderColor()),C=Re(c==null?void 0:c.getBackgroundColor()),S=e.getOptions(),k=e.getSelected();e.isSorted()&&S.sort();for(var T="",P=0,D=S.length;P<D;P++)T+=S[P],P<D-1&&(T+=`
`);for(var N=1,A={x:h+N,y:h+N,width:g-(h+N)*2,height:m-(h+N)*2},z=Hu(T,{alignment:be.Left,fontSize:s??f,font:r,bounds:A}),B=z.lines,W=z.fontSize,L=z.lineHeight,j=[],P=0,D=B.length;P<D;P++){var ie=B[P];k.includes(ie.text)&&j.push(P)}var Fe=ce(153/255,193/255,218/255),Oe=(i=a??o)!==null&&i!==void 0?i:F;return Je(a||s!==void 0?t:e.acroField,Oe,r,W),re(b,m0({x:0+h/2,y:0+h/2,width:g-h,height:m-h,borderWidth:h??0,borderColor:w,textColor:Oe,font:r.name,fontSize:W,color:C,textLines:B,lineHeight:L,selectedColor:Fe,selectedLines:j,padding:N}))},Xu=function(){function e(t,r,n){this.alreadyEmbedded=!1,x(t,"ref",[[fe,"PDFRef"]]),x(r,"doc",[[Xt,"PDFDocument"]]),x(n,"embedder",[[bu,"PDFPageEmbedder"]]),this.ref=t,this.doc=r,this.width=n.width,this.height=n.height,this.embedder=n}return e.prototype.scale=function(t){return x(t,"factor",["number"]),{width:this.width*t,height:this.height*t}},e.prototype.size=function(){return this.scale(1)},e.prototype.embed=function(){return Y(this,void 0,void 0,function(){return J(this,function(t){switch(t.label){case 0:return this.alreadyEmbedded?[3,2]:[4,this.embedder.embedIntoContext(this.doc.context,this.ref)];case 1:t.sent(),this.alreadyEmbedded=!0,t.label=2;case 2:return[2]}})})},e.of=function(t,r,n){return new e(t,r,n)},e}(),Ue=function(){function e(t,r,n){this.modified=!0,x(t,"ref",[[fe,"PDFRef"]]),x(r,"doc",[[Xt,"PDFDocument"]]),x(n,"embedder",[[Ta,"CustomFontEmbedder"],[Jn,"StandardFontEmbedder"]]),this.ref=t,this.doc=r,this.name=n.fontName,this.embedder=n}return e.prototype.encodeText=function(t){return x(t,"text",["string"]),this.modified=!0,this.embedder.encodeText(t)},e.prototype.widthOfTextAtSize=function(t,r){return x(t,"text",["string"]),x(r,"size",["number"]),this.embedder.widthOfTextAtSize(t,r)},e.prototype.heightAtSize=function(t,r){var n;return x(t,"size",["number"]),O(r==null?void 0:r.descender,"options.descender",["boolean"]),this.embedder.heightOfFontAtSize(t,{descender:(n=r==null?void 0:r.descender)!==null&&n!==void 0?n:!0})},e.prototype.sizeAtHeight=function(t){return x(t,"height",["number"]),this.embedder.sizeOfFontAtHeight(t)},e.prototype.getCharacterSet=function(){return this.embedder instanceof Jn?this.embedder.encoding.supportedCodePoints:this.embedder.font.characterSet},e.prototype.embed=function(){return Y(this,void 0,void 0,function(){return J(this,function(t){switch(t.label){case 0:return this.modified?[4,this.embedder.embedIntoContext(this.doc.context,this.ref)]:[3,2];case 1:t.sent(),this.modified=!1,t.label=2;case 2:return[2]}})})},e.of=function(t,r,n){return new e(t,r,n)},e}(),fa=function(){function e(t,r,n){x(t,"ref",[[fe,"PDFRef"]]),x(r,"doc",[[Xt,"PDFDocument"]]),x(n,"embedder",[[vu,"JpegEmbedder"],[pu,"PngEmbedder"]]),this.ref=t,this.doc=r,this.width=n.width,this.height=n.height,this.embedder=n}return e.prototype.scale=function(t){return x(t,"factor",["number"]),{width:this.width*t,height:this.height*t}},e.prototype.scaleToFit=function(t,r){x(t,"width",["number"]),x(r,"height",["number"]);var n=t/this.width,i=r/this.height,a=Math.min(n,i);return this.scale(a)},e.prototype.size=function(){return this.scale(1)},e.prototype.embed=function(){return Y(this,void 0,void 0,function(){var t,r,n;return J(this,function(i){switch(i.label){case 0:return this.embedder?(this.embedTask||(t=this,r=t.doc,n=t.ref,this.embedTask=this.embedder.embedIntoContext(r.context,n)),[4,this.embedTask]):[2];case 1:return i.sent(),this.embedder=void 0,[2]}})})},e.of=function(t,r,n){return new e(t,r,n)},e}(),Et;(function(e){e[e.Left=0]="Left",e[e.Center=1]="Center",e[e.Right=2]="Right"})(Et||(Et={}));var wr=function(e){O(e==null?void 0:e.x,"options.x",["number"]),O(e==null?void 0:e.y,"options.y",["number"]),O(e==null?void 0:e.width,"options.width",["number"]),O(e==null?void 0:e.height,"options.height",["number"]),O(e==null?void 0:e.textColor,"options.textColor",[[Object,"Color"]]),O(e==null?void 0:e.backgroundColor,"options.backgroundColor",[[Object,"Color"]]),O(e==null?void 0:e.borderColor,"options.borderColor",[[Object,"Color"]]),O(e==null?void 0:e.borderWidth,"options.borderWidth",["number"]),O(e==null?void 0:e.rotate,"options.rotate",[[Object,"Rotation"]])},$t=function(){function e(t,r,n){x(t,"acroField",[[br,"PDFAcroTerminal"]]),x(r,"ref",[[fe,"PDFRef"]]),x(n,"doc",[[Xt,"PDFDocument"]]),this.acroField=t,this.ref=r,this.doc=n}return e.prototype.getName=function(){var t;return(t=this.acroField.getFullyQualifiedName())!==null&&t!==void 0?t:""},e.prototype.isReadOnly=function(){return this.acroField.hasFlag($e.ReadOnly)},e.prototype.enableReadOnly=function(){this.acroField.setFlagTo($e.ReadOnly,!0)},e.prototype.disableReadOnly=function(){this.acroField.setFlagTo($e.ReadOnly,!1)},e.prototype.isRequired=function(){return this.acroField.hasFlag($e.Required)},e.prototype.enableRequired=function(){this.acroField.setFlagTo($e.Required,!0)},e.prototype.disableRequired=function(){this.acroField.setFlagTo($e.Required,!1)},e.prototype.isExported=function(){return!this.acroField.hasFlag($e.NoExport)},e.prototype.enableExporting=function(){this.acroField.setFlagTo($e.NoExport,!1)},e.prototype.disableExporting=function(){this.acroField.setFlagTo($e.NoExport,!0)},e.prototype.needsAppearancesUpdate=function(){throw new Le(this.constructor.name,"needsAppearancesUpdate")},e.prototype.defaultUpdateAppearances=function(t){throw new Le(this.constructor.name,"defaultUpdateAppearances")},e.prototype.markAsDirty=function(){this.doc.getForm().markFieldAsDirty(this.ref)},e.prototype.markAsClean=function(){this.doc.getForm().markFieldAsClean(this.ref)},e.prototype.isDirty=function(){return this.doc.getForm().fieldIsDirty(this.ref)},e.prototype.createWidget=function(t){var r,n=t.textColor,i=t.backgroundColor,a=t.borderColor,o=t.borderWidth,s=Du(t.rotate),f=t.caption,u=t.x,c=t.y,l=t.width+o,h=t.height+o,d=!!t.hidden,v=t.page;su(s,"degreesAngle",90);var g=oa.create(this.doc.context,this.ref),m=Qh({x:u,y:c,width:l,height:h},o,s);g.setRectangle(m),v&&g.setP(v);var b=g.getOrCreateAppearanceCharacteristics();i&&b.setBackgroundColor(ts(i)),b.setRotation(s),f&&b.setCaptions({normal:f}),a&&b.setBorderColor(ts(a));var F=g.getOrCreateBorderStyle();if(o!==void 0&&F.setWidth(o),g.setFlagTo(Hr.Print,!0),g.setFlagTo(Hr.Hidden,d),g.setFlagTo(Hr.Invisible,!1),n){var w=(r=this.acroField.getDefaultAppearance())!==null&&r!==void 0?r:"",C=w+`
`+_t(n).toString();this.acroField.setDefaultAppearance(C)}return g},e.prototype.updateWidgetAppearanceWithFont=function(t,r,n){var i=n.normal,a=n.rollover,o=n.down;this.updateWidgetAppearances(t,{normal:this.createAppearanceStream(t,i,r),rollover:a&&this.createAppearanceStream(t,a,r),down:o&&this.createAppearanceStream(t,o,r)})},e.prototype.updateOnOffWidgetAppearance=function(t,r,n){var i=n.normal,a=n.rollover,o=n.down;this.updateWidgetAppearances(t,{normal:this.createAppearanceDict(t,i,r),rollover:a&&this.createAppearanceDict(t,a,r),down:o&&this.createAppearanceDict(t,o,r)})},e.prototype.updateWidgetAppearances=function(t,r){var n=r.normal,i=r.rollover,a=r.down;t.setNormalAppearance(n),i?t.setRolloverAppearance(i):t.removeRolloverAppearance(),a?t.setDownAppearance(a):t.removeDownAppearance()},e.prototype.createAppearanceStream=function(t,r,n){var i,a=this.acroField.dict.context,o=t.getRectangle(),s=o.width,f=o.height,u=n&&{Font:(i={},i[n.name]=n.ref,i)},c=a.formXObject(r,{Resources:u,BBox:a.obj([0,0,s,f]),Matrix:a.obj([1,0,0,1,0,0])}),l=a.register(c);return l},e.prototype.createImageAppearanceStream=function(t,r,n){var i,a,o=this.acroField.dict.context,s=t.getRectangle(),f=t.getAppearanceCharacteristics(),u=t.getBorderStyle(),c=(a=u==null?void 0:u.getWidth())!==null&&a!==void 0?a:0,l=mt(f==null?void 0:f.getRotation()),h=Mt(H(H({},s),{rotation:l})),d=Jt(s,l),v=r.scaleToFit(d.width-c*2,d.height-c*2),g={x:c,y:c,width:v.width,height:v.height,rotate:V(0),xSkew:V(0),ySkew:V(0)};n===Et.Center?(g.x+=(d.width-c*2)/2-v.width/2,g.y+=(d.height-c*2)/2-v.height/2):n===Et.Right&&(g.x=d.width-c-v.width,g.y=d.height-c-v.height);var m=this.doc.context.addRandomSuffix("Image",10),b=re(h,Vu(m,g)),F={XObject:(i={},i[m]=r.ref,i)},w=o.formXObject(b,{Resources:F,BBox:o.obj([0,0,s.width,s.height]),Matrix:o.obj([1,0,0,1,0,0])});return o.register(w)},e.prototype.createAppearanceDict=function(t,r,n){var i=this.acroField.dict.context,a=this.createAppearanceStream(t,r.on),o=this.createAppearanceStream(t,r.off),s=i.obj({});return s.set(n,a),s.set(p.of("Off"),o),s},e}(),zr=function(e){E(t,e);function t(r,n,i){var a=e.call(this,r,n,i)||this;return x(r,"acroCheckBox",[[vi,"PDFAcroCheckBox"]]),a.acroField=r,a}return t.prototype.check=function(){var r,n=(r=this.acroField.getOnValue())!==null&&r!==void 0?r:p.of("Yes");this.markAsDirty(),this.acroField.setValue(n)},t.prototype.uncheck=function(){this.markAsDirty(),this.acroField.setValue(p.of("Off"))},t.prototype.isChecked=function(){var r=this.acroField.getOnValue();return!!r&&r===this.acroField.getValue()},t.prototype.addToPage=function(r,n){var i,a,o,s,f,u;x(r,"page",[[Ke,"PDFPage"]]),wr(n),n||(n={}),"textColor"in n||(n.textColor=ce(0,0,0)),"backgroundColor"in n||(n.backgroundColor=ce(1,1,1)),"borderColor"in n||(n.borderColor=ce(0,0,0)),"borderWidth"in n||(n.borderWidth=1);var c=this.createWidget({x:(i=n.x)!==null&&i!==void 0?i:0,y:(a=n.y)!==null&&a!==void 0?a:0,width:(o=n.width)!==null&&o!==void 0?o:50,height:(s=n.height)!==null&&s!==void 0?s:50,textColor:n.textColor,backgroundColor:n.backgroundColor,borderColor:n.borderColor,borderWidth:(f=n.borderWidth)!==null&&f!==void 0?f:0,rotate:(u=n.rotate)!==null&&u!==void 0?u:V(0),hidden:n.hidden,page:r.ref}),l=this.doc.context.register(c.dict);this.acroField.addWidget(l),c.setAppearanceState(p.of("Off")),this.updateWidgetAppearance(c,p.of("Yes")),r.node.addAnnot(l)},t.prototype.needsAppearancesUpdate=function(){for(var r,n=this.acroField.getWidgets(),i=0,a=n.length;i<a;i++){var o=n[i],s=o.getAppearanceState(),f=(r=o.getAppearances())===null||r===void 0?void 0:r.normal;if(!(f instanceof Z)||s&&!f.has(s))return!0}return!1},t.prototype.defaultUpdateAppearances=function(){this.updateAppearances()},t.prototype.updateAppearances=function(r){var n;O(r,"provider",[Function]);for(var i=this.acroField.getWidgets(),a=0,o=i.length;a<o;a++){var s=i[a],f=(n=s.getOnValue())!==null&&n!==void 0?n:p.of("Yes");f&&this.updateWidgetAppearance(s,f,r)}this.markAsClean()},t.prototype.updateWidgetAppearance=function(r,n,i){var a=i??j0,o=xr(a(this,r));this.updateOnOffWidgetAppearance(r,n,o)},t.of=function(r,n,i){return new t(r,n,i)},t}($t),Wn=function(e){E(t,e);function t(r,n,i){var a=e.call(this,r,n,i)||this;return x(r,"acroComboBox",[[pi,"PDFAcroComboBox"]]),a.acroField=r,a}return t.prototype.getOptions=function(){for(var r=this.acroField.getOptions(),n=new Array(r.length),i=0,a=n.length;i<a;i++){var o=r[i],s=o.display,f=o.value;n[i]=(s??f).decodeText()}return n},t.prototype.getSelected=function(){for(var r=this.acroField.getValues(),n=new Array(r.length),i=0,a=r.length;i<a;i++)n[i]=r[i].decodeText();return n},t.prototype.setOptions=function(r){x(r,"options",[Array]);for(var n=new Array(r.length),i=0,a=r.length;i<a;i++)n[i]={value:U.fromText(r[i])};this.acroField.setOptions(n)},t.prototype.addOptions=function(r){x(r,"options",["string",Array]);for(var n=Array.isArray(r)?r:[r],i=this.acroField.getOptions(),a=new Array(n.length),o=0,s=n.length;o<s;o++)a[o]={value:U.fromText(n[o])};this.acroField.setOptions(i.concat(a))},t.prototype.select=function(r,n){n===void 0&&(n=!1),x(r,"options",["string",Array]),x(n,"merge",["boolean"]);var i=Array.isArray(r)?r:[r],a=this.getOptions(),o=i.find(function(l){return!a.includes(l)});o&&this.enableEditing(),this.markAsDirty(),(i.length>1||i.length===1&&n)&&this.enableMultiselect();for(var s=new Array(i.length),f=0,u=i.length;f<u;f++)s[f]=U.fromText(i[f]);if(n){var c=this.acroField.getValues();this.acroField.setValues(c.concat(s))}else this.acroField.setValues(s)},t.prototype.clear=function(){this.markAsDirty(),this.acroField.setValues([])},t.prototype.setFontSize=function(r){hi(r,"fontSize"),this.acroField.setFontSize(r),this.markAsDirty()},t.prototype.isEditable=function(){return this.acroField.hasFlag(se.Edit)},t.prototype.enableEditing=function(){this.acroField.setFlagTo(se.Edit,!0)},t.prototype.disableEditing=function(){this.acroField.setFlagTo(se.Edit,!1)},t.prototype.isSorted=function(){return this.acroField.hasFlag(se.Sort)},t.prototype.enableSorting=function(){this.acroField.setFlagTo(se.Sort,!0)},t.prototype.disableSorting=function(){this.acroField.setFlagTo(se.Sort,!1)},t.prototype.isMultiselect=function(){return this.acroField.hasFlag(se.MultiSelect)},t.prototype.enableMultiselect=function(){this.acroField.setFlagTo(se.MultiSelect,!0)},t.prototype.disableMultiselect=function(){this.acroField.setFlagTo(se.MultiSelect,!1)},t.prototype.isSpellChecked=function(){return!this.acroField.hasFlag(se.DoNotSpellCheck)},t.prototype.enableSpellChecking=function(){this.acroField.setFlagTo(se.DoNotSpellCheck,!1)},t.prototype.disableSpellChecking=function(){this.acroField.setFlagTo(se.DoNotSpellCheck,!0)},t.prototype.isSelectOnClick=function(){return this.acroField.hasFlag(se.CommitOnSelChange)},t.prototype.enableSelectOnClick=function(){this.acroField.setFlagTo(se.CommitOnSelChange,!0)},t.prototype.disableSelectOnClick=function(){this.acroField.setFlagTo(se.CommitOnSelChange,!1)},t.prototype.addToPage=function(r,n){var i,a,o,s,f,u,c;x(r,"page",[[Ke,"PDFPage"]]),wr(n),n||(n={}),"textColor"in n||(n.textColor=ce(0,0,0)),"backgroundColor"in n||(n.backgroundColor=ce(1,1,1)),"borderColor"in n||(n.borderColor=ce(0,0,0)),"borderWidth"in n||(n.borderWidth=1);var l=this.createWidget({x:(i=n.x)!==null&&i!==void 0?i:0,y:(a=n.y)!==null&&a!==void 0?a:0,width:(o=n.width)!==null&&o!==void 0?o:200,height:(s=n.height)!==null&&s!==void 0?s:50,textColor:n.textColor,backgroundColor:n.backgroundColor,borderColor:n.borderColor,borderWidth:(f=n.borderWidth)!==null&&f!==void 0?f:0,rotate:(u=n.rotate)!==null&&u!==void 0?u:V(0),hidden:n.hidden,page:r.ref}),h=this.doc.context.register(l.dict);this.acroField.addWidget(h);var d=(c=n.font)!==null&&c!==void 0?c:this.doc.getForm().getDefaultFont();this.updateWidgetAppearance(l,d),r.node.addAnnot(h)},t.prototype.needsAppearancesUpdate=function(){var r;if(this.isDirty())return!0;for(var n=this.acroField.getWidgets(),i=0,a=n.length;i<a;i++){var o=n[i],s=((r=o.getAppearances())===null||r===void 0?void 0:r.normal)instanceof Ie;if(!s)return!0}return!1},t.prototype.defaultUpdateAppearances=function(r){x(r,"font",[[Ue,"PDFFont"]]),this.updateAppearances(r)},t.prototype.updateAppearances=function(r,n){x(r,"font",[[Ue,"PDFFont"]]),O(n,"provider",[Function]);for(var i=this.acroField.getWidgets(),a=0,o=i.length;a<o;a++){var s=i[a];this.updateWidgetAppearance(s,r,n)}this.markAsClean()},t.prototype.updateWidgetAppearance=function(r,n,i){var a=i??V0,o=xr(a(this,r,n));this.updateWidgetAppearanceWithFont(r,n,o)},t.of=function(r,n,i){return new t(r,n,i)},t}($t),qn=function(e){E(t,e);function t(r,n,i){var a=e.call(this,r,n,i)||this;return x(r,"acroListBox",[[mi,"PDFAcroListBox"]]),a.acroField=r,a}return t.prototype.getOptions=function(){for(var r=this.acroField.getOptions(),n=new Array(r.length),i=0,a=n.length;i<a;i++){var o=r[i],s=o.display,f=o.value;n[i]=(s??f).decodeText()}return n},t.prototype.getSelected=function(){for(var r=this.acroField.getValues(),n=new Array(r.length),i=0,a=r.length;i<a;i++)n[i]=r[i].decodeText();return n},t.prototype.setOptions=function(r){x(r,"options",[Array]),this.markAsDirty();for(var n=new Array(r.length),i=0,a=r.length;i<a;i++)n[i]={value:U.fromText(r[i])};this.acroField.setOptions(n)},t.prototype.addOptions=function(r){x(r,"options",["string",Array]),this.markAsDirty();for(var n=Array.isArray(r)?r:[r],i=this.acroField.getOptions(),a=new Array(n.length),o=0,s=n.length;o<s;o++)a[o]={value:U.fromText(n[o])};this.acroField.setOptions(i.concat(a))},t.prototype.select=function(r,n){n===void 0&&(n=!1),x(r,"options",["string",Array]),x(n,"merge",["boolean"]);var i=Array.isArray(r)?r:[r],a=this.getOptions();Ul(i,"option",a),this.markAsDirty(),(i.length>1||i.length===1&&n)&&this.enableMultiselect();for(var o=new Array(i.length),s=0,f=i.length;s<f;s++)o[s]=U.fromText(i[s]);if(n){var u=this.acroField.getValues();this.acroField.setValues(u.concat(o))}else this.acroField.setValues(o)},t.prototype.clear=function(){this.markAsDirty(),this.acroField.setValues([])},t.prototype.setFontSize=function(r){hi(r,"fontSize"),this.acroField.setFontSize(r),this.markAsDirty()},t.prototype.isSorted=function(){return this.acroField.hasFlag(se.Sort)},t.prototype.enableSorting=function(){this.acroField.setFlagTo(se.Sort,!0)},t.prototype.disableSorting=function(){this.acroField.setFlagTo(se.Sort,!1)},t.prototype.isMultiselect=function(){return this.acroField.hasFlag(se.MultiSelect)},t.prototype.enableMultiselect=function(){this.acroField.setFlagTo(se.MultiSelect,!0)},t.prototype.disableMultiselect=function(){this.acroField.setFlagTo(se.MultiSelect,!1)},t.prototype.isSelectOnClick=function(){return this.acroField.hasFlag(se.CommitOnSelChange)},t.prototype.enableSelectOnClick=function(){this.acroField.setFlagTo(se.CommitOnSelChange,!0)},t.prototype.disableSelectOnClick=function(){this.acroField.setFlagTo(se.CommitOnSelChange,!1)},t.prototype.addToPage=function(r,n){var i,a,o,s,f,u,c;x(r,"page",[[Ke,"PDFPage"]]),wr(n),n||(n={}),"textColor"in n||(n.textColor=ce(0,0,0)),"backgroundColor"in n||(n.backgroundColor=ce(1,1,1)),"borderColor"in n||(n.borderColor=ce(0,0,0)),"borderWidth"in n||(n.borderWidth=1);var l=this.createWidget({x:(i=n.x)!==null&&i!==void 0?i:0,y:(a=n.y)!==null&&a!==void 0?a:0,width:(o=n.width)!==null&&o!==void 0?o:200,height:(s=n.height)!==null&&s!==void 0?s:100,textColor:n.textColor,backgroundColor:n.backgroundColor,borderColor:n.borderColor,borderWidth:(f=n.borderWidth)!==null&&f!==void 0?f:0,rotate:(u=n.rotate)!==null&&u!==void 0?u:V(0),hidden:n.hidden,page:r.ref}),h=this.doc.context.register(l.dict);this.acroField.addWidget(h);var d=(c=n.font)!==null&&c!==void 0?c:this.doc.getForm().getDefaultFont();this.updateWidgetAppearance(l,d),r.node.addAnnot(h)},t.prototype.needsAppearancesUpdate=function(){var r;if(this.isDirty())return!0;for(var n=this.acroField.getWidgets(),i=0,a=n.length;i<a;i++){var o=n[i],s=((r=o.getAppearances())===null||r===void 0?void 0:r.normal)instanceof Ie;if(!s)return!0}return!1},t.prototype.defaultUpdateAppearances=function(r){x(r,"font",[[Ue,"PDFFont"]]),this.updateAppearances(r)},t.prototype.updateAppearances=function(r,n){x(r,"font",[[Ue,"PDFFont"]]),O(n,"provider",[Function]);for(var i=this.acroField.getWidgets(),a=0,o=i.length;a<o;a++){var s=i[a];this.updateWidgetAppearance(s,r,n)}this.markAsClean()},t.prototype.updateWidgetAppearance=function(r,n,i){var a=i??W0,o=xr(a(this,r,n));this.updateWidgetAppearanceWithFont(r,n,o)},t.of=function(r,n,i){return new t(r,n,i)},t}($t),jr=function(e){E(t,e);function t(r,n,i){var a=e.call(this,r,n,i)||this;return x(r,"acroRadioButton",[[bi,"PDFAcroRadioButton"]]),a.acroField=r,a}return t.prototype.getOptions=function(){var r=this.acroField.getExportValues();if(r){for(var n=new Array(r.length),i=0,a=r.length;i<a;i++)n[i]=r[i].decodeText();return n}for(var o=this.acroField.getOnValues(),s=new Array(o.length),i=0,a=s.length;i<a;i++)s[i]=o[i].decodeText();return s},t.prototype.getSelected=function(){var r=this.acroField.getValue();if(r!==p.of("Off")){var n=this.acroField.getExportValues();if(n){for(var i=this.acroField.getOnValues(),a=0,o=i.length;a<o;a++)if(i[a]===r)return n[a].decodeText()}return r.decodeText()}},t.prototype.select=function(r){x(r,"option",["string"]);var n=this.getOptions();Dt(r,"option",n),this.markAsDirty();var i=this.acroField.getOnValues(),a=this.acroField.getExportValues();if(a)for(var o=0,s=a.length;o<s;o++)a[o].decodeText()===r&&this.acroField.setValue(i[o]);else for(var o=0,s=i.length;o<s;o++){var f=i[o];f.decodeText()===r&&this.acroField.setValue(f)}},t.prototype.clear=function(){this.markAsDirty(),this.acroField.setValue(p.of("Off"))},t.prototype.isOffToggleable=function(){return!this.acroField.hasFlag(qe.NoToggleToOff)},t.prototype.enableOffToggling=function(){this.acroField.setFlagTo(qe.NoToggleToOff,!1)},t.prototype.disableOffToggling=function(){this.acroField.setFlagTo(qe.NoToggleToOff,!0)},t.prototype.isMutuallyExclusive=function(){return!this.acroField.hasFlag(qe.RadiosInUnison)},t.prototype.enableMutualExclusion=function(){this.acroField.setFlagTo(qe.RadiosInUnison,!1)},t.prototype.disableMutualExclusion=function(){this.acroField.setFlagTo(qe.RadiosInUnison,!0)},t.prototype.addOptionToPage=function(r,n,i){var a,o,s,f,u,c,l,h,d;x(r,"option",["string"]),x(n,"page",[[Ke,"PDFPage"]]),wr(i);var v=this.createWidget({x:(a=i==null?void 0:i.x)!==null&&a!==void 0?a:0,y:(o=i==null?void 0:i.y)!==null&&o!==void 0?o:0,width:(s=i==null?void 0:i.width)!==null&&s!==void 0?s:50,height:(f=i==null?void 0:i.height)!==null&&f!==void 0?f:50,textColor:(u=i==null?void 0:i.textColor)!==null&&u!==void 0?u:ce(0,0,0),backgroundColor:(c=i==null?void 0:i.backgroundColor)!==null&&c!==void 0?c:ce(1,1,1),borderColor:(l=i==null?void 0:i.borderColor)!==null&&l!==void 0?l:ce(0,0,0),borderWidth:(h=i==null?void 0:i.borderWidth)!==null&&h!==void 0?h:1,rotate:(d=i==null?void 0:i.rotate)!==null&&d!==void 0?d:V(0),hidden:i==null?void 0:i.hidden,page:n.ref}),g=this.doc.context.register(v.dict),m=this.acroField.addWidgetWithOpt(g,U.fromText(r),!this.isMutuallyExclusive());v.setAppearanceState(p.of("Off")),this.updateWidgetAppearance(v,m),n.node.addAnnot(g)},t.prototype.needsAppearancesUpdate=function(){for(var r,n=this.acroField.getWidgets(),i=0,a=n.length;i<a;i++){var o=n[i],s=o.getAppearanceState(),f=(r=o.getAppearances())===null||r===void 0?void 0:r.normal;if(!(f instanceof Z)||s&&!f.has(s))return!0}return!1},t.prototype.defaultUpdateAppearances=function(){this.updateAppearances()},t.prototype.updateAppearances=function(r){O(r,"provider",[Function]);for(var n=this.acroField.getWidgets(),i=0,a=n.length;i<a;i++){var o=n[i],s=o.getOnValue();s&&this.updateWidgetAppearance(o,s,r)}},t.prototype.updateWidgetAppearance=function(r,n,i){var a=i??M0,o=xr(a(this,r));this.updateOnOffWidgetAppearance(r,n,o)},t.of=function(r,n,i){return new t(r,n,i)},t}($t),ca=function(e){E(t,e);function t(r,n,i){var a=e.call(this,r,n,i)||this;return x(r,"acroSignature",[[Aa,"PDFAcroSignature"]]),a.acroField=r,a}return t.prototype.needsAppearancesUpdate=function(){return!1},t.of=function(r,n,i){return new t(r,n,i)},t}($t),Kn=function(e){E(t,e);function t(r,n,i){var a=e.call(this,r,n,i)||this;return x(r,"acroText",[[gi,"PDFAcroText"]]),a.acroField=r,a}return t.prototype.getText=function(){var r=this.acroField.getValue();if(!r&&this.isRichFormatted())throw new T0(this.getName());return r==null?void 0:r.decodeText()},t.prototype.setText=function(r){O(r,"text",["string"]);var n=this.getMaxLength();if(n!==void 0&&r&&r.length>n)throw new A0(r.length,n,this.getName());this.markAsDirty(),this.disableRichFormatting(),r?this.acroField.setValue(U.fromText(r)):this.acroField.removeValue()},t.prototype.getAlignment=function(){var r=this.acroField.getQuadding();return r===0?be.Left:r===1?be.Center:r===2?be.Right:be.Left},t.prototype.setAlignment=function(r){Dt(r,"alignment",be),this.markAsDirty(),this.acroField.setQuadding(r)},t.prototype.getMaxLength=function(){return this.acroField.getMaxLength()},t.prototype.setMaxLength=function(r){if(_e(r,"maxLength",0,Number.MAX_SAFE_INTEGER),this.markAsDirty(),r===void 0)this.acroField.removeMaxLength();else{var n=this.getText();if(n&&n.length>r)throw new D0(n.length,r,this.getName());this.acroField.setMaxLength(r)}},t.prototype.removeMaxLength=function(){this.markAsDirty(),this.acroField.removeMaxLength()},t.prototype.setImage=function(r){for(var n=this.getAlignment(),i=n===be.Center?Et.Center:n===be.Right?Et.Right:Et.Left,a=this.acroField.getWidgets(),o=0,s=a.length;o<s;o++){var f=a[o],u=this.createImageAppearanceStream(f,r,i);this.updateWidgetAppearances(f,{normal:u})}this.markAsClean()},t.prototype.setFontSize=function(r){hi(r,"fontSize"),this.acroField.setFontSize(r),this.markAsDirty()},t.prototype.isMultiline=function(){return this.acroField.hasFlag(de.Multiline)},t.prototype.enableMultiline=function(){this.markAsDirty(),this.acroField.setFlagTo(de.Multiline,!0)},t.prototype.disableMultiline=function(){this.markAsDirty(),this.acroField.setFlagTo(de.Multiline,!1)},t.prototype.isPassword=function(){return this.acroField.hasFlag(de.Password)},t.prototype.enablePassword=function(){this.acroField.setFlagTo(de.Password,!0)},t.prototype.disablePassword=function(){this.acroField.setFlagTo(de.Password,!1)},t.prototype.isFileSelector=function(){return this.acroField.hasFlag(de.FileSelect)},t.prototype.enableFileSelection=function(){this.acroField.setFlagTo(de.FileSelect,!0)},t.prototype.disableFileSelection=function(){this.acroField.setFlagTo(de.FileSelect,!1)},t.prototype.isSpellChecked=function(){return!this.acroField.hasFlag(de.DoNotSpellCheck)},t.prototype.enableSpellChecking=function(){this.acroField.setFlagTo(de.DoNotSpellCheck,!1)},t.prototype.disableSpellChecking=function(){this.acroField.setFlagTo(de.DoNotSpellCheck,!0)},t.prototype.isScrollable=function(){return!this.acroField.hasFlag(de.DoNotScroll)},t.prototype.enableScrolling=function(){this.acroField.setFlagTo(de.DoNotScroll,!1)},t.prototype.disableScrolling=function(){this.acroField.setFlagTo(de.DoNotScroll,!0)},t.prototype.isCombed=function(){return this.acroField.hasFlag(de.Comb)&&!this.isMultiline()&&!this.isPassword()&&!this.isFileSelector()&&this.getMaxLength()!==void 0},t.prototype.enableCombing=function(){if(this.getMaxLength()===void 0){var r="PDFTextFields must have a max length in order to be combed";console.warn(r)}this.markAsDirty(),this.disableMultiline(),this.disablePassword(),this.disableFileSelection(),this.acroField.setFlagTo(de.Comb,!0)},t.prototype.disableCombing=function(){this.markAsDirty(),this.acroField.setFlagTo(de.Comb,!1)},t.prototype.isRichFormatted=function(){return this.acroField.hasFlag(de.RichText)},t.prototype.enableRichFormatting=function(){this.acroField.setFlagTo(de.RichText,!0)},t.prototype.disableRichFormatting=function(){this.acroField.setFlagTo(de.RichText,!1)},t.prototype.addToPage=function(r,n){var i,a,o,s,f,u,c;x(r,"page",[[Ke,"PDFPage"]]),wr(n),n||(n={}),"textColor"in n||(n.textColor=ce(0,0,0)),"backgroundColor"in n||(n.backgroundColor=ce(1,1,1)),"borderColor"in n||(n.borderColor=ce(0,0,0)),"borderWidth"in n||(n.borderWidth=1);var l=this.createWidget({x:(i=n.x)!==null&&i!==void 0?i:0,y:(a=n.y)!==null&&a!==void 0?a:0,width:(o=n.width)!==null&&o!==void 0?o:200,height:(s=n.height)!==null&&s!==void 0?s:50,textColor:n.textColor,backgroundColor:n.backgroundColor,borderColor:n.borderColor,borderWidth:(f=n.borderWidth)!==null&&f!==void 0?f:0,rotate:(u=n.rotate)!==null&&u!==void 0?u:V(0),hidden:n.hidden,page:r.ref}),h=this.doc.context.register(l.dict);this.acroField.addWidget(h);var d=(c=n.font)!==null&&c!==void 0?c:this.doc.getForm().getDefaultFont();this.updateWidgetAppearance(l,d),r.node.addAnnot(h)},t.prototype.needsAppearancesUpdate=function(){var r;if(this.isDirty())return!0;for(var n=this.acroField.getWidgets(),i=0,a=n.length;i<a;i++){var o=n[i],s=((r=o.getAppearances())===null||r===void 0?void 0:r.normal)instanceof Ie;if(!s)return!0}return!1},t.prototype.defaultUpdateAppearances=function(r){x(r,"font",[[Ue,"PDFFont"]]),this.updateAppearances(r)},t.prototype.updateAppearances=function(r,n){x(r,"font",[[Ue,"PDFFont"]]),O(n,"provider",[Function]);for(var i=this.acroField.getWidgets(),a=0,o=i.length;a<o;a++){var s=i[a];this.updateWidgetAppearance(s,r,n)}this.markAsClean()},t.prototype.updateWidgetAppearance=function(r,n,i){var a=i??U0,o=xr(a(this,r,n));this.updateWidgetAppearanceWithFont(r,n,o)},t.of=function(r,n,i){return new t(r,n,i)},t}($t),ai;(function(e){e.Courier="Courier",e.CourierBold="Courier-Bold",e.CourierOblique="Courier-Oblique",e.CourierBoldOblique="Courier-BoldOblique",e.Helvetica="Helvetica",e.HelveticaBold="Helvetica-Bold",e.HelveticaOblique="Helvetica-Oblique",e.HelveticaBoldOblique="Helvetica-BoldOblique",e.TimesRoman="Times-Roman",e.TimesRomanBold="Times-Bold",e.TimesRomanItalic="Times-Italic",e.TimesRomanBoldItalic="Times-BoldItalic",e.Symbol="Symbol",e.ZapfDingbats="ZapfDingbats"})(ai||(ai={}));var q0=function(){function e(t,r){var n=this;this.embedDefaultFont=function(){return n.doc.embedStandardFont(ai.Helvetica)},x(t,"acroForm",[[$n,"PDFAcroForm"]]),x(r,"doc",[[Xt,"PDFDocument"]]),this.acroForm=t,this.doc=r,this.dirtyFields=new Set,this.defaultFontCache=pt.populatedBy(this.embedDefaultFont)}return e.prototype.hasXFA=function(){return this.acroForm.dict.has(p.of("XFA"))},e.prototype.deleteXFA=function(){this.acroForm.dict.delete(p.of("XFA"))},e.prototype.getFields=function(){for(var t=this.acroForm.getAllFields(),r=[],n=0,i=t.length;n<i;n++){var a=t[n],o=a[0],s=a[1],f=K0(o,s,this.doc);f&&r.push(f)}return r},e.prototype.getFieldMaybe=function(t){x(t,"name",["string"]);for(var r=this.getFields(),n=0,i=r.length;n<i;n++){var a=r[n];if(a.getName()===t)return a}},e.prototype.getField=function(t){x(t,"name",["string"]);var r=this.getFieldMaybe(t);if(r)return r;throw new k0(t)},e.prototype.getButton=function(t){x(t,"name",["string"]);var r=this.getField(t);if(r instanceof Ln)return r;throw new It(t,Ln,r)},e.prototype.getCheckBox=function(t){x(t,"name",["string"]);var r=this.getField(t);if(r instanceof zr)return r;throw new It(t,zr,r)},e.prototype.getDropdown=function(t){x(t,"name",["string"]);var r=this.getField(t);if(r instanceof Wn)return r;throw new It(t,Wn,r)},e.prototype.getOptionList=function(t){x(t,"name",["string"]);var r=this.getField(t);if(r instanceof qn)return r;throw new It(t,qn,r)},e.prototype.getRadioGroup=function(t){x(t,"name",["string"]);var r=this.getField(t);if(r instanceof jr)return r;throw new It(t,jr,r)},e.prototype.getSignature=function(t){x(t,"name",["string"]);var r=this.getField(t);if(r instanceof ca)return r;throw new It(t,ca,r)},e.prototype.getTextField=function(t){x(t,"name",["string"]);var r=this.getField(t);if(r instanceof Kn)return r;throw new It(t,Kn,r)},e.prototype.createButton=function(t){x(t,"name",["string"]);var r=ir(t),n=this.findOrCreateNonTerminals(r.nonTerminal),i=yi.create(this.doc.context);return i.setPartialName(r.terminal),ar(n,[i,i.ref],r.terminal),Ln.of(i,i.ref,this.doc)},e.prototype.createCheckBox=function(t){x(t,"name",["string"]);var r=ir(t),n=this.findOrCreateNonTerminals(r.nonTerminal),i=vi.create(this.doc.context);return i.setPartialName(r.terminal),ar(n,[i,i.ref],r.terminal),zr.of(i,i.ref,this.doc)},e.prototype.createDropdown=function(t){x(t,"name",["string"]);var r=ir(t),n=this.findOrCreateNonTerminals(r.nonTerminal),i=pi.create(this.doc.context);return i.setPartialName(r.terminal),ar(n,[i,i.ref],r.terminal),Wn.of(i,i.ref,this.doc)},e.prototype.createOptionList=function(t){x(t,"name",["string"]);var r=ir(t),n=this.findOrCreateNonTerminals(r.nonTerminal),i=mi.create(this.doc.context);return i.setPartialName(r.terminal),ar(n,[i,i.ref],r.terminal),qn.of(i,i.ref,this.doc)},e.prototype.createRadioGroup=function(t){x(t,"name",["string"]);var r=ir(t),n=this.findOrCreateNonTerminals(r.nonTerminal),i=bi.create(this.doc.context);return i.setPartialName(r.terminal),ar(n,[i,i.ref],r.terminal),jr.of(i,i.ref,this.doc)},e.prototype.createTextField=function(t){x(t,"name",["string"]);var r=ir(t),n=this.findOrCreateNonTerminals(r.nonTerminal),i=gi.create(this.doc.context);return i.setPartialName(r.terminal),ar(n,[i,i.ref],r.terminal),Kn.of(i,i.ref,this.doc)},e.prototype.flatten=function(t){t===void 0&&(t={updateFieldAppearances:!0}),t.updateFieldAppearances&&this.updateFieldAppearances();for(var r=this.getFields(),n=0,i=r.length;n<i;n++){for(var a=r[n],o=a.acroField.getWidgets(),s=0,f=o.length;s<f;s++){var u=o[s],c=this.findWidgetPage(u),l=this.findWidgetAppearanceRef(a,u),h=c.node.newXObject("FlatWidget",l),d=u.getRectangle(),v=re([xe(),Ge(d.x,d.y)],Mt(H(H({},d),{rotation:0})),[ja(h),we()]).filter(Boolean);c.pushOperators.apply(c,v)}this.removeField(a)}},e.prototype.removeField=function(t){for(var r=t.acroField.getWidgets(),n=new Set,i=0,a=r.length;i<a;i++){var o=r[i],s=this.findWidgetAppearanceRef(t,o),f=this.findWidgetPage(o);n.add(f),f.node.removeAnnot(s)}n.forEach(function(d){return d.node.removeAnnot(t.ref)}),this.acroForm.removeField(t.acroField);for(var u=t.acroField.normalizedEntries().Kids,c=u.size(),l=0;l<c;l++){var h=u.get(l);h instanceof fe&&this.doc.context.delete(h)}this.doc.context.delete(t.ref)},e.prototype.updateFieldAppearances=function(t){O(t,"font",[[Ue,"PDFFont"]]),t=t??this.getDefaultFont();for(var r=this.getFields(),n=0,i=r.length;n<i;n++){var a=r[n];a.needsAppearancesUpdate()&&a.defaultUpdateAppearances(t)}},e.prototype.markFieldAsDirty=function(t){O(t,"fieldRef",[[fe,"PDFRef"]]),this.dirtyFields.add(t)},e.prototype.markFieldAsClean=function(t){O(t,"fieldRef",[[fe,"PDFRef"]]),this.dirtyFields.delete(t)},e.prototype.fieldIsDirty=function(t){return O(t,"fieldRef",[[fe,"PDFRef"]]),this.dirtyFields.has(t)},e.prototype.getDefaultFont=function(){return this.defaultFontCache.access()},e.prototype.findWidgetPage=function(t){var r=t.P(),n=this.doc.getPages().find(function(a){return a.ref===r});if(n===void 0){var i=this.doc.context.getObjectRef(t.dict);if(i===void 0)throw new Error("Could not find PDFRef for PDFObject");if(n=this.doc.findPageForAnnotationRef(i),n===void 0)throw new Error("Could not find page for PDFRef "+i)}return n},e.prototype.findWidgetAppearanceRef=function(t,r){var n,i=r.getNormalAppearance();if(i instanceof Z&&(t instanceof zr||t instanceof jr)){var a=t.acroField.getValue(),o=(n=i.get(a))!==null&&n!==void 0?n:i.get(p.of("Off"));o instanceof fe&&(i=o)}if(!(i instanceof fe)){var s=t.getName();throw new Error("Failed to extract appearance ref for: "+s)}return i},e.prototype.findOrCreateNonTerminals=function(t){for(var r=[this.acroForm],n=0,i=t.length;n<i;n++){var a=t[n];if(!a)throw new C0(a);var o=r[0],s=r[1],f=this.findNonTerminal(a,o);if(f)r=f;else{var u=_n.create(this.doc.context);u.setPartialName(a),u.setParent(s);var c=this.doc.context.register(u.dict);o.addField(c),r=[u,c]}}return r},e.prototype.findNonTerminal=function(t,r){for(var n=r instanceof $n?this.acroForm.getFields():Da(r.Kids()),i=0,a=n.length;i<a;i++){var o=n[i],s=o[0],f=o[1];if(s.getPartialName()===t){if(s instanceof _n)return[s,f];throw new qu(t)}}},e.of=function(t,r){return new e(t,r)},e}(),K0=function(e,t,r){if(e instanceof yi)return Ln.of(e,t,r);if(e instanceof vi)return zr.of(e,t,r);if(e instanceof pi)return Wn.of(e,t,r);if(e instanceof mi)return qn.of(e,t,r);if(e instanceof gi)return Kn.of(e,t,r);if(e instanceof bi)return jr.of(e,t,r);if(e instanceof Aa)return ca.of(e,t,r)},ir=function(e){if(e.length===0)throw new Error("PDF field names must not be empty strings");for(var t=e.split("."),r=0,n=t.length;r<n;r++)if(t[r]==="")throw new Error('Periods in PDF field names must be separated by at least one character: "'+e+'"');return t.length===1?{nonTerminal:[],terminal:t[0]}:{nonTerminal:t.slice(0,t.length-1),terminal:t[t.length-1]}},ar=function(e,t,r){for(var n=e[0],i=e[1],a=t[0],o=t[1],s=n.normalizedEntries(),f=Da("Kids"in s?s.Kids:s.Fields),u=0,c=f.length;u<c;u++)if(f[u][0].getPartialName()===r)throw new qu(r);n.addField(o),a.setParent(i)},L0={"4A0":[4767.87,6740.79],"2A0":[3370.39,4767.87],A0:[2383.94,3370.39],A1:[1683.78,2383.94],A2:[1190.55,1683.78],A3:[841.89,1190.55],A4:[595.28,841.89],A5:[419.53,595.28],A6:[297.64,419.53],A7:[209.76,297.64],A8:[147.4,209.76],A9:[104.88,147.4],A10:[73.7,104.88],B0:[2834.65,4008.19],B1:[2004.09,2834.65],B2:[1417.32,2004.09],B3:[1000.63,1417.32],B4:[708.66,1000.63],B5:[498.9,708.66],B6:[354.33,498.9],B7:[249.45,354.33],B8:[175.75,249.45],B9:[124.72,175.75],B10:[87.87,124.72],C0:[2599.37,3676.54],C1:[1836.85,2599.37],C2:[1298.27,1836.85],C3:[918.43,1298.27],C4:[649.13,918.43],C5:[459.21,649.13],C6:[323.15,459.21],C7:[229.61,323.15],C8:[161.57,229.61],C9:[113.39,161.57],C10:[79.37,113.39],RA0:[2437.8,3458.27],RA1:[1729.13,2437.8],RA2:[1218.9,1729.13],RA3:[864.57,1218.9],RA4:[609.45,864.57],SRA0:[2551.18,3628.35],SRA1:[1814.17,2551.18],SRA2:[1275.59,1814.17],SRA3:[907.09,1275.59],SRA4:[637.8,907.09],Executive:[521.86,756],Folio:[612,936],Legal:[612,1008],Letter:[612,792],Tabloid:[792,1224]},la;(function(e){e[e.Fastest=1/0]="Fastest",e[e.Fast=1500]="Fast",e[e.Medium=500]="Medium",e[e.Slow=100]="Slow"})(la||(la={}));var G0=function(){function e(t,r,n){this.alreadyEmbedded=!1,this.ref=t,this.doc=r,this.embedder=n}return e.prototype.embed=function(){return Y(this,void 0,void 0,function(){var t,r,n,i,a;return J(this,function(o){switch(o.label){case 0:return this.alreadyEmbedded?[3,2]:[4,this.embedder.embedIntoContext(this.doc.context,this.ref)];case 1:t=o.sent(),this.doc.catalog.has(p.of("Names"))||this.doc.catalog.set(p.of("Names"),this.doc.context.obj({})),r=this.doc.catalog.lookup(p.of("Names"),Z),r.has(p.of("EmbeddedFiles"))||r.set(p.of("EmbeddedFiles"),this.doc.context.obj({})),n=r.lookup(p.of("EmbeddedFiles"),Z),n.has(p.of("Names"))||n.set(p.of("Names"),this.doc.context.obj([])),i=n.lookup(p.of("Names"),ae),i.push(U.fromText(this.embedder.fileName)),i.push(t),this.doc.catalog.has(p.of("AF"))||this.doc.catalog.set(p.of("AF"),this.doc.context.obj([])),a=this.doc.catalog.lookup(p.of("AF"),ae),a.push(t),this.alreadyEmbedded=!0,o.label=2;case 2:return[2]}})})},e.of=function(t,r,n){return new e(t,r,n)},e}(),H0=function(){function e(t,r,n){this.alreadyEmbedded=!1,this.ref=t,this.doc=r,this.embedder=n}return e.prototype.embed=function(){return Y(this,void 0,void 0,function(){var t,r,n,i,a,o,s;return J(this,function(f){switch(f.label){case 0:return this.alreadyEmbedded?[3,2]:(t=this.doc,r=t.catalog,n=t.context,[4,this.embedder.embedIntoContext(this.doc.context,this.ref)]);case 1:i=f.sent(),r.has(p.of("Names"))||r.set(p.of("Names"),n.obj({})),a=r.lookup(p.of("Names"),Z),a.has(p.of("JavaScript"))||a.set(p.of("JavaScript"),n.obj({})),o=a.lookup(p.of("JavaScript"),Z),o.has(p.of("Names"))||o.set(p.of("Names"),n.obj([])),s=o.lookup(p.of("Names"),ae),s.push(U.fromText(this.embedder.scriptName)),s.push(i),this.alreadyEmbedded=!0,f.label=2;case 2:return[2]}})})},e.of=function(t,r,n){return new e(t,r,n)},e}(),X0=function(){function e(t,r){this.script=t,this.scriptName=r}return e.for=function(t,r){return new e(t,r)},e.prototype.embedIntoContext=function(t,r){return Y(this,void 0,void 0,function(){var n;return J(this,function(i){return n=t.obj({Type:"Action",S:"JavaScript",JS:U.fromText(this.script)}),r?(t.assign(r,n),[2,r]):[2,t.register(n)]})})},e}(),Xt=function(){function e(t,r,n){var i=this;if(this.defaultWordBreaks=[" "],this.computePages=function(){var a=[];return i.catalog.Pages().traverse(function(o,s){if(o instanceof gt){var f=i.pageMap.get(o);f||(f=Ke.of(o,s,i),i.pageMap.set(o,f)),a.push(f)}}),a},this.getOrCreateForm=function(){var a=i.catalog.getOrCreateAcroForm();return q0.of(a,i)},x(t,"context",[[ia,"PDFContext"]]),x(r,"ignoreEncryption",["boolean"]),this.context=t,this.catalog=t.lookup(t.trailerInfo.Root),this.isEncrypted=!!t.lookup(t.trailerInfo.Encrypt),this.pageCache=pt.populatedBy(this.computePages),this.pageMap=new Map,this.formCache=pt.populatedBy(this.getOrCreateForm),this.fonts=[],this.images=[],this.embeddedPages=[],this.embeddedFiles=[],this.javaScripts=[],!r&&this.isEncrypted)throw new x0;n&&this.updateInfoDict()}return e.load=function(t,r){return r===void 0&&(r={}),Y(this,void 0,void 0,function(){var n,i,a,o,s,f,u,c,l,h,d,v;return J(this,function(g){switch(g.label){case 0:return n=r.ignoreEncryption,i=n===void 0?!1:n,a=r.parseSpeed,o=a===void 0?la.Slow:a,s=r.throwOnInvalidObject,f=s===void 0?!1:s,u=r.updateMetadata,c=u===void 0?!0:u,l=r.capNumbers,h=l===void 0?!1:l,x(t,"pdf",["string",Uint8Array,ArrayBuffer]),x(i,"ignoreEncryption",["boolean"]),x(o,"parseSpeed",["number"]),x(f,"throwOnInvalidObject",["boolean"]),d=Sr(t),[4,Yh.forBytesWithOptions(d,o,f,h).parseDocument()];case 1:return v=g.sent(),[2,new e(v,i,c)]}})})},e.create=function(t){return t===void 0&&(t={}),Y(this,void 0,void 0,function(){var r,n,i,a,o,s;return J(this,function(f){return r=t.updateMetadata,n=r===void 0?!0:r,i=ia.create(),a=ku.withContext(i),o=i.register(a),s=Fu.withContextAndPages(i,o),i.trailerInfo.Root=i.register(s),[2,new e(i,!1,n)]})})},e.prototype.registerFontkit=function(t){this.fontkit=t},e.prototype.getForm=function(){var t=this.formCache.access();return t.hasXFA()&&(console.warn("Removing XFA form data as pdf-lib does not support reading or writing XFA"),t.deleteXFA()),t},e.prototype.getTitle=function(){var t=this.getInfoDict().lookup(p.Title);if(t)return St(t),t.decodeText()},e.prototype.getAuthor=function(){var t=this.getInfoDict().lookup(p.Author);if(t)return St(t),t.decodeText()},e.prototype.getSubject=function(){var t=this.getInfoDict().lookup(p.Subject);if(t)return St(t),t.decodeText()},e.prototype.getKeywords=function(){var t=this.getInfoDict().lookup(p.Keywords);if(t)return St(t),t.decodeText()},e.prototype.getCreator=function(){var t=this.getInfoDict().lookup(p.Creator);if(t)return St(t),t.decodeText()},e.prototype.getProducer=function(){var t=this.getInfoDict().lookup(p.Producer);if(t)return St(t),t.decodeText()},e.prototype.getCreationDate=function(){var t=this.getInfoDict().lookup(p.CreationDate);if(t)return St(t),t.decodeDate()},e.prototype.getModificationDate=function(){var t=this.getInfoDict().lookup(p.ModDate);if(t)return St(t),t.decodeDate()},e.prototype.setTitle=function(t,r){x(t,"title",["string"]);var n=p.of("Title");if(this.getInfoDict().set(n,U.fromText(t)),r!=null&&r.showInWindowTitleBar){var i=this.catalog.getOrCreateViewerPreferences();i.setDisplayDocTitle(!0)}},e.prototype.setAuthor=function(t){x(t,"author",["string"]);var r=p.of("Author");this.getInfoDict().set(r,U.fromText(t))},e.prototype.setSubject=function(t){x(t,"author",["string"]);var r=p.of("Subject");this.getInfoDict().set(r,U.fromText(t))},e.prototype.setKeywords=function(t){x(t,"keywords",[Array]);var r=p.of("Keywords");this.getInfoDict().set(r,U.fromText(t.join(" ")))},e.prototype.setCreator=function(t){x(t,"creator",["string"]);var r=p.of("Creator");this.getInfoDict().set(r,U.fromText(t))},e.prototype.setProducer=function(t){x(t,"creator",["string"]);var r=p.of("Producer");this.getInfoDict().set(r,U.fromText(t))},e.prototype.setLanguage=function(t){x(t,"language",["string"]);var r=p.of("Lang");this.catalog.set(r,ue.of(t))},e.prototype.setCreationDate=function(t){x(t,"creationDate",[[Date,"Date"]]);var r=p.of("CreationDate");this.getInfoDict().set(r,ue.fromDate(t))},e.prototype.setModificationDate=function(t){x(t,"modificationDate",[[Date,"Date"]]);var r=p.of("ModDate");this.getInfoDict().set(r,ue.fromDate(t))},e.prototype.getPageCount=function(){return this.pageCount===void 0&&(this.pageCount=this.getPages().length),this.pageCount},e.prototype.getPages=function(){return this.pageCache.access()},e.prototype.getPage=function(t){var r=this.getPages();return je(t,"index",0,r.length-1),r[t]},e.prototype.getPageIndices=function(){return vf(0,this.getPageCount())},e.prototype.removePage=function(t){var r=this.getPageCount();if(this.pageCount===0)throw new F0;je(t,"index",0,r-1),this.catalog.removeLeafNode(t),this.pageCount=r-1},e.prototype.addPage=function(t){return x(t,"page",["undefined",[Ke,"PDFPage"],Array]),this.insertPage(this.getPageCount(),t)},e.prototype.insertPage=function(t,r){var n=this.getPageCount();if(je(t,"index",0,n),x(r,"page",["undefined",[Ke,"PDFPage"],Array]),!r||Array.isArray(r)){var i=Array.isArray(r)?r:L0.A4;r=Ke.create(this),r.setSize.apply(r,i)}else if(r.doc!==this)throw new S0;var a=this.catalog.insertLeafNode(r.ref,t);return r.node.setParent(a),this.pageMap.set(r.node,r),this.pageCache.invalidate(),this.pageCount=n+1,r},e.prototype.copyPages=function(t,r){return Y(this,void 0,void 0,function(){var n,i,a,o,s,f,u,c;return J(this,function(l){switch(l.label){case 0:return x(t,"srcDoc",[[e,"PDFDocument"]]),x(r,"indices",[Array]),[4,t.flush()];case 1:for(l.sent(),n=Lo.for(t.context,this.context),i=t.getPages(),a=new Array(r.length),o=0,s=r.length;o<s;o++)f=i[r[o]],u=n.copy(f.node),c=this.context.register(u),a[o]=Ke.of(u,c,this);return[2,a]}})})},e.prototype.copy=function(){return Y(this,void 0,void 0,function(){var t,r,n,i;return J(this,function(a){switch(a.label){case 0:return[4,e.create()];case 1:return t=a.sent(),[4,t.copyPages(this,this.getPageIndices())];case 2:for(r=a.sent(),n=0,i=r.length;n<i;n++)t.addPage(r[n]);return this.getAuthor()!==void 0&&t.setAuthor(this.getAuthor()),this.getCreationDate()!==void 0&&t.setCreationDate(this.getCreationDate()),this.getCreator()!==void 0&&t.setCreator(this.getCreator()),this.getModificationDate()!==void 0&&t.setModificationDate(this.getModificationDate()),this.getProducer()!==void 0&&t.setProducer(this.getProducer()),this.getSubject()!==void 0&&t.setSubject(this.getSubject()),this.getTitle()!==void 0&&t.setTitle(this.getTitle()),t.defaultWordBreaks=this.defaultWordBreaks,[2,t]}})})},e.prototype.addJavaScript=function(t,r){x(t,"name",["string"]),x(r,"script",["string"]);var n=X0.for(r,t),i=this.context.nextRef(),a=H0.of(i,this,n);this.javaScripts.push(a)},e.prototype.attach=function(t,r,n){return n===void 0&&(n={}),Y(this,void 0,void 0,function(){var i,a,o,s;return J(this,function(f){return x(t,"attachment",["string",Uint8Array,ArrayBuffer]),x(r,"name",["string"]),O(n.mimeType,"mimeType",["string"]),O(n.description,"description",["string"]),O(n.creationDate,"options.creationDate",[Date]),O(n.modificationDate,"options.modificationDate",[Date]),We(n.afRelationship,"options.afRelationship",aa),i=Sr(t),a=wh.for(i,r,n),o=this.context.nextRef(),s=G0.of(o,this,a),this.embeddedFiles.push(s),[2]})})},e.prototype.embedFont=function(t,r){return r===void 0&&(r={}),Y(this,void 0,void 0,function(){var n,i,a,o,s,f,u,c,l,h;return J(this,function(d){switch(d.label){case 0:return n=r.subset,i=n===void 0?!1:n,a=r.customName,o=r.features,x(t,"font",["string",Uint8Array,ArrayBuffer]),x(i,"subset",["boolean"]),zo(t)?(s=Jn.for(t,a),[3,7]):[3,1];case 1:return gf(t)?(f=Sr(t),u=this.assertFontkit(),i?[4,xh.for(u,f,a,o)]:[3,3]):[3,6];case 2:return c=d.sent(),[3,5];case 3:return[4,Ta.for(u,f,a,o)];case 4:c=d.sent(),d.label=5;case 5:return s=c,[3,7];case 6:throw new TypeError("`font` must be one of `StandardFonts | string | Uint8Array | ArrayBuffer`");case 7:return l=this.context.nextRef(),h=Ue.of(l,this,s),this.fonts.push(h),[2,h]}})})},e.prototype.embedStandardFont=function(t,r){if(x(t,"font",["string"]),!zo(t))throw new TypeError("`font` must be one of type `StandardFonts`");var n=Jn.for(t,r),i=this.context.nextRef(),a=Ue.of(i,this,n);return this.fonts.push(a),a},e.prototype.embedJpg=function(t){return Y(this,void 0,void 0,function(){var r,n,i,a;return J(this,function(o){switch(o.label){case 0:return x(t,"jpg",["string",Uint8Array,ArrayBuffer]),r=Sr(t),[4,vu.for(r)];case 1:return n=o.sent(),i=this.context.nextRef(),a=fa.of(i,this,n),this.images.push(a),[2,a]}})})},e.prototype.embedPng=function(t){return Y(this,void 0,void 0,function(){var r,n,i,a;return J(this,function(o){switch(o.label){case 0:return x(t,"png",["string",Uint8Array,ArrayBuffer]),r=Sr(t),[4,pu.for(r)];case 1:return n=o.sent(),i=this.context.nextRef(),a=fa.of(i,this,n),this.images.push(a),[2,a]}})})},e.prototype.embedPdf=function(t,r){return r===void 0&&(r=[0]),Y(this,void 0,void 0,function(){var n,i,a;return J(this,function(o){switch(o.label){case 0:return x(t,"pdf",["string",Uint8Array,ArrayBuffer,[e,"PDFDocument"]]),x(r,"indices",[Array]),t instanceof e?(i=t,[3,3]):[3,1];case 1:return[4,e.load(t)];case 2:i=o.sent(),o.label=3;case 3:return n=i,a=pf(n.getPages(),r),[2,this.embedPages(a)]}})})},e.prototype.embedPage=function(t,r,n){return Y(this,void 0,void 0,function(){var i;return J(this,function(a){switch(a.label){case 0:return x(t,"page",[[Ke,"PDFPage"]]),[4,this.embedPages([t],[r],[n])];case 1:return i=a.sent()[0],[2,i]}})})},e.prototype.embedPages=function(t,r,n){return r===void 0&&(r=[]),n===void 0&&(n=[]),Y(this,void 0,void 0,function(){var u,c,i,a,o,s,f,u,c,l,h,d,v,g,m;return J(this,function(b){switch(b.label){case 0:if(t.length===0)return[2,[]];for(u=0,c=t.length-1;u<c;u++)if(i=t[u],a=t[u+1],i.node.context!==a.node.context)throw new Xl;o=t[0].node.context,s=o===this.context?function(F){return F}:Lo.for(o,this.context).copy,f=new Array(t.length),u=0,c=t.length,b.label=1;case 1:return u<c?(l=s(t[u].node),h=r[u],d=n[u],[4,bu.for(l,h,d)]):[3,4];case 2:v=b.sent(),g=this.context.nextRef(),f[u]=Xu.of(g,this,v),b.label=3;case 3:return u++,[3,1];case 4:return(m=this.embeddedPages).push.apply(m,f),[2,f]}})})},e.prototype.flush=function(){return Y(this,void 0,void 0,function(){return J(this,function(t){switch(t.label){case 0:return[4,this.embedAll(this.fonts)];case 1:return t.sent(),[4,this.embedAll(this.images)];case 2:return t.sent(),[4,this.embedAll(this.embeddedPages)];case 3:return t.sent(),[4,this.embedAll(this.embeddedFiles)];case 4:return t.sent(),[4,this.embedAll(this.javaScripts)];case 5:return t.sent(),[2]}})})},e.prototype.save=function(t){return t===void 0&&(t={}),Y(this,void 0,void 0,function(){var r,n,i,a,o,s,f,u,c,l;return J(this,function(h){switch(h.label){case 0:return r=t.useObjectStreams,n=r===void 0?!0:r,i=t.addDefaultPage,a=i===void 0?!0:i,o=t.objectsPerTick,s=o===void 0?50:o,f=t.updateFieldAppearances,u=f===void 0?!0:f,x(n,"useObjectStreams",["boolean"]),x(a,"addDefaultPage",["boolean"]),x(s,"objectsPerTick",["number"]),x(u,"updateFieldAppearances",["boolean"]),a&&this.getPageCount()===0&&this.addPage(),u&&(c=this.formCache.getValue(),c&&c.updateFieldAppearances()),[4,this.flush()];case 1:return h.sent(),l=n?vh:hu,[2,l.forContext(this.context,s).serializeToBuffer()]}})})},e.prototype.saveAsBase64=function(t){return t===void 0&&(t={}),Y(this,void 0,void 0,function(){var r,n,i,a,o;return J(this,function(s){switch(s.label){case 0:return r=t.dataUri,n=r===void 0?!1:r,i=Ju(t,["dataUri"]),x(n,"dataUri",["boolean"]),[4,this.save(i)];case 1:return a=s.sent(),o=Qu(a),[2,n?"data:application/pdf;base64,"+o:o]}})})},e.prototype.findPageForAnnotationRef=function(t){for(var r=this.getPages(),n=0,i=r.length;n<i;n++){var a=r[n],o=a.node.Annots();if((o==null?void 0:o.indexOf(t))!==void 0)return a}},e.prototype.embedAll=function(t){return Y(this,void 0,void 0,function(){var r,n;return J(this,function(i){switch(i.label){case 0:r=0,n=t.length,i.label=1;case 1:return r<n?[4,t[r].embed()]:[3,4];case 2:i.sent(),i.label=3;case 3:return r++,[3,1];case 4:return[2]}})})},e.prototype.updateInfoDict=function(){var t="pdf-lib (https://github.com/Hopding/pdf-lib)",r=new Date,n=this.getInfoDict();this.setProducer(t),this.setModificationDate(r),n.get(p.of("Creator"))||this.setCreator(t),n.get(p.of("CreationDate"))||this.setCreationDate(r)},e.prototype.getInfoDict=function(){var t=this.context.lookup(this.context.trailerInfo.Info);if(t instanceof Z)return t;var r=this.context.obj({});return this.context.trailerInfo.Info=this.context.register(r),r},e.prototype.assertFontkit=function(){if(!this.fontkit)throw new w0;return this.fontkit},e}();function St(e){if(!(e instanceof U)&&!(e instanceof ue))throw new Zn([U,ue],e)}var ht;(function(e){e.Normal="Normal",e.Multiply="Multiply",e.Screen="Screen",e.Overlay="Overlay",e.Darken="Darken",e.Lighten="Lighten",e.ColorDodge="ColorDodge",e.ColorBurn="ColorBurn",e.HardLight="HardLight",e.SoftLight="SoftLight",e.Difference="Difference",e.Exclusion="Exclusion"})(ht||(ht={}));var Ke=function(){function e(t,r,n){this.fontSize=24,this.fontColor=ce(0,0,0),this.lineHeight=24,this.x=0,this.y=0,x(t,"leafNode",[[gt,"PDFPageLeaf"]]),x(r,"ref",[[fe,"PDFRef"]]),x(n,"doc",[[Xt,"PDFDocument"]]),this.node=t,this.ref=r,this.doc=n}return e.prototype.setRotation=function(t){var r=Du(t);su(r,"degreesAngle",90),this.node.set(p.of("Rotate"),this.doc.context.obj(r))},e.prototype.getRotation=function(){var t=this.node.Rotate();return V(t?t.asNumber():0)},e.prototype.setSize=function(t,r){x(t,"width",["number"]),x(r,"height",["number"]);var n=this.getMediaBox();this.setMediaBox(n.x,n.y,t,r);var i=this.getCropBox(),a=this.getBleedBox(),o=this.getTrimBox(),s=this.getArtBox(),f=this.node.CropBox(),u=this.node.BleedBox(),c=this.node.TrimBox(),l=this.node.ArtBox();f&&Tn(i,n)&&this.setCropBox(n.x,n.y,t,r),u&&Tn(a,n)&&this.setBleedBox(n.x,n.y,t,r),c&&Tn(o,n)&&this.setTrimBox(n.x,n.y,t,r),l&&Tn(s,n)&&this.setArtBox(n.x,n.y,t,r)},e.prototype.setWidth=function(t){x(t,"width",["number"]),this.setSize(t,this.getSize().height)},e.prototype.setHeight=function(t){x(t,"height",["number"]),this.setSize(this.getSize().width,t)},e.prototype.setMediaBox=function(t,r,n,i){x(t,"x",["number"]),x(r,"y",["number"]),x(n,"width",["number"]),x(i,"height",["number"]);var a=this.doc.context.obj([t,r,t+n,r+i]);this.node.set(p.MediaBox,a)},e.prototype.setCropBox=function(t,r,n,i){x(t,"x",["number"]),x(r,"y",["number"]),x(n,"width",["number"]),x(i,"height",["number"]);var a=this.doc.context.obj([t,r,t+n,r+i]);this.node.set(p.CropBox,a)},e.prototype.setBleedBox=function(t,r,n,i){x(t,"x",["number"]),x(r,"y",["number"]),x(n,"width",["number"]),x(i,"height",["number"]);var a=this.doc.context.obj([t,r,t+n,r+i]);this.node.set(p.BleedBox,a)},e.prototype.setTrimBox=function(t,r,n,i){x(t,"x",["number"]),x(r,"y",["number"]),x(n,"width",["number"]),x(i,"height",["number"]);var a=this.doc.context.obj([t,r,t+n,r+i]);this.node.set(p.TrimBox,a)},e.prototype.setArtBox=function(t,r,n,i){x(t,"x",["number"]),x(r,"y",["number"]),x(n,"width",["number"]),x(i,"height",["number"]);var a=this.doc.context.obj([t,r,t+n,r+i]);this.node.set(p.ArtBox,a)},e.prototype.getSize=function(){var t=this.getMediaBox(),r=t.width,n=t.height;return{width:r,height:n}},e.prototype.getWidth=function(){return this.getSize().width},e.prototype.getHeight=function(){return this.getSize().height},e.prototype.getMediaBox=function(){var t=this.node.MediaBox();return t.asRectangle()},e.prototype.getCropBox=function(){var t,r=this.node.CropBox();return(t=r==null?void 0:r.asRectangle())!==null&&t!==void 0?t:this.getMediaBox()},e.prototype.getBleedBox=function(){var t,r=this.node.BleedBox();return(t=r==null?void 0:r.asRectangle())!==null&&t!==void 0?t:this.getCropBox()},e.prototype.getTrimBox=function(){var t,r=this.node.TrimBox();return(t=r==null?void 0:r.asRectangle())!==null&&t!==void 0?t:this.getCropBox()},e.prototype.getArtBox=function(){var t,r=this.node.ArtBox();return(t=r==null?void 0:r.asRectangle())!==null&&t!==void 0?t:this.getCropBox()},e.prototype.translateContent=function(t,r){x(t,"x",["number"]),x(r,"y",["number"]),this.node.normalize(),this.getContentStream();var n=this.createContentStream(xe(),Ge(t,r)),i=this.doc.context.register(n),a=this.createContentStream(we()),o=this.doc.context.register(a);this.node.wrapContentStreams(i,o)},e.prototype.scale=function(t,r){x(t,"x",["number"]),x(r,"y",["number"]),this.setSize(this.getWidth()*t,this.getHeight()*r),this.scaleContent(t,r),this.scaleAnnotations(t,r)},e.prototype.scaleContent=function(t,r){x(t,"x",["number"]),x(r,"y",["number"]),this.node.normalize(),this.getContentStream();var n=this.createContentStream(xe(),rn(t,r)),i=this.doc.context.register(n),a=this.createContentStream(we()),o=this.doc.context.register(a);this.node.wrapContentStreams(i,o)},e.prototype.scaleAnnotations=function(t,r){x(t,"x",["number"]),x(r,"y",["number"]);var n=this.node.Annots();if(n)for(var i=0;i<n.size();i++){var a=n.lookup(i);a instanceof Z&&this.scaleAnnot(a,t,r)}},e.prototype.resetPosition=function(){this.getContentStream(!1),this.x=0,this.y=0},e.prototype.setFont=function(t){x(t,"font",[[Ue,"PDFFont"]]),this.font=t,this.fontKey=this.node.newFontDictionary(this.font.name,this.font.ref)},e.prototype.setFontSize=function(t){x(t,"fontSize",["number"]),this.fontSize=t},e.prototype.setFontColor=function(t){x(t,"fontColor",[[Object,"Color"]]),this.fontColor=t},e.prototype.setLineHeight=function(t){x(t,"lineHeight",["number"]),this.lineHeight=t},e.prototype.getPosition=function(){return{x:this.x,y:this.y}},e.prototype.getX=function(){return this.x},e.prototype.getY=function(){return this.y},e.prototype.moveTo=function(t,r){x(t,"x",["number"]),x(r,"y",["number"]),this.x=t,this.y=r},e.prototype.moveDown=function(t){x(t,"yDecrease",["number"]),this.y-=t},e.prototype.moveUp=function(t){x(t,"yIncrease",["number"]),this.y+=t},e.prototype.moveLeft=function(t){x(t,"xDecrease",["number"]),this.x-=t},e.prototype.moveRight=function(t){x(t,"xIncrease",["number"]),this.x+=t},e.prototype.pushOperators=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];ou(t,"operator",[[ee,"PDFOperator"]]);var n=this.getContentStream();n.push.apply(n,t)},e.prototype.drawText=function(t,r){var n,i,a,o,s,f,u;r===void 0&&(r={}),x(t,"text",["string"]),O(r.color,"options.color",[[Object,"Color"]]),_e(r.opacity,"opacity.opacity",0,1),O(r.font,"options.font",[[Ue,"PDFFont"]]),O(r.size,"options.size",["number"]),O(r.rotate,"options.rotate",[[Object,"Rotation"]]),O(r.xSkew,"options.xSkew",[[Object,"Rotation"]]),O(r.ySkew,"options.ySkew",[[Object,"Rotation"]]),O(r.x,"options.x",["number"]),O(r.y,"options.y",["number"]),O(r.lineHeight,"options.lineHeight",["number"]),O(r.maxWidth,"options.maxWidth",["number"]),O(r.wordBreaks,"options.wordBreaks",[Array]),We(r.blendMode,"options.blendMode",ht);for(var c=this.setOrEmbedFont(r.font),l=c.oldFont,h=c.newFont,d=c.newFontKey,v=r.size||this.fontSize,g=r.wordBreaks||this.doc.defaultWordBreaks,m=function(T){return h.widthOfTextAtSize(T,v)},b=r.maxWidth===void 0?ss(an(t)):sf(t,g,r.maxWidth,m),F=new Array(b.length),w=0,C=b.length;w<C;w++)F[w]=h.encodeText(b[w]);var S=this.maybeEmbedGraphicsState({opacity:r.opacity,blendMode:r.blendMode}),k=this.getContentStream();k.push.apply(k,h0(F,{color:(n=r.color)!==null&&n!==void 0?n:this.fontColor,font:d,size:v,rotate:(i=r.rotate)!==null&&i!==void 0?i:V(0),xSkew:(a=r.xSkew)!==null&&a!==void 0?a:V(0),ySkew:(o=r.ySkew)!==null&&o!==void 0?o:V(0),x:(s=r.x)!==null&&s!==void 0?s:this.x,y:(f=r.y)!==null&&f!==void 0?f:this.y,lineHeight:(u=r.lineHeight)!==null&&u!==void 0?u:this.lineHeight,graphicsState:S})),r.font&&(l?this.setFont(l):this.resetFont())},e.prototype.drawImage=function(t,r){var n,i,a,o,s,f,u;r===void 0&&(r={}),x(t,"image",[[fa,"PDFImage"]]),O(r.x,"options.x",["number"]),O(r.y,"options.y",["number"]),O(r.width,"options.width",["number"]),O(r.height,"options.height",["number"]),O(r.rotate,"options.rotate",[[Object,"Rotation"]]),O(r.xSkew,"options.xSkew",[[Object,"Rotation"]]),O(r.ySkew,"options.ySkew",[[Object,"Rotation"]]),_e(r.opacity,"opacity.opacity",0,1),We(r.blendMode,"options.blendMode",ht);var c=this.node.newXObject("Image",t.ref),l=this.maybeEmbedGraphicsState({opacity:r.opacity,blendMode:r.blendMode}),h=this.getContentStream();h.push.apply(h,Vu(c,{x:(n=r.x)!==null&&n!==void 0?n:this.x,y:(i=r.y)!==null&&i!==void 0?i:this.y,width:(a=r.width)!==null&&a!==void 0?a:t.size().width,height:(o=r.height)!==null&&o!==void 0?o:t.size().height,rotate:(s=r.rotate)!==null&&s!==void 0?s:V(0),xSkew:(f=r.xSkew)!==null&&f!==void 0?f:V(0),ySkew:(u=r.ySkew)!==null&&u!==void 0?u:V(0),graphicsState:l}))},e.prototype.drawPage=function(t,r){var n,i,a,o,s;r===void 0&&(r={}),x(t,"embeddedPage",[[Xu,"PDFEmbeddedPage"]]),O(r.x,"options.x",["number"]),O(r.y,"options.y",["number"]),O(r.xScale,"options.xScale",["number"]),O(r.yScale,"options.yScale",["number"]),O(r.width,"options.width",["number"]),O(r.height,"options.height",["number"]),O(r.rotate,"options.rotate",[[Object,"Rotation"]]),O(r.xSkew,"options.xSkew",[[Object,"Rotation"]]),O(r.ySkew,"options.ySkew",[[Object,"Rotation"]]),_e(r.opacity,"opacity.opacity",0,1),We(r.blendMode,"options.blendMode",ht);var f=this.node.newXObject("EmbeddedPdfPage",t.ref),u=this.maybeEmbedGraphicsState({opacity:r.opacity,blendMode:r.blendMode}),c=r.width!==void 0?r.width/t.width:r.xScale!==void 0?r.xScale:1,l=r.height!==void 0?r.height/t.height:r.yScale!==void 0?r.yScale:1,h=this.getContentStream();h.push.apply(h,d0(f,{x:(n=r.x)!==null&&n!==void 0?n:this.x,y:(i=r.y)!==null&&i!==void 0?i:this.y,xScale:c,yScale:l,rotate:(a=r.rotate)!==null&&a!==void 0?a:V(0),xSkew:(o=r.xSkew)!==null&&o!==void 0?o:V(0),ySkew:(s=r.ySkew)!==null&&s!==void 0?s:V(0),graphicsState:u}))},e.prototype.drawSvgPath=function(t,r){var n,i,a,o,s,f,u,c,l;r===void 0&&(r={}),x(t,"path",["string"]),O(r.x,"options.x",["number"]),O(r.y,"options.y",["number"]),O(r.scale,"options.scale",["number"]),O(r.rotate,"options.rotate",[[Object,"Rotation"]]),O(r.borderWidth,"options.borderWidth",["number"]),O(r.color,"options.color",[[Object,"Color"]]),_e(r.opacity,"opacity.opacity",0,1),O(r.borderColor,"options.borderColor",[[Object,"Color"]]),O(r.borderDashArray,"options.borderDashArray",[Array]),O(r.borderDashPhase,"options.borderDashPhase",["number"]),We(r.borderLineCap,"options.borderLineCap",ur),_e(r.borderOpacity,"options.borderOpacity",0,1),We(r.blendMode,"options.blendMode",ht);var h=this.maybeEmbedGraphicsState({opacity:r.opacity,borderOpacity:r.borderOpacity,blendMode:r.blendMode});!("color"in r)&&!("borderColor"in r)&&(r.borderColor=ce(0,0,0));var d=this.getContentStream();d.push.apply(d,y0(t,{x:(n=r.x)!==null&&n!==void 0?n:this.x,y:(i=r.y)!==null&&i!==void 0?i:this.y,scale:r.scale,rotate:(a=r.rotate)!==null&&a!==void 0?a:V(0),color:(o=r.color)!==null&&o!==void 0?o:void 0,borderColor:(s=r.borderColor)!==null&&s!==void 0?s:void 0,borderWidth:(f=r.borderWidth)!==null&&f!==void 0?f:0,borderDashArray:(u=r.borderDashArray)!==null&&u!==void 0?u:void 0,borderDashPhase:(c=r.borderDashPhase)!==null&&c!==void 0?c:void 0,borderLineCap:(l=r.borderLineCap)!==null&&l!==void 0?l:void 0,graphicsState:h}))},e.prototype.drawLine=function(t){var r,n,i,a,o;x(t.start,"options.start",[[Object,"{ x: number, y: number }"]]),x(t.end,"options.end",[[Object,"{ x: number, y: number }"]]),x(t.start.x,"options.start.x",["number"]),x(t.start.y,"options.start.y",["number"]),x(t.end.x,"options.end.x",["number"]),x(t.end.y,"options.end.y",["number"]),O(t.thickness,"options.thickness",["number"]),O(t.color,"options.color",[[Object,"Color"]]),O(t.dashArray,"options.dashArray",[Array]),O(t.dashPhase,"options.dashPhase",["number"]),We(t.lineCap,"options.lineCap",ur),_e(t.opacity,"opacity.opacity",0,1),We(t.blendMode,"options.blendMode",ht);var s=this.maybeEmbedGraphicsState({borderOpacity:t.opacity,blendMode:t.blendMode});"color"in t||(t.color=ce(0,0,0));var f=this.getContentStream();f.push.apply(f,v0({start:t.start,end:t.end,thickness:(r=t.thickness)!==null&&r!==void 0?r:1,color:(n=t.color)!==null&&n!==void 0?n:void 0,dashArray:(i=t.dashArray)!==null&&i!==void 0?i:void 0,dashPhase:(a=t.dashPhase)!==null&&a!==void 0?a:void 0,lineCap:(o=t.lineCap)!==null&&o!==void 0?o:void 0,graphicsState:s}))},e.prototype.drawRectangle=function(t){var r,n,i,a,o,s,f,u,c,l,h,d,v;t===void 0&&(t={}),O(t.x,"options.x",["number"]),O(t.y,"options.y",["number"]),O(t.width,"options.width",["number"]),O(t.height,"options.height",["number"]),O(t.rotate,"options.rotate",[[Object,"Rotation"]]),O(t.xSkew,"options.xSkew",[[Object,"Rotation"]]),O(t.ySkew,"options.ySkew",[[Object,"Rotation"]]),O(t.borderWidth,"options.borderWidth",["number"]),O(t.color,"options.color",[[Object,"Color"]]),_e(t.opacity,"opacity.opacity",0,1),O(t.borderColor,"options.borderColor",[[Object,"Color"]]),O(t.borderDashArray,"options.borderDashArray",[Array]),O(t.borderDashPhase,"options.borderDashPhase",["number"]),We(t.borderLineCap,"options.borderLineCap",ur),_e(t.borderOpacity,"options.borderOpacity",0,1),We(t.blendMode,"options.blendMode",ht);var g=this.maybeEmbedGraphicsState({opacity:t.opacity,borderOpacity:t.borderOpacity,blendMode:t.blendMode});!("color"in t)&&!("borderColor"in t)&&(t.color=ce(0,0,0));var m=this.getContentStream();m.push.apply(m,vr({x:(r=t.x)!==null&&r!==void 0?r:this.x,y:(n=t.y)!==null&&n!==void 0?n:this.y,width:(i=t.width)!==null&&i!==void 0?i:150,height:(a=t.height)!==null&&a!==void 0?a:100,rotate:(o=t.rotate)!==null&&o!==void 0?o:V(0),xSkew:(s=t.xSkew)!==null&&s!==void 0?s:V(0),ySkew:(f=t.ySkew)!==null&&f!==void 0?f:V(0),borderWidth:(u=t.borderWidth)!==null&&u!==void 0?u:0,color:(c=t.color)!==null&&c!==void 0?c:void 0,borderColor:(l=t.borderColor)!==null&&l!==void 0?l:void 0,borderDashArray:(h=t.borderDashArray)!==null&&h!==void 0?h:void 0,borderDashPhase:(d=t.borderDashPhase)!==null&&d!==void 0?d:void 0,graphicsState:g,borderLineCap:(v=t.borderLineCap)!==null&&v!==void 0?v:void 0}))},e.prototype.drawSquare=function(t){t===void 0&&(t={});var r=t.size;O(r,"size",["number"]),this.drawRectangle(H(H({},t),{width:r,height:r}))},e.prototype.drawEllipse=function(t){var r,n,i,a,o,s,f,u,c,l,h;t===void 0&&(t={}),O(t.x,"options.x",["number"]),O(t.y,"options.y",["number"]),O(t.xScale,"options.xScale",["number"]),O(t.yScale,"options.yScale",["number"]),O(t.rotate,"options.rotate",[[Object,"Rotation"]]),O(t.color,"options.color",[[Object,"Color"]]),_e(t.opacity,"opacity.opacity",0,1),O(t.borderColor,"options.borderColor",[[Object,"Color"]]),_e(t.borderOpacity,"options.borderOpacity",0,1),O(t.borderWidth,"options.borderWidth",["number"]),O(t.borderDashArray,"options.borderDashArray",[Array]),O(t.borderDashPhase,"options.borderDashPhase",["number"]),We(t.borderLineCap,"options.borderLineCap",ur),We(t.blendMode,"options.blendMode",ht);var d=this.maybeEmbedGraphicsState({opacity:t.opacity,borderOpacity:t.borderOpacity,blendMode:t.blendMode});!("color"in t)&&!("borderColor"in t)&&(t.color=ce(0,0,0));var v=this.getContentStream();v.push.apply(v,ua({x:(r=t.x)!==null&&r!==void 0?r:this.x,y:(n=t.y)!==null&&n!==void 0?n:this.y,xScale:(i=t.xScale)!==null&&i!==void 0?i:100,yScale:(a=t.yScale)!==null&&a!==void 0?a:100,rotate:(o=t.rotate)!==null&&o!==void 0?o:void 0,color:(s=t.color)!==null&&s!==void 0?s:void 0,borderColor:(f=t.borderColor)!==null&&f!==void 0?f:void 0,borderWidth:(u=t.borderWidth)!==null&&u!==void 0?u:0,borderDashArray:(c=t.borderDashArray)!==null&&c!==void 0?c:void 0,borderDashPhase:(l=t.borderDashPhase)!==null&&l!==void 0?l:void 0,borderLineCap:(h=t.borderLineCap)!==null&&h!==void 0?h:void 0,graphicsState:d}))},e.prototype.drawCircle=function(t){t===void 0&&(t={});var r=t.size,n=r===void 0?100:r;O(n,"size",["number"]),this.drawEllipse(H(H({},t),{xScale:n,yScale:n}))},e.prototype.setOrEmbedFont=function(t){var r=this.font,n=this.fontKey;t?this.setFont(t):this.getFont();var i=this.font,a=this.fontKey;return{oldFont:r,oldFontKey:n,newFont:i,newFontKey:a}},e.prototype.getFont=function(){if(!this.font||!this.fontKey){var t=this.doc.embedStandardFont(ai.Helvetica);this.setFont(t)}return[this.font,this.fontKey]},e.prototype.resetFont=function(){this.font=void 0,this.fontKey=void 0},e.prototype.getContentStream=function(t){return t===void 0&&(t=!0),t&&this.contentStream?this.contentStream:(this.contentStream=this.createContentStream(),this.contentStreamRef=this.doc.context.register(this.contentStream),this.node.addContentStream(this.contentStreamRef),this.contentStream)},e.prototype.createContentStream=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=this.doc.context.obj({}),i=qr.of(n,t);return i},e.prototype.maybeEmbedGraphicsState=function(t){var r=t.opacity,n=t.borderOpacity,i=t.blendMode;if(!(r===void 0&&n===void 0&&i===void 0)){var a=this.doc.context.obj({Type:"ExtGState",ca:r,CA:n,BM:i}),o=this.node.newExtGState("GS",a);return o}},e.prototype.scaleAnnot=function(t,r,n){for(var i=["RD","CL","Vertices","QuadPoints","L","Rect"],a=0,o=i.length;a<o;a++){var s=t.lookup(p.of(i[a]));s instanceof ae&&s.scalePDFNumbers(r,n)}var f=t.lookup(p.of("InkList"));if(f instanceof ae)for(var a=0,o=f.size();a<o;a++){var u=f.lookup(a);u instanceof ae&&u.scalePDFNumbers(r,n)}},e.of=function(t,r,n){return new e(t,r,n)},e.create=function(t){x(t,"doc",[[Xt,"PDFDocument"]]);var r=fe.of(-1),n=gt.withContextAndParent(t.context,r),i=t.context.register(n);return new e(n,i,t)},e}(),Z0=function(e){E(t,e);function t(r,n,i){var a=e.call(this,r,n,i)||this;return x(r,"acroButton",[[yi,"PDFAcroPushButton"]]),a.acroField=r,a}return t.prototype.setImage=function(r,n){n===void 0&&(n=Et.Center);for(var i=this.acroField.getWidgets(),a=0,o=i.length;a<o;a++){var s=i[a],f=this.createImageAppearanceStream(s,r,n);this.updateWidgetAppearances(s,{normal:f})}this.markAsClean()},t.prototype.setFontSize=function(r){hi(r,"fontSize"),this.acroField.setFontSize(r),this.markAsDirty()},t.prototype.addToPage=function(r,n,i){var a,o,s,f,u,c,l,h,d,v,g;O(r,"text",["string"]),O(n,"page",[[Ke,"PDFPage"]]),wr(i);var m=this.createWidget({x:((a=i==null?void 0:i.x)!==null&&a!==void 0?a:0)-((o=i==null?void 0:i.borderWidth)!==null&&o!==void 0?o:0)/2,y:((s=i==null?void 0:i.y)!==null&&s!==void 0?s:0)-((f=i==null?void 0:i.borderWidth)!==null&&f!==void 0?f:0)/2,width:(u=i==null?void 0:i.width)!==null&&u!==void 0?u:100,height:(c=i==null?void 0:i.height)!==null&&c!==void 0?c:50,textColor:(l=i==null?void 0:i.textColor)!==null&&l!==void 0?l:ce(0,0,0),backgroundColor:(h=i==null?void 0:i.backgroundColor)!==null&&h!==void 0?h:ce(.75,.75,.75),borderColor:i==null?void 0:i.borderColor,borderWidth:(d=i==null?void 0:i.borderWidth)!==null&&d!==void 0?d:0,rotate:(v=i==null?void 0:i.rotate)!==null&&v!==void 0?v:V(0),caption:r,hidden:i==null?void 0:i.hidden,page:n.ref}),b=this.doc.context.register(m.dict);this.acroField.addWidget(b);var F=(g=i==null?void 0:i.font)!==null&&g!==void 0?g:this.doc.getForm().getDefaultFont();this.updateWidgetAppearance(m,F),n.node.addAnnot(b)},t.prototype.needsAppearancesUpdate=function(){var r;if(this.isDirty())return!0;for(var n=this.acroField.getWidgets(),i=0,a=n.length;i<a;i++){var o=n[i],s=((r=o.getAppearances())===null||r===void 0?void 0:r.normal)instanceof Ie;if(!s)return!0}return!1},t.prototype.defaultUpdateAppearances=function(r){x(r,"font",[[Ue,"PDFFont"]]),this.updateAppearances(r)},t.prototype.updateAppearances=function(r,n){x(r,"font",[[Ue,"PDFFont"]]),O(n,"provider",[Function]);for(var i=this.acroField.getWidgets(),a=0,o=i.length;a<o;a++){var s=i[a];this.updateWidgetAppearance(s,r,n)}},t.prototype.updateWidgetAppearance=function(r,n,i){var a=i??I0,o=xr(a(this,r,n));this.updateWidgetAppearanceWithFont(r,n,o)},t.of=function(r,n,i){return new t(r,n,i)},t}($t);const Ln=Z0;
