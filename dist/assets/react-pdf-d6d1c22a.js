import"./vendor-1c28ea83.js";import{p as o,a as s}from"./@react-pdf-viewer/core-75a73120.js";import{w as r}from"./react-calendar-eace60fa.js";const e="default"in o?s:o,n=e,i=typeof document<"u",a=i&&window.location.protocol==="file:",t="On Chromium based browsers, you can use --allow-file-access-from-files flag for debugging purposes.";function l(){r(!a,`Loading PDF.js worker may not work on protocols other than HTTP/HTTPS. ${t}`)}l();n.GlobalWorkerOptions.workerSrc="pdf.worker.js";export{n as p};
