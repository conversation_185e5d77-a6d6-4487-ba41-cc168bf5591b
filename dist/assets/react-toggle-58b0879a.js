import{r as h}from"./vendor-1c28ea83.js";import{c as T}from"./@uppy/dashboard-3a4b1704.js";import{p as E}from"./@fortawesome/react-fontawesome-b5aa048d.js";var _={},d={};Object.defineProperty(d,"__esModule",{value:!0});var x=h,p=O(x);function O(t){return t&&t.__esModule?t:{default:t}}d.default=function(){return p.default.createElement("svg",{width:"14",height:"11",viewBox:"0 0 14 11"},p.default.createElement("path",{d:"M11.264 0L5.26 6.004 2.103 2.847 0 4.95l5.26 5.26 8.108-8.107L11.264 0",fill:"#fff",fillRule:"evenodd"}))};var f={};Object.defineProperty(f,"__esModule",{value:!0});var C=h,k=w(C);function w(t){return t&&t.__esModule?t:{default:t}}f.default=function(){return k.default.createElement("svg",{width:"10",height:"10",viewBox:"0 0 10 10"},k.default.createElement("path",{d:"M9.9 2.12L7.78 0 4.95 2.828 2.12 0 0 2.12l2.83 2.83L0 7.776 2.123 9.9 4.95 7.07 7.78 9.9 9.9 7.776 7.072 4.95 9.9 2.12",fill:"#fff",fillRule:"evenodd"}))};var v={};Object.defineProperty(v,"__esModule",{value:!0});v.pointerCoord=M;function M(t){if(t){var a=t.changedTouches;if(a&&a.length>0){var n=a[0];return{x:n.clientX,y:n.clientY}}var e=t.pageX;if(e!==void 0)return{x:e,y:t.pageY}}return{x:0,y:0}}Object.defineProperty(_,"__esModule",{value:!0});var P=Object.assign||function(t){for(var a=1;a<arguments.length;a++){var n=arguments[a];for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(t[e]=n[e])}return t},S=function(){function t(a,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(a,r.key,r)}}return function(a,n,e){return n&&t(a.prototype,n),e&&t(a,e),a}}(),g=h,i=o(g),F=T,B=o(F),N=E,c=o(N),D=d,R=o(D),X=f,$=o(X),s=v;function o(t){return t&&t.__esModule?t:{default:t}}function I(t,a){var n={};for(var e in t)a.indexOf(e)>=0||Object.prototype.hasOwnProperty.call(t,e)&&(n[e]=t[e]);return n}function L(t,a){if(!(t instanceof a))throw new TypeError("Cannot call a class as a function")}function j(t,a){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:t}function q(t,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);t.prototype=Object.create(a&&a.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(t,a):t.__proto__=a)}var u=function(t){q(a,t);function a(n){L(this,a);var e=j(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,n));return e.handleClick=e.handleClick.bind(e),e.handleTouchStart=e.handleTouchStart.bind(e),e.handleTouchMove=e.handleTouchMove.bind(e),e.handleTouchEnd=e.handleTouchEnd.bind(e),e.handleFocus=e.handleFocus.bind(e),e.handleBlur=e.handleBlur.bind(e),e.previouslyChecked=!!(n.checked||n.defaultChecked),e.state={checked:!!(n.checked||n.defaultChecked),hasFocus:!1},e}return S(a,[{key:"componentDidUpdate",value:function(e){e.checked!==this.props.checked&&this.setState({checked:!!this.props.checked})}},{key:"handleClick",value:function(e){if(!this.props.disabled){var r=this.input;if(e.target!==r&&!this.moved){this.previouslyChecked=r.checked,e.preventDefault(),r.focus(),r.click();return}var l=this.props.hasOwnProperty("checked")?this.props.checked:r.checked;this.setState({checked:l})}}},{key:"handleTouchStart",value:function(e){this.props.disabled||(this.startX=(0,s.pointerCoord)(e).x,this.activated=!0)}},{key:"handleTouchMove",value:function(e){if(this.activated&&(this.moved=!0,this.startX)){var r=(0,s.pointerCoord)(e).x;this.state.checked&&r+15<this.startX?(this.setState({checked:!1}),this.startX=r,this.activated=!0):r-15>this.startX&&(this.setState({checked:!0}),this.startX=r,this.activated=r<this.startX+5)}}},{key:"handleTouchEnd",value:function(e){if(this.moved){var r=this.input;if(e.preventDefault(),this.startX){var l=(0,s.pointerCoord)(e).x;this.previouslyChecked===!0&&this.startX+4>l?this.previouslyChecked!==this.state.checked&&(this.setState({checked:!1}),this.previouslyChecked=this.state.checked,r.click()):this.startX-4<l&&this.previouslyChecked!==this.state.checked&&(this.setState({checked:!0}),this.previouslyChecked=this.state.checked,r.click()),this.activated=!1,this.startX=null,this.moved=!1}}}},{key:"handleFocus",value:function(e){var r=this.props.onFocus;r&&r(e),this.setState({hasFocus:!0})}},{key:"handleBlur",value:function(e){var r=this.props.onBlur;r&&r(e),this.setState({hasFocus:!1})}},{key:"getIcon",value:function(e){var r=this.props.icons;return r?r[e]===void 0?a.defaultProps.icons[e]:r[e]:null}},{key:"render",value:function(){var e=this,r=this.props,l=r.className;r.icons;var m=I(r,["className","icons"]),y=(0,B.default)("react-toggle",{"react-toggle--checked":this.state.checked,"react-toggle--focus":this.state.hasFocus,"react-toggle--disabled":this.props.disabled},l);return i.default.createElement("div",{className:y,onClick:this.handleClick,onTouchStart:this.handleTouchStart,onTouchMove:this.handleTouchMove,onTouchEnd:this.handleTouchEnd},i.default.createElement("div",{className:"react-toggle-track"},i.default.createElement("div",{className:"react-toggle-track-check"},this.getIcon("checked")),i.default.createElement("div",{className:"react-toggle-track-x"},this.getIcon("unchecked"))),i.default.createElement("div",{className:"react-toggle-thumb"}),i.default.createElement("input",P({},m,{ref:function(b){e.input=b},onFocus:this.handleFocus,onBlur:this.handleBlur,className:"react-toggle-screenreader-only",type:"checkbox"})))}}]),a}(g.PureComponent),A=_.default=u;u.displayName="Toggle";u.defaultProps={icons:{checked:i.default.createElement(R.default,null),unchecked:i.default.createElement($.default,null)}};u.propTypes={checked:c.default.bool,disabled:c.default.bool,defaultChecked:c.default.bool,onChange:c.default.func,onFocus:c.default.func,onBlur:c.default.func,className:c.default.string,name:c.default.string,value:c.default.string,id:c.default.string,"aria-labelledby":c.default.string,"aria-label":c.default.string,icons:c.default.oneOfType([c.default.bool,c.default.shape({checked:c.default.node,unchecked:c.default.node})])};export{A as _};
