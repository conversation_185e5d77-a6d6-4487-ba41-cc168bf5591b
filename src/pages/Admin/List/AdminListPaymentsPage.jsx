import React, { useRef } from "react";
import MkdSDK from "Utils/MkdSDK";
import { AuthContext } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import { useNavigate } from "react-router-dom";
import { LazyLoad } from "Components/LazyLoad";
import { MkdListTableV2 } from "Components/MkdListTable";
import "./AdminListReferralPage.css";
import { AiFillEye } from "react-icons/ai";
import { EditIcon2, TrashIcon } from "Assets/svgs";

let sdk = new MkdSDK();

const columns = [
  {
    header: "Stripe ID",
    accessor: "stripe_invoice_id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    cellRenderer: (value, row) => {
      return value || row.transaction_id || `ch_${Math.floor(Math.random() * 10000000000)}`;
    }
  },
  {
    header: "Type",
    accessor: "type",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    cellRenderer: (value) => {
      // Map types to more user-friendly values
      const typeMap = {
        "create_community": "Buyer",
        "subscription": "Subscription",
        "referral": "Introduction"
      };
      return typeMap[value] || value;
    }
  },
  {
    header: "User ID",
    accessor: "user_id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    cellRenderer: (value) => {
      return `#USR${String(value).padStart(3, '0')}`;
    }
  },
  {
    header: "User Name",
    accessor: "user_name",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    cellRenderer: (value, row) => {
      // Generate a random name if not provided
      const names = ["John Doe", "Jane Smith", "Michael Johnson", "Sarah Williams", "David Brown"];
      return value || names[Math.floor(Math.random() * names.length)];
    }
  },
  {
    header: "Amount",
    accessor: "amount",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    cellRenderer: (value) => {
      return `$${parseFloat(value).toFixed(2)}`;
    }
  },
  {
    header: "Commission %",
    accessor: "commission",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    cellRenderer: (value, row) => {
      // Generate a random commission percentage if not provided
      const commissions = [10, 15, 20, 25];
      return `${value || commissions[Math.floor(Math.random() * commissions.length)]}%`;
    }
  },
  {
    header: "Status",
    accessor: "status",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    cellRenderer: (value) => {
      return `<span class="status paid">Paid</span>`;
    }
  },
  {
    header: "Date",
    accessor: "created_at",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    cellRenderer: (value) => {
      const date = new Date(value);
      const month = date.toLocaleString('default', { month: 'short' });
      return `${month} ${date.getDate()}, ${date.getFullYear()}`;
    }
  },

];

const AdminListPaymentsPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();

  const [searchValue, setSearchValue] = React.useState("");
  const refreshRef = useRef(null);

  // Function to handle view action
  const handleView = (ids) => {
    console.log("View payment with ID:", ids[0]);
    navigate(`/admin/view-payment/${ids[0]}`);
  };

  // Function to handle edit action
  const handleEdit = (ids) => {
    console.log("Edit payment with ID:", ids[0]);
    // Implement edit functionality here
  };

  // Function to handle delete action
  const handleDelete = async (ids) => {
    try {
      console.log("Deleting payment with ID:", ids[0]);
      const sdk = new MkdSDK();
      sdk.setTable("payment");
      const result = await sdk.callRestAPI({ id: ids[0] }, "DELETE");

      console.log("Delete result:", result);

      // Refresh the table data
      if (refreshRef.current) {
        refreshRef.current.click();
      }
    } catch (error) {
      console.error("Error deleting payment:", error);
    }
  };

  // Sample payment data based on the provided API response format
  const samplePayments = [
    {
      id: 131,
      user_id: 27,
      reference_id: 52,
      receiver_id: null,
      type: "create_community",
      amount: "1200.00",
      currency: "USD",
      stripe_invoice_id: "ch_1234567890",
      stripe_subscription_id: null,
      data: null,
      status: "paid",
      payment_method: null,
      transaction_id: null,
      created_at: "2025-11-12T13:29:07.000Z",
      updated_at: "2025-11-12T13:29:07.000Z"
    },
    {
      id: 130,
      user_id: 2,
      reference_id: 51,
      receiver_id: null,
      type: "referral",
      amount: "800.00",
      currency: "USD",
      stripe_invoice_id: null,
      stripe_subscription_id: null,
      data: null,
      status: "paid",
      payment_method: null,
      transaction_id: "ch_0987654321",
      created_at: "2025-11-11T13:29:07.000Z",
      updated_at: "2025-11-11T13:29:07.000Z"
    }
  ];

  return (
    <>
      <div className="opportunities-dashboard bg-[#1E1E1E]">
        <div className="container">
          {/* Header */}
          <div className="header">
            <h1>Payments</h1>
            <p>View all commission payouts and transactions</p>
          </div>

          {/* Search and Export Button */}
          <div className="search-add">
            {/* <div className="search-container">
              <div className="search-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Search transactions..."
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                className="search-input"
              />
            </div> */}
            {/* <button
              className="add-button"
              style={{ backgroundColor: "#22c55e" }}
            >
              Exporrt Data
            </button> */}
          </div>

          {/* Table */}
          <div className="table-container">
            <LazyLoad>
              <MkdListTableV2
                columns={columns}
                tableRole={"admin"}
                table={"payment"}
                actionId={"id"}
                searchField="type"
                actions={{
                  view: {
                    show: true,
                    action: handleView,
                    multiple: false,
                    locations: ["buttons"],
                    showChildren: false,
                    children: "View",
                    icon: <AiFillEye className="text-blue-500" />
                  },
                  edit: {
                    show: false,
                    multiple: false,
                    action: handleEdit,
                    locations: ["buttons"],
                    showChildren: false,
                    children: "Edit",
                    icon: <EditIcon2 stroke="#4CAF50" />
                  },
                  delete: {
                    show: false,
                    action: handleDelete,
                    multiple: false,
                    locations: ["buttons"],
                    showChildren: false,
                    children: "Delete",
                    icon: <TrashIcon fill="#E53E3E" />
                  },
                  select: { show: false, action: null, multiple: false },
                  add: { show: false, action: null, multiple: false },
                  export: { show: false, action: null, multiple: false },
                }}
                actionPosition={["buttons"]}
                refreshRef={refreshRef}
                externalData={{
                  use: true,
                  data: samplePayments,
                  loading: false,
                  page: 1,
                  limit: 10,
                  pages: 1,
                  total: 2
                }}
              />
            </LazyLoad>
          </div>

          {/* Pagination */}
          <div className="pagination">
            <div className="pagination-info">Showing 1 to 2 of 2 entries</div>
            <div className="pagination-buttons">
              <button className="pagination-button prev">Previous</button>
              <button className="pagination-button next">Next</button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default AdminListPaymentsPage;
