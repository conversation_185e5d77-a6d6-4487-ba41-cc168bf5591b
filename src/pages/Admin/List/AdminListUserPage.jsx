/*
 * AdminListUserPage - User management interface
 *
 * Note: This file contains some variables that are currently unused but kept for
 * future API integration and functionality. These include:
 * - sdk: MkdSDK instance for API calls
 * - dispatch/globalDispatch: For state management
 * - navigate: For potential navigation needs
 */

import React, { useRef } from "react";
import MkdSDK from "Utils/MkdSDK";
import { AuthContext } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import { useNavigate } from "react-router-dom";
import { LazyLoad } from "Components/LazyLoad";
import { ModalSidebar } from "Components/ModalSidebar";
import { MkdListTableV2 } from "Components/MkdListTable";
import { AdminEditUserPage, AdminAddUserPage } from "Src/routes/LazyLoad";
import "./AdminListUserPage.css";
import { AiFillEye } from "react-icons/ai";
import { EditIcon2, TrashIcon } from "Assets/svgs";

// SDK instance is created but kept for potential future use
// eslint-disable-next-line no-unused-vars
const sdk = new MkdSDK();

// Updated columns to match the screenshot
const columns = [
  {
    header: "Full Name",
    accessor: "data",
    dataField: "first_name", // Extract first_name from data JSON
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
    cellRenderer: (_, row) => {
      // Generate initials for avatar if no image is available
      const firstName = row.data?.first_name || "";
      const lastName = row.data?.last_name || "";
      const initials = `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
      const fullName = `${firstName} ${lastName}`;

      // Random colors for avatars
      const colors = ["#4F46E5", "#22C55E", "#EF4444", "#F59E0B", "#3B82F6"];
      const colorIndex = row.id % colors.length;
      const bgColor = colors[colorIndex];

      return `
        <div class="user-info">
          <div class="user-avatar" style="background-color: ${bgColor}">
            ${initials}
          </div>
          <div>${fullName}</div>
        </div>
      `;
    }
  },
  {
    header: "Email",
    accessor: "email",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
    selected_column: true,
  },
  {
    header: "Role",
    accessor: "role_id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: true,
    mappings: {
      "admin": "Admin",
      "member": "Member",
      "user": "User",
      "editor": "Editor",
      "viewer": "Viewer"
    },
    selected_column: true,
    cellRenderer: (value) => {
      // Format the role value for display
      if (!value) return "N/A";

      // Capitalize the first letter of the role
      const formattedRole = typeof value === 'string'
        ? value.charAt(0).toUpperCase() + value.slice(1)
        : value;

      return `${formattedRole}`;
    }
  },
  {
    header: "Status",
    accessor: "verify",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: true,
    mappings: { 0: "Inactive", 1: "Active" },
    selected_column: true,
    cellRenderer: (value) => {
      // Map status values to appropriate styling
      const isActive = value === 1 || value === "1";
      return isActive
        ? `<span class="status-badge active">Active</span>`
        : `<span class="status-badge inactive">Inactive</span>`;
    }
  },

];

const AdminUserListPage = () => {
  // These variables are kept for potential future use and API integration
  // eslint-disable-next-line no-unused-vars
  const { dispatch } = React.useContext(AuthContext);
  // eslint-disable-next-line no-unused-vars
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  // eslint-disable-next-line no-unused-vars
  const navigate = useNavigate();

  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const [showEditSidebar, setShowEditSidebar] = React.useState(false);
  const [activeEditId, setActiveEditId] = React.useState();
  const [searchValue, setSearchValue] = React.useState("");
  const refreshRef = useRef(null);
  // Sample stats for the UI
  const [stats] = React.useState({
    total: 125,
    active: 98,
    inactive: 27
  });

  // State to manage table data and loading
  const [tableData, setTableData] = React.useState({
    data: [
      { id: 53, email: "<EMAIL>", data: { first_name: "Kevin", last_name: "Pettie" }, role_id: "member", verify: 1, login_type: 1, status: 1 },
      { id: 52, email: "<EMAIL>", data: { first_name: "Tyrone", last_name: "" }, role_id: "member", verify: 1, login_type: 1, status: 1 },
      { id: 51, email: "fyghjnkn@jsnkam", data: { first_name: "Zainee", last_name: "" }, role_id: "member", verify: 1, login_type: 1, status: 1 },
      { id: 50, email: "<EMAIL>", data: { first_name: "Idera", last_name: "" }, role_id: "member", verify: 1, login_type: 1, status: 1 },
      { id: 49, email: "<EMAIL>", data: { first_name: "Umer", last_name: "" }, role_id: "admin", verify: 1, login_type: 1, status: 1 },
      { id: 48, email: "<EMAIL>", data: { first_name: "-", last_name: "" }, role_id: "user", verify: 1, login_type: 1, status: 1 },
      { id: 47, email: "<EMAIL>", data: { first_name: "not tester", last_name: "" }, role_id: "member", verify: 0, login_type: 1, status: 0 },
    ],
    loading: false,
    page: 1,
    limit: 10,
    pages: 13,
    total: 125
  });

  const onToggleModal = async (modal, toggle, ids = []) => {
    switch (modal) {
      case "add":
        setShowAddSidebar(toggle);
        break;
      case "edit":
        setShowEditSidebar(toggle);
        setActiveEditId(ids[0]);
        break;
      case "delete":
        await handleDelete(ids);
        break;
      default:
        break;
    }
  };

  // Function to handle delete action
  const handleDelete = async (ids) => {
    try {
      console.log("Deleting user with ID:", ids[0]);

      // Set loading state to true
      setTableData(prev => ({...prev, loading: true}));

      const sdk = new MkdSDK();
      sdk.setTable("user");
      const result = await sdk.callRestAPI({ id: ids[0] }, "DELETE");

      console.log("Delete result:", result);

      if (!result?.error) {
        // Update the table data by removing the deleted item
        setTableData(prev => ({
          ...prev,
          data: prev.data.filter(item => item.id !== ids[0]),
          loading: false
        }));

        // No need to click refresh button as we've already updated the data
      } else {
        // If there was an error, just set loading to false
        setTableData(prev => ({...prev, loading: false}));
      }
    } catch (error) {
      console.error("Error deleting user:", error);
      // Make sure to set loading to false in case of error
      setTableData(prev => ({...prev, loading: false}));
    }
  };

  return (
    <>
      <div className="users-dashboard bg-[#1E1E1E]">
        <div className="container">
          {/* Header */}
          <div className="header">
            <h1>Users Dashboard</h1>
            <p>Manage and review all user accounts</p>
          </div>

          {/* Search and Add Button */}
          <div className="search-add">
            {/* <div className="search-container">
              <div className="search-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Search users..."
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                className="search-input"
              />
            </div> */}
            <button
              onClick={() => onToggleModal("add", true)}
              className="add-user-btn ml-auto"
            >
              <span>+</span> Add New User
            </button>
          </div>


          {/* Table */}
          <div className="table-container">
            <LazyLoad>
              <MkdListTableV2
                pageUser={true}
                columns={columns}
                tableRole={"admin"}
                table={"user"}
                actionId={"id"}
                searchField="email"
                actions={{
                  view: {
                    show: true,
                    action: (ids) => {
                      console.log("View user with ID:", ids[0]);
                      navigate(`/admin/view-user/${ids[0]}`);
                    },
                    multiple: false,
                    locations: ["buttons"],
                    showChildren: false,
                    children: "View",
                    icon: <AiFillEye className="text-blue-500" />
                  },
                  edit: {
                    show: true,
                    multiple: false,
                    action: (ids) => onToggleModal("edit", true, ids),
                    locations: ["buttons"],
                    showChildren: false,
                    children: "Edit",
                    icon: <EditIcon2 stroke="#4CAF50" />
                  },
                  delete: {
                    show: true,
                    action: handleDelete,
                    multiple: false,
                    locations: ["buttons"],
                    showChildren: false,
                    children: "Delete",
                    icon: <TrashIcon fill="#E53E3E" />
                  },
                  select: { show: false, action: null, multiple: false },
                  add: {
                    show: true,
                    action: () => onToggleModal("add", true),
                    multiple: false,
                    children: "Add New",
                    showChildren: true,
                  },
                  export: { show: false, action: null, multiple: true },
                }}
                actionPosition={["buttons"]}
                refreshRef={refreshRef}
                allowSortColumns={false}
                externalData={{
                  use: true,
                  data: tableData.data,
                  loading: tableData.loading,
                  page: tableData.page,
                  limit: tableData.limit,
                  pages: tableData.pages,
                  total: tableData.total,
                  fetch: (page, limit, filter) => {
                    // This function is called when the table needs to refresh
                    console.log("Fetch called with page:", page, "limit:", limit, "filter:", filter);
                    // We're using static data, so just set loading to false after a short delay
                    // In a real app, you would make an API call here
                    setTimeout(() => {
                      setTableData(prev => ({...prev, loading: false}));
                    }, 500);
                  },
                  search: (search, _columns, _searchFilter, query) => {
                    // This function is called when the search is triggered
                    console.log("Search called with:", search, query);
                    // We're using static data, so just set loading to false after a short delay
                    setTimeout(() => {
                      setTableData(prev => ({...prev, loading: false}));
                    }, 500);
                  }
                }}
              />
            </LazyLoad>
          </div>

          {/* Pagination */}
          <div className="pagination">
            <div className="pagination-info">Showing 1 to 7 of 125 entries</div>
            <div className="pagination-buttons">
              <button className="pagination-button prev">Previous</button>
              <button className="pagination-button next">Next</button>
            </div>
          </div>
        </div>
      </div>

      <LazyLoad>
        <ModalSidebar
          isModalActive={showAddSidebar}
          closeModalFn={() => setShowAddSidebar(false)}
        >
          <AdminAddUserPage setSidebar={setShowAddSidebar} />
        </ModalSidebar>
      </LazyLoad>

      {showEditSidebar && (
        <LazyLoad>
          <ModalSidebar
            isModalActive={showEditSidebar}
            closeModalFn={() => setShowEditSidebar(false)}
          >
            <AdminEditUserPage
              activeId={activeEditId}
              setSidebar={setShowEditSidebar}
            />
          </ModalSidebar>
        </LazyLoad>
      )}
    </>
  );
};

export default AdminUserListPage;
