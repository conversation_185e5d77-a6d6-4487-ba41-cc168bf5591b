
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate, useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { InteractiveButton } from "Components/InteractiveButton";
import { SkeletonLoader } from "Components/Skeleton";
import { Toast } from "Components/Toast";

const EmojiIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM10 18C5.58 18 2 14.42 2 10C2 5.58 5.58 2 10 2C14.42 2 18 5.58 18 10C18 14.42 14.42 18 10 18ZM7 6C7 6.82843 6.32843 7.5 5.5 7.5C4.67157 7.5 4 6.82843 4 6C4 5.17157 4.67157 4.5 5.5 4.5C6.32843 4.5 7 5.17157 7 6ZM16 6C16 6.82843 15.3284 7.5 14.5 7.5C13.6716 7.5 13 6.82843 13 6C13 5.17157 13.6716 4.5 14.5 4.5C15.3284 4.5 16 5.17157 16 6ZM10 15.5C12.33 15.5 14.32 14.05 15.12 12H4.88C5.68 14.05 7.67 15.5 10 15.5Z" fill="#B5B5B5"/>
  </svg>
);

const AttachmentIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M17.5 9.375L11.25 15.625C10.2083 16.6667 8.89583 17.1875 7.3125 17.1875C5.72917 17.1875 4.41667 16.6667 3.375 15.625C2.33333 14.5833 1.8125 13.2708 1.8125 11.6875C1.8125 10.1042 2.33333 8.79167 3.375 7.75L10.9375 0.1875C11.6458 -0.520833 12.5104 -0.875 13.5312 -0.875C14.5521 -0.875 15.4167 -0.520833 16.125 0.1875C16.8333 0.895833 17.1875 1.76042 17.1875 2.78125C17.1875 3.80208 16.8333 4.66667 16.125 5.375L8.5625 12.9375C8.20833 13.2917 7.78125 13.4688 7.28125 13.4688C6.78125 13.4688 6.35417 13.2917 6 12.9375C5.64583 12.5833 5.46875 12.1562 5.46875 11.6562C5.46875 11.1562 5.64583 10.7292 6 10.375L12.8125 3.5625" stroke="#B5B5B5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>
);

const EditAdminReferralPage = (props) => {
  const { dispatch } = React.useContext(AuthContext);
  const schema = yup
    .object({
      title: yup.string().required(),
      type: yup.string().required(),
      industry: yup.string().required(),
      description: yup.string().required(),
      deal_size: yup.string().required(),
      referral_fee: yup.string().required(),
      payment_method: yup.string().required(),
      referral_type: yup.string().required(),
      community: yup.string(),
      direct_person: yup.string(),
      additional_notes: yup.string(),
      expiration_date: yup.string()
    })
    .required();

  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [isSubmitLoading, setIsSubmitLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const params = useParams();

  React.useEffect(function() {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "referral",
      },
    });

    (async function() {
      try {
        setLoading(true);
        const activeId = props.activeId || Number(params?.id);
        const sdk = new MkdSDK();
        sdk.setTable("referral");
        const result = await sdk.callRestAPI(
          { id: activeId },
          "GET"
        );

        if (!result.error) {
          // Map API data to form fields
          setValue('title', result.model.job_title || result.model.title);
          setValue('description', result.model.description);
          setValue('requirements', result.model.requirements || result.model.description);
          setValue('industry', result.model.industry_id?.toString());
          setValue('type', result.model.type);
          setValue('know_client', result.model.know_client === "yes" ? "yes" : "no");
          setValue('deal_size', result.model.deal_size);
          setValue('referral_fee', result.model.referral_fee || result.model.pay);
          setValue('payment_method', result.model.payment_method);
          setValue('referral_type', result.model.referral_type?.includes("open") ? "open" : "direct");
          setValue('community', result.model.community_id?.toString());
          setValue('direct_person', result.model.referred_to_id?.toString() || result.model.user_id?.toString());
          setValue('additional_notes', result.model.additional_notes || result.model.client_details);

          // Format expiration date if it exists
          if (result.model.expiration_date) {
            // Extract just the date part if it's in the format YYYY-MM-DD 00:00:00
            const datePart = result.model.expiration_date.split(' ')[0];
            setValue('expiration_date', datePart);
          }
        }
        setLoading(false);
      } catch (error) {
        setLoading(false);
        console.log("Error", error);
        tokenExpireError(dispatch, error.message);
      }
    })();
  }, []);

  const onSubmit = async (_data) => {
    setIsSubmitLoading(true);
    setError("");

    try {
      const sdk = new MkdSDK();
      sdk.setTable("referral");

      // Format date as YYYY-MM-DD 00:00:00 for updated_at
      const currentDate = new Date().toISOString().split('T')[0] + ' 00:00:00';

      // Map the form data to the API expected format based on the database schema
      const apiData = {
        id: props.activeId || Number(params?.id),
        user_id: _data.direct_person ? parseInt(_data.direct_person, 10) : 1,
        reposted_from: 0,
        job_title: _data.title,
        title: _data.title, // Include both title and job_title with the same data
        description: _data.description,
        description_image: "",
        pay: _data.referral_fee, // Commission percent
        referred_to_id: _data.direct_person ? parseInt(_data.direct_person, 10) : 0,
        industry_id: _data.industry ? parseInt(_data.industry, 10) : null,
        is_active: 1, // tinyint(1) with default 1
        type: _data.type,
        client_details: _data.additional_notes || "",
        completed_by: null,
        status: "active", // Default status
        expiration_date: _data.expiration_date
          ? new Date(_data.expiration_date).toISOString().split('T')[0] + ' 00:00:00'
          : null, // Format: YYYY-MM-DD 00:00:00
        updated_at: currentDate,
        deal_size: _data.deal_size,
        payment_method: _data.payment_method,
        referral_type: _data.referral_type === "open" ? "open referral" : "direct referral",
        additional_notes: _data.additional_notes || null,
        community_id: _data.community ? parseInt(_data.community, 10) : null
      };

      const result = await sdk.callRestAPI(
        apiData,
        "PUT"
      );

      if (!result.error) {
        showToast(globalDispatch, "Opportunity Updated Successfully");

        // First, dispatch the REFRESH_DATA action to trigger a refresh of the table
        console.log("Dispatching REFRESH_DATA action to refresh the table");
        globalDispatch({
          type: "REFRESH_DATA",
          payload: {
            refreshData: true,
            timestamp: new Date().getTime() // Add timestamp to ensure uniqueness
          },
        });

        // Then close the sidebar modal with a slight delay to ensure the refresh is triggered
        setTimeout(() => {
          if (props.setSidebar) {
            props.setSidebar(false);
          }
        }, 100);
      } else {
        setError(result.message || "Failed to update opportunity");
      }
      setIsSubmitLoading(false);
    } catch (error) {
      setIsSubmitLoading(false);
      console.log("Error", error);
      setError(error.message || "Failed to update opportunity");
      tokenExpireError(dispatch, error.message);
    }
  };



  return (
    <div className="w-full rounded-lg bg-[#1e1e1e] p-6">
      <h2 className="mb-6 text-xl font-bold text-[#eaeaea]">Edit Opportunity</h2>
      {loading ? (
        <SkeletonLoader />
      ) : (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4  w-[60vw] mx-auto">
          <div>
            <label className="block text-sm text-[#b5b5b5]">Title</label>
            <input
              type="text"
              {...register("title")}
              placeholder="Enter referral title"
              className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
            />
            {errors.title && <p className="mt-1 text-xs text-red-500">{errors.title.message}</p>}
          </div>

          <div>
            <label className="block text-sm text-[#b5b5b5]">Type of Opportunity</label>
            <select
              {...register("type")}
              className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
            >
              <option value="">Select type</option>
              <option value="looking_for_service">Looking for Service</option>
              <option value="looking_for_product">Looking for Product</option>
              <option value="looking_for_buyer">Looking for Buyer</option>
              <option value="looking_for_investor">Looking for Investor</option>
              <option value="looking_for_partner">Looking for Partner</option>
            </select>
            {errors.type && <p className="mt-1 text-xs text-red-500">{errors.type.message}</p>}
          </div>

          <div>
            <label className="block text-sm text-[#b5b5b5]">Industry</label>
            <select
              {...register("industry")}
              className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
            >
              <option value="">Select industry</option>
              <option value="1">Agriculture & Farming</option>
              <option value="2">Construction</option>
              <option value="3">Education & Training</option>
              <option value="4">Energy & Utilities</option>
              <option value="5">Financial Services</option>
              <option value="6">Government & Public Sector</option>
              <option value="7">Healthcare & Life Sciences</option>
              <option value="8">Hospitality & Tourism</option>
              <option value="9">Information Technology & Software</option>
              <option value="10">Legal Services</option>
              <option value="11">Logistics & Transportation</option>
              <option value="12">Manufacturing</option>
              <option value="13">Marketing & Advertising</option>
              <option value="14">Media & Entertainment</option>
              <option value="15">Non-Profit & Charities</option>
              <option value="16">Professional Services (e.g., consulting, accounting)</option>
              <option value="17">Real Estate & Property Management</option>
              <option value="18">Retail & E-Commerce</option>
              <option value="19">Telecommunications</option>
              <option value="20">Wholesale & Distribution</option>
            </select>
            {errors.industry && <p className="mt-1 text-xs text-red-500">{errors.industry.message}</p>}
          </div>

          <div>
            <label className="block text-sm text-[#b5b5b5]">Description</label>
            <div className="relative">
              <textarea
                {...register("description")}
                placeholder="Write your text here..."
                rows={4}
                className="mt-1 w-full rounded-lg border border-[#363636] bg-[#242424] p-4 text-[#eaeaea]"
              />
            </div>
            {errors.description && <p className="mt-1 text-xs text-red-500">{errors.description.message}</p>}
          </div>



          <div>
            <label className="block text-sm text-[#b5b5b5]">Estimated Deal Size</label>
            <input
              type="text"
              {...register("deal_size")}
              placeholder="e.g. $500"
              className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
            />
            {errors.deal_size && <p className="mt-1 text-xs text-red-500">{errors.deal_size.message}</p>}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm text-[#b5b5b5]">Commission Percentage (%)</label>
              <input
                type="text"
                {...register("referral_fee")}
                placeholder="Enter percentage (e.g. 10, 15)"
                className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
              />
              {errors.referral_fee && <p className="mt-1 text-xs text-red-500">{errors.referral_fee.message}</p>}
            </div>
            <div>
              <label className="block text-sm text-[#b5b5b5]">Payment Method</label>
              <select
                {...register("payment_method")}
                className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
              >
                <option value="">Select method</option>
                <option value="bank">Bank Transfer</option>
                <option value="card">Bank card</option>
              </select>
              {errors.payment_method && <p className="mt-1 text-xs text-red-500">{errors.payment_method.message}</p>}
            </div>
          </div>

          <div>
            <label className="block text-sm text-[#b5b5b5]">Referral Type</label>
            <select
              {...register("referral_type")}
              className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
            >
              <option value="">Select type</option>
              <option value="open">Open Referral</option>
              <option value="direct">Direct Referral</option>
            </select>
            {errors.referral_type && <p className="mt-1 text-xs text-red-500">{errors.referral_type.message}</p>}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm text-[#b5b5b5]">Select Community</label>
              <select
                {...register("community")}
                className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
              >
                <option value="">Select community</option>
                <option value="1">Community 1</option>
                <option value="2">Community 2</option>
                <option value="3">Community 3</option>
              </select>
              {errors.community && <p className="mt-1 text-xs text-red-500">{errors.community.message}</p>}
            </div>
            <div>
              <label className="block text-sm text-[#b5b5b5]">Select Direct Person</label>
              <select
                {...register("direct_person")}
                className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
              >
                <option value="">Select person</option>
                <option value="1">User 1</option>
                <option value="2">User 2</option>
                <option value="3">User 3</option>
              </select>
              {errors.direct_person && <p className="mt-1 text-xs text-red-500">{errors.direct_person.message}</p>}
            </div>
          </div>

          <div>
            <label className="block text-sm text-[#b5b5b5]">Additional Notes</label>
            <textarea
              {...register("additional_notes")}
              placeholder="Write any additional notes here..."
              rows={4}
              className="mt-1 w-full rounded-lg border border-[#363636] bg-[#242424] p-4 text-[#eaeaea]"
            />
            {errors.additional_notes && <p className="mt-1 text-xs text-red-500">{errors.additional_notes.message}</p>}
          </div>

          <div>
            <label className="block text-sm text-[#b5b5b5]">Expiration Date</label>
            <input
              type="date"
              {...register("expiration_date")}
              className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
            />
            <p className="mt-1 text-xs text-[#b5b5b5]">If not set, the existing expiration date will be kept</p>
            {errors.expiration_date && <p className="mt-1 text-xs text-red-500">{errors.expiration_date.message}</p>}
          </div>

          <div className="flex justify-end gap-4">
            <button
              type="button"
              onClick={() => {
                // Reset form instead of closing sidebar
                window.location.reload();
              }}
              className="rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#eaeaea]"
            >
              Close
            </button>
            <InteractiveButton
              type="submit"
              loading={isSubmitLoading}
              disabled={isSubmitLoading}
              className="rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
            >
              Update Opportunity
            </InteractiveButton>
          </div>
        </form>
      )}

      {error && <Toast message={error} />}
    </div>
  );
};

export default EditAdminReferralPage;