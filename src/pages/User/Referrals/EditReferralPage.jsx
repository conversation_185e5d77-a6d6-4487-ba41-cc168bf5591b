import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { Toast } from "Components/Toast";
import { SkeletonLoader } from "Components/Skeleton";
import { useContext } from "react";
import { GlobalContext, showToast } from "Context/Global";

const EditReferralPage = () => {
  console.log("%cEditReferralPage Rendering", "color: #00ff00; font-weight: bold");
  const { id } = useParams();
  console.log("%cReferral ID:", "color: #00ff00; font-weight: bold", id);

  const navigate = useNavigate();
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [joinedCommunities, setJoinedCommunities] = useState([]);
  const [selectedType, setSelectedType] = useState('');
  const [selectedCommunity, setSelectedCommunity] = useState('');
  const [users, setUsers] = useState([]);
  const [fetchingUsers, setFetchingUsers] = useState(false);
  const [originalData, setOriginalData] = useState(null);
  const [uploadingAttachment, setUploadingAttachment] = useState(false);
  const [attachmentPreview, setAttachmentPreview] = useState("");
  const [attachmentName, setAttachmentName] = useState("");
  const [industries, setIndustries] = useState([]);
  const [formData, setFormData] = useState({
    title: "",
    type: "",
    industry: "",
    description: "",
    know_client: "",
    deal_size: "",
    referral_fee: "",
    payment_method: "",
    referral_type: "",
    community: "",
    direct_person: "",
    additional_notes: "",
    description_image: "",
    requirements: "",
    expiration_date: ""
  });

  const loadIndustries = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI("/v1/api/dealmaker/industries", {}, "GET");

      if (!response.error && response.data) {
        setIndustries(response.data);
        return response.data;
      } else {
        setError(response.message || "Failed to load industries");
        return [];
      }
    } catch (err) {
      setError(err.message || "Failed to load industries");
      return [];
    }
  };

  useEffect(() => {
    const initializeData = async () => {
      console.log("%cInitializing Data", "color: #ff00ff; font-weight: bold");
      if (!id) {
        console.log("No ID provided");
        return;
      }

      try {
        console.log("%cFetching Referral Data", "color: #ff00ff; font-weight: bold");
        const sdk = new MkdSDK();

        // Load industries first
        const industriesData = await loadIndustries();

        const response = await sdk.GetReferralDetail(id);
        console.log("%cAPI Response:", "color: #ff00ff; font-weight: bold", response);

        if (!response.error && response.model) {
          const referral = response.model;
          console.log("%cReferral Model:", "color: #ff00ff; font-weight: bold", referral);

          // Update form data
          const industryName = referral.industry_name?.value || "";
          // Convert industry name to ID for the dropdown
          let industryId = "";
          if (industryName && industriesData.length > 0) {
            const industry = industriesData.find(option =>
              option.name === industryName
            );
            industryId = industry ? industry.id : "";
          }

          const newFormData = {
            title: referral.title?.value || "",
            type: referral.type?.value || "",
            industry: industryId || "",
            description: referral.description?.value || "",
            know_client: referral.know_client?.value || "",
            deal_size: referral.deal_size?.value || "",
            referral_fee: referral.referral_fee?.value || "",
            payment_method: referral.payment_method?.value || "",
            referral_type: referral.referral_type?.value || "",
            community: referral.community?.id?.value || "",
            direct_person: referral.direct_person?.id?.value || "",
            additional_notes: referral.additional_notes?.value || "",
            description_image: referral.description_image?.value || referral.description_image || "",
            requirements: referral.requirements?.value || "",
            expiration_date: referral.expiration_date?.value || ""
          };

          console.log("%cSetting Form Data:", "color: #ff00ff; font-weight: bold", newFormData);
          setFormData(newFormData);
          setOriginalData(newFormData);

          // Handle image preview
          const imageUrl = referral.description_image?.value || referral.description_image;
          if (imageUrl && typeof imageUrl === 'string') {
            console.log("%cSetting Image Preview:", "color: #ff00ff; font-weight: bold", imageUrl);
            setAttachmentPreview(imageUrl);
            setAttachmentName(imageUrl.split('/').pop());
          }

          // Load communities
          await loadCommunities();

          // Load users if needed
          if (referral.referral_type?.value === "direct") {
            await loadUsers();
          }
        }
      } catch (err) {
        console.error("%cError Loading Data:", "color: #ff0000; font-weight: bold", err);
        setError(err.message || "Failed to load referral data");
      } finally {
        setLoading(false);
      }
    };

    initializeData();
  }, [id]);

  // Add default community if none found
  useEffect(() => {
    if (joinedCommunities.length === 0) {
      setJoinedCommunities([{
        id: { value: 1 },
        title: { value: "Rain Maker LLC" }
      }]);
    }
  }, [joinedCommunities]);

  const loadCommunities = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.GetJoinedCommunities();

      if (!response.error) {
        setJoinedCommunities(response.list || []);
        // Add default community if none found
        if (response.list?.length === 0) {
          setJoinedCommunities([{
            id: { value: 1 },
            title: { value: "Rain Maker LLC" }
          }]);
        }
      }
    } catch (err) {
      console.error("Failed to load communities:", err);
    }
  };

  const fetchUsers = async (communityId) => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.GetCommunityUsers(communityId);
      if (!response.error) {
        setUsers(response.list || []);
      }
    } catch (err) {
      console.error("Failed to fetch users:", err);
      setError(err.message || "Failed to fetch users");
    }
  };


  const handleCommunityChange = (e) => {
    const communityId = e.target.value;
    setSelectedCommunity(communityId);
    formData.community = communityId;
    if (formData.referral_type === 'direct referral') {
      setFetchingUsers(true);
      fetchUsers(communityId);
      setFetchingUsers(false);
    }
  };


  const loadUsers = async () => {
    setFetchingUsers(true);
    try {
      const sdk = new MkdSDK();
      const response = await sdk.GetCommunityMembers(formData.community);

      if (!response.error) {
        setUsers(response.list || []);
      }
    } catch (err) {
      console.error("Failed to load users:", err);
    } finally {
      setFetchingUsers(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // If changing referral type or community, we need to update related fields
    if (name === "referral_type" && value === "direct") {
      if (formData.community) {
        loadUsers();
      }
    } else if (name === "community" && formData.referral_type === "direct") {
      loadUsers();
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError("");

    try {
      // Validate required fields
      if (!formData.title) {
        setError("Title is required");
        setSaving(false);
        return;
      }

      // Validate expiration date if provided
      if (formData.expiration_date) {
        const selectedDate = new Date(formData.expiration_date);
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Reset time part for accurate date comparison

        if (selectedDate < today) {
          setError("Expiration date cannot be in the past");
          setSaving(false);
          return;
        }
      }

      const sdk = new MkdSDK();

      // Prepare update data
      const updateData = {};

      // Process each field in formData
      Object.entries(formData).forEach(([key, value]) => {
        // Special handling for description_image
        if (key === 'description_image') {
          const originalImage = originalData[key]?.value || originalData[key];
          if (value !== originalImage) {
            updateData[key] = value;
          }
        } else {
          // For other fields, compare with original data
          const originalValue = originalData[key]?.value || originalData[key];

          // Special handling for industry field - ensure proper comparison
          if (key === 'industry') {
            // Convert both to numbers for comparison if they're valid numbers
            const currentNum = parseInt(value, 10);
            const originalNum = parseInt(originalValue, 10);

            // If both are valid numbers, compare as numbers
            if (!isNaN(currentNum) && !isNaN(originalNum)) {
              if (currentNum !== originalNum) {
                updateData[key] = value;
              }
            } else {
              // Otherwise compare as strings
              const currentValueStr = String(value || '');
              const originalValueStr = String(originalValue || '');
              if (currentValueStr !== originalValueStr) {
                updateData[key] = value;
              }
            }
          } else {
            // Convert both values to strings for comparison to handle number vs string issues
            const currentValueStr = String(value || '');
            const originalValueStr = String(originalValue || '');

            if (currentValueStr !== originalValueStr) {
              updateData[key] = value;
            }
          }
        }
      });

      console.log("Original Data:", originalData);
      console.log("Form Data:", formData);
      console.log("Update Data:", updateData);

      // Debug industry field specifically
      console.log("Industry Debug:");
      console.log("- Original industry:", originalData.industry);
      console.log("- Current industry:", formData.industry);
      console.log("- Industry in updateData:", updateData.industry);

      // If no changes detected, but user is submitting, check if industry has a value
      // This is a fallback in case the change detection missed the industry change
      if (Object.keys(updateData).length === 0) {
        // If industry field has a value and it's different from empty, include it
        if (formData.industry && formData.industry !== '' && formData.industry !== originalData.industry) {
          updateData.industry = formData.industry;
          console.log("Fallback: Added industry to updateData:", formData.industry);
        }

        // If still no changes after fallback, return
        if (Object.keys(updateData).length === 0) {
          navigate("/member/referrals", { state: { activeTab: 'my-referrals' } });
          return;
        }
      }

      // Convert industry ID to industry_id for API if industry was changed
      console.log("Checking if updateData.industry exists:", updateData.industry);
      console.log("updateData before conversion:", JSON.stringify(updateData));

      if (updateData.industry) {
        updateData.industry_id = parseInt(updateData.industry, 10);
        delete updateData.industry; // Remove the industry field since we're sending industry_id
        console.log("Converted industry to industry_id:", updateData.industry_id);
        console.log("Final updateData after conversion:", JSON.stringify(updateData));
      } else {
        console.log("No industry field found in updateData");
      }

      console.log("Final updateData before API call:", updateData);

      // Send update request
      const response = await sdk.UpdateReferral(id, updateData);

      if (!response.error) {
        // Success case
        // Force refresh the data to get the latest image
        const refreshResponse = await sdk.GetReferralDetail(id);
        if (!refreshResponse.error && refreshResponse.model) {
          const referral = refreshResponse.model;

          // Update form data with fresh data
          const refreshIndustryName = referral.industry_name?.value || "";
          // Convert industry name to ID for the dropdown
          let refreshIndustryId = "";
          if (refreshIndustryName && industries.length > 0) {
            const industry = industries.find(option =>
              option.name === refreshIndustryName
            );
            refreshIndustryId = industry ? industry.id : "";
          }

          const newFormData = {
            title: referral.title?.value || "",
            type: referral.type?.value || "",
            industry: refreshIndustryId || "",
            description: referral.description?.value || "",
            know_client: referral.know_client?.value || "",
            deal_size: referral.deal_size?.value || "",
            referral_fee: referral.referral_fee?.value || "",
            payment_method: referral.payment_method?.value || "",
            referral_type: referral.referral_type?.value || "",
            community: referral.community?.id?.value || "",
            direct_person: referral.direct_person?.id?.value || "",
            additional_notes: referral.additional_notes?.value || "",
            description_image: referral.description_image?.value || referral.description_image || "",
            requirements: referral.requirements?.value || "",
            expiration_date: referral.expiration_date?.value || ""
          };

          setFormData(newFormData);
          setOriginalData(newFormData);

          // Update image preview with fresh data
          const imageUrl = referral.description_image?.value || referral.description_image;
          if (imageUrl && typeof imageUrl === 'string') {
            setAttachmentPreview(imageUrl);
            setAttachmentName(imageUrl.split('/').pop());
          }
        }

        showToast(globalDispatch, "Referral updated successfully!", 5000, "success");
        navigate("/member/referrals", { state: { activeTab: 'my-referrals' } });
      } else {
        // Error case
        setError(response.message);
        showToast(globalDispatch, "Failed to update referral", 5000, "error");
      }
    } catch (err) {
      setError(err.message || "Failed to update referral");
      showToast(globalDispatch, "Failed to update referral", 5000, "error");
    } finally {
      setSaving(false);
    }
  };

  const handleAttachmentUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    try {
      setUploadingAttachment(true);
      const sdk = new MkdSDK();
      const formData = new FormData();
      formData.append("file", file);

      const result = await sdk.uploadImage(formData);
      if (result.url) {
        setFormData(prev => ({ ...prev, description_image: result.url }));
        setAttachmentPreview(URL.createObjectURL(file));
        setAttachmentName(file.name);
      }
    } catch (err) {
      console.error("Error uploading attachment:", err);
      setError("Failed to upload attachment");
    } finally {
      setUploadingAttachment(false);
    }
  };

  return (
    <div style={{
      width:"600px",
      margin:"20px auto",
    }} className="min-h-screen bg-[#161616] p-6">
      <div className="mx-auto max-w-3xl">
        <h2 className="mb-6 text-xl font-semibold text-[#eaeaea]">Edit Referral</h2>

        <div className="bg-[#161616] rounded-xl p-6 mb-8">
          {loading ? (
            <div className="space-y-4">
              <SkeletonLoader height={10} width="30%" />
              <SkeletonLoader height={40} />
              <SkeletonLoader height={10} width="30%" />
              <SkeletonLoader height={40} />
              <SkeletonLoader height={10} width="30%" />
              <SkeletonLoader height={120} />
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-5">
              <div>
                <label className="mb-2 block text-sm text-[#b5b5b5]">Title *</label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  placeholder="Enter referral title"
                  className="h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] placeholder-[#666]"
                />
              </div>

              <div>
                <label className="mb-2 block text-sm text-[#b5b5b5]">Type of Opportunity</label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value }))}
                  className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]"
                >
                  <option value="">Select type</option>
                  <option value="looking_for_service">Looking for Service</option>
                  <option value="looking_for_product">Looking for Product</option>
                  <option value="looking_for_buyer">Looking for Buyer</option>
                  <option value="looking_for_investor">Looking for Investor</option>
                  <option value="looking_for_partner">Looking for Partner</option>
                </select>
              </div>

              <div>
                <label className="mb-2 block text-sm text-[#b5b5b5]">Industry</label>
                <select
                  value={formData.industry}
                  onChange={(e) => setFormData(prev => ({ ...prev, industry: e.target.value }))}
                  className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]"
                >
                  <option value="">Select industry</option>
                  {industries.map((industry) => (
                    <option key={industry.id} value={industry.id}>
                      {industry.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="mb-2 block text-sm text-[#b5b5b5]">Description</label>
                <div className="relative">
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    placeholder="Provide details about the opportunity"
                    rows={4}
                    className="w-full rounded-lg border border-[#363636] bg-[#242424] p-4 text-[#eaeaea] placeholder-[#666]"
                  />
                  <div className="absolute bottom-4 right-4 flex items-center gap-2">
                    <div className="relative">
                      <input
                        type="file"
                        onChange={handleAttachmentUpload}
                        className="hidden"
                        id="attachment-input"
                        accept="image/*,.pdf,.doc,.docx"
                        disabled={uploadingAttachment}
                      />
                      <button
                        type="button"
                        onClick={() => document.getElementById('attachment-input').click()}
                        className={`text-[#b5b5b5] hover:text-[#eaeaea] ${uploadingAttachment ? 'opacity-50 cursor-not-allowed' : ''}`}
                        disabled={uploadingAttachment}
                      >
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M17.5 9.375L11.25 15.625C10.2083 16.6667 8.89583 17.1875 7.3125 17.1875C5.72917 17.1875 4.41667 16.6667 3.375 15.625C2.33333 14.5833 1.8125 13.2708 1.8125 11.6875C1.8125 10.1042 2.33333 8.79167 3.375 7.75L10.9375 0.1875C11.6458 -0.520833 12.5104 -0.875 13.5312 -0.875C14.5521 -0.875 15.4167 -0.520833 16.125 0.1875C16.8333 0.895833 17.1875 1.76042 17.1875 2.78125C17.1875 3.80208 16.8333 4.66667 16.125 5.375L8.5625 12.9375C8.20833 13.2917 7.78125 13.4688 7.28125 13.4688C6.78125 13.4688 6.35417 13.2917 6 12.9375C5.64583 12.5833 5.46875 12.1562 5.46875 11.6562C5.46875 11.1562 5.64583 10.7292 6 10.375L12.8125 3.5625" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </button>
                      {uploadingAttachment && (
                        <div className="absolute -top-1 -right-1 h-2 w-2">
                          <div className="animate-ping absolute h-full w-full rounded-full bg-[#7dd87d] opacity-75"></div>
                          <div className="rounded-full h-full w-full bg-[#7dd87d]"></div>
                        </div>
                      )}
                    </div>
                    <button
                      type="button"
                      className="text-[#b5b5b5] hover:text-[#eaeaea]"
                      onClick={() => {/* Emoji functionality will be added later */}}
                    >
                      {/* <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM10 18C5.58 18 2 14.42 2 10C2 5.58 5.58 2 10 2C14.42 2 18 5.58 18 10C18 14.42 14.42 18 10 18ZM7 6C7 6.82843 6.32843 7.5 5.5 7.5C4.67157 7.5 4 6.82843 4 6C4 5.17157 4.67157 4.5 5.5 4.5C6.32843 4.5 7 5.17157 7 6ZM16 6C16 6.82843 15.3284 7.5 14.5 7.5C13.6716 7.5 13 6.82843 13 6C13 5.17157 13.6716 4.5 14.5 4.5C15.3284 4.5 16 5.17157 16 6ZM10 15.5C12.33 15.5 14.32 14.05 15.12 12H4.88C5.68 14.05 7.67 15.5 10 15.5Z" fill="currentColor"/>
                      </svg> */}
                    </button>
                  </div>
                </div>
                {attachmentPreview && (
                  <div className="mt-2 flex items-center gap-2 rounded-lg border border-[#363636] bg-[#1a1a1a] p-2">
                    <div className="flex-1 truncate text-sm text-[#eaeaea]">
                      {attachmentName}
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        setFormData(prev => ({ ...prev, description_image: "" }));
                        setAttachmentPreview("");
                        setAttachmentName("");
                      }}
                      className="text-[#b5b5b5] hover:text-[#eaeaea]"
                    >
                      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                )}
              </div>



              <div className="grid grid-cols-1 gap-4">

                <div>
                  <label className="mb-2 block text-sm text-[#b5b5b5]">Estimated Deal Size</label>
                  <input
                    type="text"
                    name="deal_size"
                    value={formData.deal_size}
                    onChange={handleChange}
                    placeholder="e.g., $500"
                    className="h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] placeholder-[#666]"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="mb-2 block text-sm text-[#b5b5b5]">Referral Fee (%)</label>
                  <input
                    type="text"
                    name="referral_fee"
                    value={formData.referral_fee}
                    onChange={handleChange}
                    placeholder="Enter percentage"
                    className="h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] placeholder-[#666]"
                  />
                </div>
                <div>
                  <label className="mb-2 block text-sm text-[#b5b5b5]">Payment Method</label>
                  <select
                    name="payment_method"
                    value={formData.payment_method}
                    onChange={handleChange}
                    className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]"
                  >
                    <option value="">Select method</option>
                    <option value="bank">Bank Transfer</option>
                    <option value="bank">Bank Card</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="mb-2 block text-sm text-[#b5b5b5]">Referral Type</label>
                <select
                  value={formData.referral_type}
                  onChange={(e) => {
                    setSelectedType(e.target.value);
                    return setFormData(prev => ({ ...prev, referral_type: e.target.value }));
                  }}
                  className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]"
                >
                  <option value="">Select type</option>
                  <option value="open referral">Open Referral</option>
                  <option value="community referral">Community Referral</option>
                  <option value="direct referral">Direct Referral</option>
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                {(formData.referral_type === "community referral" || formData.referral_type === "direct referral") && (<div>
                  <label className="mb-2 block text-sm text-[#b5b5b5]">Community</label>
                  <select
                    name="community"
                    value={formData.community}
                    onChange={(e) => handleCommunityChange(e)}
                    className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]"
                    required
                  >
                    <option value="" disabled>Select community</option>
                    {joinedCommunities.map((community) => (
                      <option
                        key={community.id.value}
                        value={community.id.value}
                      >
                        {community.title.value}
                      </option>
                    ))}
                  </select>
                  </div>)}
                </div>

                {formData.referral_type === "direct referral" && (
                  <div>
                    <label className="mb-2 block text-sm text-[#b5b5b5]">Direct Person</label>
                    {fetchingUsers ? (
                      <div className="h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] placeholder-[#666]">
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-[#eaeaea]" style={{ marginTop: "8px" }}></div>
                        </div>
                      </div>
                    ) : (
                      <select
                        name="direct_person"
                        value={formData.direct_person}
                        onChange={handleChange}
                        className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]"
                      >
                      <option value="">Select person</option>
                      {users.map((user) => (
                        <option key={user.id.value} value={user.id.value}>
                          {user.name.value}
                        </option>
                      ))}
                      </select>
                    )}
                  </div>
                )}
                </div>

              <div>
                <label className="mb-2 block text-sm text-[#b5b5b5]">Expiration Date</label>
                <input
                  type="date"
                  name="expiration_date"
                  value={formData.expiration_date}
                  onChange={handleChange}
                  className="h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
                  min={new Date().toISOString().split('T')[0]} // Set minimum date to today
                />
                <p className="mt-1 text-xs text-[#666]">The opportunity will expire on this date. Leave blank for no expiration.</p>
              </div>

              <div>
                <label className="mb-2 block text-sm text-[#b5b5b5]">Additional Notes</label>
                <textarea
                  name="additional_notes"
                  value={formData.additional_notes}
                  onChange={handleChange}
                  placeholder="Any other information that might be helpful"
                  rows={3}
                  className="w-full rounded-lg border border-[#363636] bg-[#242424] p-4 text-[#eaeaea] placeholder-[#666]"
                />
              </div>

              <div className="flex justify-end gap-4">
                <button
                  type="button"
                  onClick={() => navigate('/member/referrals', { state: { activeTab: 'my-referrals' } })}
                  className="rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#363636]"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={saving}
                  className="rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#2e7d32]/90"
                >
                  {saving ? "Saving..." : "Save Changes"}
                </button>
              </div>
            </form>
          )}

          {error && <Toast message={error} />}
        </div>
      </div>
    </div>
  );
};

export default EditReferralPage;