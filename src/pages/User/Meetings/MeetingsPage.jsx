import React, { useState, useEffect, useCallback, useContext } from "react";
import { useNavigate } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { Toast } from "Components/Toast";
import { SkeletonLoader } from "Components/Skeleton";
import { showToast, GlobalContext } from "Context/Global";
import debounce from "lodash/debounce";

const PlatformOption = ({ selected, logo, name, onClick }) => (
  <div
    onClick={onClick}
    className={`mb-2 mr-4 flex items-center ${
      selected ? "border-[#2e7d32] " : "border-[#363636]"
    } cursor-pointer transition-colors hover:border-[#2e7d32]`}
  >
    <div className="flex gap-2 items-center">
      <div
        className={`h-4 w-4 rounded-full border-2 ${
          selected ? "border-[#2e7d32]" : ""
        } flex items-center justify-center`}
      >
        {selected && <div className="w-2 h-2 bg-white rounded-full" />}
      </div>
      {name === "Google Meet" ? (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          className="w-5 h-5"
        >
          <path
            d="M15.25 8.18125C15.25 12.6031 12.2219 15.75 7.75 15.75C3.4625 15.75 0 12.2875 0 8C0 3.7125 3.4625 0.25 7.75 0.25C9.8375 0.25 11.5938 1.01562 12.9469 2.27813L10.8375 4.30625C8.07812 1.64375 2.94688 3.64375 2.94688 8C2.94688 10.7031 5.10625 12.8938 7.75 12.8938C10.8188 12.8938 11.9688 10.6938 12.15 9.55313H7.75V6.8875H15.1281C15.2 7.28437 15.25 7.66562 15.25 8.18125Z"
            fill="#EAEAEA"
          />
        </svg>
      ) : name === "Zoom" ? (
        <svg
          className="w-5 h-5"
          xmlns="http://www.w3.org/2000/svg"
          aria-label="Zoom"
          role="img"
          viewBox="0 0 512 512"
        >
          <rect width="512" height="512" rx="15%" fill="#2D8CFF" />
          <path
            fill="#ffffff"
            d="M428 357c8 2 15-2 19-8 2-3 2-8 2-19V179c0-11 0-15-2-19-3-8-11-11-19-8-21 14-67 55-68 72-.8 3-.8 8-.8 15v38c0 8 0 11 .8 15 1 8 4 15 8 19 12 9 52 45 61 45zM64 187c0-15 0-23 3-27 2-4 8-8 11-11 4-3 11-3 27-3h129c38 0 57 0 72 8 11 8 23 15 30 30 8 15 8 34 8 72v68c0 15 0 23-3 27-2 4-8 8-11 11-4 3-11 3-27 3H174c-38 0-57 0-72-8-11-8-23-15-30-30-8-15-8-34-8-72z"
          />
        </svg>
      ) : (
        <svg
          className="w-5 h-5"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 2228.833 2073.333"
        >
          <path
            fill="#EAEAEA"
            d="M1554.637,777.5h575.713c54.391,0,98.483,44.092,98.483,98.483c0,0,0,0,0,0v524.398 c0,54.391-44.092,98.483-98.483,98.483c0,0,0,0,0,0h-575.713c-54.391,0-98.483-44.092-98.483-98.483c0,0,0,0,0,0V875.983 C1456.154,821.592,1500.246,777.5,1554.637,777.5z M1943.783,1016.499l-283.33,186.265v-372.53L1943.783,1016.499z"
          />
          <path
            fill="#EAEAEA"
            d="M1014.637,777.5h575.713c54.391,0,98.483,44.092,98.483,98.483c0,0,0,0,0,0v524.398 c0,54.391-44.092,98.483-98.483,98.483c0,0,0,0,0,0h-575.713c-54.391,0-98.483-44.092-98.483-98.483c0,0,0,0,0,0V875.983 C916.154,821.592,960.246,777.5,1014.637,777.5z"
          />
        </svg>
      )}
      <span className="text-[#eaeaea]">{name}</span>
    </div>
  </div>
);

const AttendeeTag = ({ email, onRemove }) => {
  console.log(email, "Adding Attendee");
  return (
    <div className="inline-flex items-center gap-1 rounded bg-[#2e7d32] px-2 py-1 text-sm text-white">
      {email}
      <button onClick={onRemove} className="text-white hover:text-red-300">
        ×
      </button>
    </div>
  );
};

const MeetingsPage = () => {
  const navigate = useNavigate();
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(true);
  const [upcomingMeetings, setUpcomingMeetings] = useState([]);
  const [recentActivity, setRecentActivity] = useState([]);
  const [notes, setNotes] = useState([]);
  const [communityUpdates, setCommunityUpdates] = useState([]);
  const [showNewMeeting, setShowNewMeeting] = useState(false);
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [newMeetingForm, setNewMeetingForm] = useState({
    calendar: "Google Calendar",
    platform: "Google Meet",
    title: "",
    date: "",
    time: "",
    attendees: [],
  });
  const [showAttendeesList, setShowAttendeesList] = useState(false);
  const [attendeeEmail, setAttendeeEmail] = useState("");
  const [attendees, setAttendees] = useState([]);
  const [suggestedUsers, setSuggestedUsers] = useState([]);
  const [searchingUsers, setSearchingUsers] = useState(false);
  const [displayCount, setDisplayCount] = useState(5);
  const [selectedMeeting, setSelectedMeeting] = useState(null);
  const [showMeetingDetails, setShowMeetingDetails] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const sdk = new MkdSDK();
      const [meetingsResponse, data, communityUpdates] = await Promise.all([
        sdk.GetUpcomingMeetings(),
        sdk.GetDashboardStats(),
        sdk.GetCommunityUpdates(),
      ]);

      if (!meetingsResponse.error && !data.error && !communityUpdates.error) {
        const { notes: notesRes = [], activities = [] } = data.data;

        setUpcomingMeetings(meetingsResponse.list || []);
        setNotes(notesRes);
        setRecentActivity(activities.slice(0, 5));
        setCommunityUpdates(communityUpdates.updates.slice(0, 5) || []);
      }
    } catch (err) {
      setError(err.message || "Failed to load data");
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString, monthday = false) => {
    if (!dateString) return "";

    const date = new Date(dateString);

    if (monthday) {
      return date
        .toLocaleString("default", {
          month: "short",
          day: "numeric",
        })
        .replace(/(\d{2}) (\d{2})/, "$1/$2")
        .split(" ")
        .reverse()
        .join(" ");
    }

    // For meetings from the API that have separate date and time fields
    if (typeof dateString === "object" && dateString.date && dateString.time) {
      const combinedDate = new Date(
        `${dateString.date.value}T${dateString.time.value}`
      );
      return combinedDate.toLocaleString("default", {
        weekday: "short",
        month: "short",
        day: "numeric",
        hour: "numeric",
        minute: "numeric",
        hour12: true,
      });
    }

    // For meetings, combine the date and time properly
    if (dateString.includes("T")) {
      return date.toLocaleString("default", {
        weekday: "short",
        month: "short",
        day: "numeric",
        hour: "numeric",
        minute: "numeric",
        hour12: true,
      });
    }

    // If we have separate date and time fields (like in the form)
    const timeString = newMeetingForm.time;
    if (timeString) {
      const [hours, minutes] = timeString.split(":");
      date.setHours(parseInt(hours, 10), parseInt(minutes, 10));
    }

    return date.toLocaleString("default", {
      weekday: "short",
      month: "short",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
      hour12: true,
    });
  };

  const handleNewMeetingClick = () => {
    setShowNewMeeting(true);
  };

  const handleAddAttendee = (email, id) => {
    console.log(email, "Adding Attendee 1");
    if (
      !attendees.some((attendee) => attendee.email === email) &&
      email.trim() !== ""
    ) {
      setAttendees([...attendees, { email, id }]);
      console.log(attendees, "Adding Attendee");
    }
    setAttendeeEmail("");
    setShowAttendeesList(false);
  };

  const handleRemoveAttendee = (emailToRemove) => {
    setAttendees(
      attendees.filter((attendee) => attendee.email !== emailToRemove)
    );
  };

  // const handleAttendeeKeyPress = (e) => {
  //   if (e.key === 'Enter') {
  //     e.preventDefault();
  //     handleAddAttendee(attendeeEmail);
  //   }
  // };

  const debouncedSearch = useCallback(
    debounce(async (query) => {
      if (query.trim().length < 2) return;

      try {
        setSearchingUsers(true);
        const sdk = new MkdSDK();
        const response = await sdk.SearchUsers(query);

        if (!response.error) {
          setSuggestedUsers(response.list || []);
          setSearchingUsers(false);
        }
      } catch (err) {
        console.error("Failed to search users:", err);
      } finally {
      }
    }, 300),
    []
  );

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Combine date and time into a proper ISO string
    const { date, time } = newMeetingForm;
    let combinedDateTime = null;

    if (date && time) {
      const [hours, minutes] = time.split(":");
      const dateObj = new Date(date);
      dateObj.setHours(parseInt(hours, 10), parseInt(minutes, 10));
      combinedDateTime = dateObj.toISOString();
    }

    console.log("Form submitted with:", {
      ...newMeetingForm,
      dateTime: combinedDateTime,
      attendees,
    });

    const sdk = new MkdSDK();

    try {
      const response = await sdk.CreateMeeting({
        ...newMeetingForm,
        dateTime: combinedDateTime,
        attendees: attendees.map((attendee) => attendee.id),
      });

      if (!response.error) {
        setShowNewMeeting(false);
        loadData();
        showToast(globalDispatch, response.message, 5000, "success");
      } else {
        showToast(globalDispatch, response.error, 5000, "error");
      }
    } catch (err) {
      console.error("Failed to create meeting:", err);
      showToast(globalDispatch, err.message, 5000, "error");
    }
  };

  const handleViewMore = () => {
    setDisplayCount((prevCount) => prevCount + 5);
  };

  const MeetingDetailsModal = ({ meeting, onClose }) => {
    if (!meeting) return null;

    return (
      <div className="flex fixed inset-0 z-50 justify-center items-center w-full h-full bg-black/50">
        <div className="w-full max-w-2xl rounded-xl bg-[#161616] p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-[#eaeaea]">
              {meeting.title.value}
            </h2>
            <button
              onClick={onClose}
              className="text-[#b5b5b5] hover:text-[#eaeaea]"
            >
              ×
            </button>
          </div>

          <div className="mt-4 mb-4 space-y-6">
            {/* Meeting Info */}
            <div className="rounded-lg bg-[#242424] p-4">
              <h3 className="mb-4 font-medium text-[#eaeaea]">
                Meeting Details
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-[#b5b5b5]">Date & Time</span>
                  <span className="text-[#eaeaea]">
                    {formatDate({ date: meeting.date, time: meeting.time })}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-[#b5b5b5]">Platform</span>
                  <span className="text-[#eaeaea]">
                    {meeting.platform.value === "google_meet"
                      ? "Google Meet"
                      : "Zoom"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-[#b5b5b5]">Status</span>
                  <span className="rounded-full bg-[#2e7d32]/20 px-2 py-1 text-xs text-[#7dd87d]">
                    {meeting.status.value}
                  </span>
                </div>
              </div>
            </div>

            {/* Creator Info */}
            <div className="mb-4 mt-4 rounded-lg bg-[#242424] p-4">
              <h3 className="mb-4 font-medium text-[#eaeaea]">Created By</h3>
              <div className="flex gap-3 items-center">
                {meeting.creator.avatar.value ? (
                  <img
                    src={meeting.creator.avatar.value}
                    alt=""
                    className="w-10 h-10 rounded-full"
                  />
                ) : (
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-[#2e7d32] text-white">
                    {meeting.creator.name.value.charAt(0)}
                  </div>
                )}
                <div>
                  <p className="text-[#eaeaea]">{meeting.creator.name.value}</p>
                </div>
              </div>
            </div>

            {/* Attendees */}
            <div className="rounded-lg bg-[#242424] p-4">
              <h3 className="mb-4 font-medium text-[#eaeaea]">
                Attendees ({meeting.attendees.length})
              </h3>
              <div className="space-y-3">
                {meeting.attendees.map((attendee) => (
                  <div
                    key={attendee.id.value}
                    className="flex gap-3 items-center"
                  >
                    {attendee.avatar.value ? (
                      <img
                        src={attendee.avatar.value}
                        alt=""
                        className="w-8 h-8 rounded-full"
                      />
                    ) : (
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#2e7d32] text-sm text-white">
                        {attendee.name.value.charAt(0)}
                      </div>
                    )}
                    <p className="text-[#eaeaea]">{attendee.name.value}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="flex gap-3 justify-end mt-4 mt-6 mb-4">
            <button
              onClick={onClose}
              className="rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#242424]"
            >
              Close
            </button>
            {meeting.join_url.value && (
              <button
                onClick={() => window.open(meeting.join_url.value, "_blank")}
                className="rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
              >
                Join Meeting
              </button>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-[#1e1e1e] p-6">
      {!showNewMeeting ? (
        <div className="">
          {/* Main Content */}
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-semibold text-[#eaeaea]">
                Upcoming Events
              </h1>
              <p className="text-sm text-[#b5b5b5]">
                Upcoming: {formatDate(new Date(), true)} -{" "}
                {formatDate(
                  new Date(new Date().setMonth(new Date().getMonth() + 1)),
                  true
                )}{" "}
                · {upcomingMeetings.length} meetings
              </p>
              <p className="text-sm text-[#7dd87d]">
                Check 1 month of meetings from today
              </p>
            </div>
            <button
              onClick={handleNewMeetingClick}
              className="rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
            >
              + New Meeting
            </button>
          </div>
          <div className="flex gap-6">
            <div
              style={{
                width: "65%",
              }}
              className="flex-1"
            >
              {/* Upcoming Events */}
              <div className="mb-6 rounded-xl bg-[#161616] p-6">
                {loading ? (
                  <div className="space-y-4">
                    {[...Array(3)].map((_, index) => (
                      <div
                        key={index}
                        className="animate-pulse rounded-lg bg-[#242424] p-4"
                      >
                        <div className="h-6 w-48 rounded bg-[#363636]" />
                        <div className="mt-2 h-4 w-32 rounded bg-[#363636]" />
                      </div>
                    ))}
                  </div>
                ) : upcomingMeetings.length > 0 ? (
                  <div className="space-y-4">
                    {upcomingMeetings.slice(0, displayCount).map((meeting) => (
                      <div
                        key={meeting.id.value}
                        className="flex cursor-pointer items-center justify-between rounded-lg bg-[#242424] p-4 hover:bg-[#2e2e2e]"
                        onClick={() => {
                          setSelectedMeeting(meeting);
                          setShowMeetingDetails(true);
                        }}
                      >
                        <div className="flex gap-4 items-center">
                          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-[#2e7d32] text-white">
                            {meeting.title.value.charAt(0)}
                          </div>
                          <div>
                            <h3 className="text-sm font-medium text-[#eaeaea]">
                              {meeting.title.value} {meeting.platform && meeting.platform.value && (
                                `(${meeting.platform.value})`
                              )}
                            </h3>
                            <p className="text-xs text-[#b5b5b5]">
                              {formatDate({
                                date: meeting.date,
                                time: meeting.time,
                              })}
                            </p>
                          </div>
                        </div>
                        <div className="flex gap-2">

                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              window.open(meeting.join_url.value, "_blank");
                            }}
                            className="rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
                          >
                            Join
                          </button>
                        </div>
                      </div>
                    ))}
                    {upcomingMeetings.length > displayCount && (
                      <button
                        onClick={handleViewMore}
                        className="mt-4 w-full rounded-lg border border-[#363636] py-2 text-sm text-[#eaeaea] hover:bg-[#242424]"
                      >
                        View More
                      </button>
                    )}
                  </div>
                ) : (
                  <p className="text-center text-[#b5b5b5]">
                    No upcoming meetings
                  </p>
                )}
              </div>

              {/* Community Updates */}

            </div>

            {/* Right Sidebar */}
            <div
              style={{
                width: "35%",
              }}
              className="space-y-6 w-80"
            >
              {/* Recent Activity */}
              {/* <div className="rounded-xl bg-[#161616] p-6">
                <h2 className="mb-4 text-lg font-semibold text-[#eaeaea]">
                  Recent Activity
                </h2>
                <div className="space-y-4">
                  {recentActivity.map((activity) => (
                    <div
                      key={activity.id.value}
                      className="flex gap-4 items-start"
                    >
                      <div className="flex min-h-8 min-w-8 items-center justify-center rounded-full bg-[#2e7d32] text-white">
                        {activity.author.value.charAt(0)}
                      </div>
                      <div className="">
                        <p className="text-sm text-[#eaeaea]">
                          {activity.title.value}
                        </p>
                        <p className="text-xs text-[#b5b5b5]">
                          {formatDate(activity.created_at.value)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div> */}

              {/* Notes */}
              {/* <div className="mt-4 rounded-xl bg-[#161616] p-6">
                <h2 className="mb-4 text-lg font-semibold text-[#eaeaea]">
                  Notes
                </h2>
                <div className="space-y-4">
                  {notes.map((note) => (
                    <div key={note.id} className="rounded-lg bg-[#242424] p-4">
                      <p className="text-sm text-[#eaeaea]">{note.text}</p>
                      <p className="mt-2 text-xs text-[#b5b5b5]">{note.date}</p>
                    </div>
                  ))}
                </div>
              </div> */}
            </div>
          </div>
        </div>
      ) : (
        <div className="">
          {/* New Meeting Form */}
          <h1 className="mb-4 text-2xl font-semibold text-[#eaeaea]">
            Manage Meetings
          </h1>
          <p className="mb-6 text-[#b5b5b5]">
            Schedule and manage your meetings
          </p>
          <div className="flex gap-6">
            <div
              style={{
                width: "65%",
              }}
              className="flex-1"
            >
              <div className="rounded-xl bg-[#161616] p-6">
                <h2 className="mb-6 text-xl font-semibold text-[#eaeaea]">
                  Schedule Meeting
                </h2>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Calendar Selection */}
                  <div>
                    <label className="mb-2 block text-sm text-[#b5b5b5]">
                      Calendar
                    </label>
                    <select
                      value={newMeetingForm.calendar}
                      onChange={(e) =>
                        setNewMeetingForm({
                          ...newMeetingForm,
                          calendar: e.target.value,
                        })
                      }
                      className="w-full rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-[#eaeaea]"
                    >
                      <option value="google_calendar">Google Calendar</option>
                      <option value="outlook_calendar">Outlook Calendar</option>
                      <option value="highlevel">HighLevel</option>
                    </select>
                  </div>

                  {/* Platform Selection */}
                  <div>
                    <label className="mb-2 mt-4 block text-sm text-[#b5b5b5]">
                      Platform
                    </label>
                    <div className="flex items-center">
                      <PlatformOption
                        selected={newMeetingForm.platform === "Google Meet"}
                        name="Google Meet"
                        onClick={() =>
                          setNewMeetingForm({
                            ...newMeetingForm,
                            platform: "Google Meet",
                          })
                        }
                      />
                      <PlatformOption
                        selected={newMeetingForm.platform === "Zoom"}
                        name="Zoom"
                        onClick={() =>
                          setNewMeetingForm({
                            ...newMeetingForm,
                            platform: "Zoom",
                          })
                        }
                      />
                      <PlatformOption
                        selected={newMeetingForm.platform === "Microsoft Teams"}
                        name="Microsoft Teams"
                        onClick={() =>
                          setNewMeetingForm({
                            ...newMeetingForm,
                            platform: "Microsoft Teams",
                          })
                        }
                      />
                    </div>
                  </div>

                  {/* Meeting Title */}
                  <div>
                    <label className="mb-4 mt-4 block text-sm text-[#b5b5b5]">
                      Meeting Title
                    </label>
                    <input
                      type="text"
                      value={newMeetingForm.title}
                      onChange={(e) =>
                        setNewMeetingForm({
                          ...newMeetingForm,
                          title: e.target.value,
                        })
                      }
                      placeholder="Write title of meeting"
                      className="w-full rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-[#eaeaea]"
                    />
                  </div>

                  {/* Date and Time */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="mb-4 mt-4 block text-sm text-[#b5b5b5]">
                        Date
                      </label>
                      <input
                        type="date"
                        value={newMeetingForm.date}
                        onChange={(e) =>
                          setNewMeetingForm({
                            ...newMeetingForm,
                            date: e.target.value,
                          })
                        }
                        className="w-full rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-[#eaeaea]"
                      />
                    </div>
                    <div>
                      <label className="mb-4 mt-4 block text-sm text-[#b5b5b5]">
                        Time
                      </label>
                      <input
                        type="time"
                        value={newMeetingForm.time}
                        onChange={(e) =>
                          setNewMeetingForm({
                            ...newMeetingForm,
                            time: e.target.value,
                          })
                        }
                        className="w-full rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-[#eaeaea]"
                      />
                    </div>
                  </div>

                  {/* Attendees */}
                  <div className="relative">
                    <label className="mb-4 mt-4 block text-sm text-[#b5b5b5]">
                      Attendees
                    </label>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {attendees.map((attendee) => (
                        <AttendeeTag
                          key={attendee.email}
                          email={attendee.email}
                          id={attendee.id}
                          onRemove={() => handleRemoveAttendee(attendee.email)}
                        />
                      ))}
                    </div>
                    <div className="relative">
                      <input
                        type="email"
                        value={attendeeEmail}
                        onChange={(e) => {
                          const value = e.target.value;
                          setAttendeeEmail(value);
                          setShowAttendeesList(true);
                          debouncedSearch(value);
                        }}
                        // onKeyPress={handleAttendeeKeyPress}
                        placeholder="Add attendee email"
                        className="w-full rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-[#eaeaea]"
                      />
                      {showAttendeesList && attendeeEmail.trim() !== "" && (
                        <div className="absolute z-10 mt-1 w-full rounded-lg border border-[#363636] bg-[#242424] py-2 shadow-lg">
                          {searchingUsers ? (
                            <div className="px-4 py-2 text-[#b5b5b5]">
                              Searching...
                            </div>
                          ) : suggestedUsers.length > 0 ? (
                            suggestedUsers.map((user) => (
                              <div
                                key={user.id.value}
                                onClick={() =>
                                  handleAddAttendee(
                                    user.email.value,
                                    user.id.value
                                  )
                                }
                                className="cursor-pointer px-4 py-2 text-[#eaeaea] hover:bg-[#2e7d32]"
                              >
                                <div className="flex gap-2 items-center">
                                  {user.avatar.value ? (
                                    <img
                                      src={user.avatar.value}
                                      alt=""
                                      className="w-6 h-6 rounded-full"
                                    />
                                  ) : (
                                    <div className="flex h-6 w-6 items-center justify-center rounded-full bg-[#2e7d32] text-sm text-white">
                                      {user.name.value.charAt(0)}
                                    </div>
                                  )}
                                  <div>
                                    <div>{user.name.value}</div>
                                    <div className="text-sm text-[#b5b5b5]">
                                      {user.email.value}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))
                          ) : (
                            <div className="px-4 py-2 text-[#b5b5b5]">
                              No users found
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Submit Button */}
                  <button
                    type="submit"
                    className="mt-4 w-full rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
                  >
                    Send Invite
                  </button>
                </form>
              </div>
            </div>

            {/* Upcoming Meetings Sidebar */}
            <div
              style={{
                width: "35%",
              }}
              className=""
            >
              <div className="rounded-xl bg-[#161616] p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold text-[#eaeaea]">
                    Upcoming Meetings
                  </h2>
                  <label className="text-xs text-[#b5b5b5]">
                    <input
                      type="checkbox"
                      className="mr-2 h-3 w-3 text-[#b5b5b5]"
                    />
                    Next 30 days
                  </label>
                </div>
                <div className="space-y-4">
                  {upcomingMeetings.slice(0, displayCount).map((meeting) => (
                    <div
                      key={meeting.id.value}
                      className="flex items-center justify-between rounded-lg bg-[#242424] p-4"
                    >
                      <div className="flex gap-4 items-center">
                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-[#2e7d32] text-white">
                          {meeting.title.value.charAt(0)}
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-[#eaeaea]">
                            {meeting.title.value}
                          </h3>
                          <p className="text-xs text-[#b5b5b5]">
                            {formatDate({
                              date: meeting.date,
                              time: meeting.time,
                            })}
                          </p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <button
                          onClick={() =>
                            window.open(meeting.join_url.value, "_blank")
                          }
                          className="rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
                        >
                          Join
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* View More Button */}
                {upcomingMeetings.length > displayCount && (
                  <button
                    onClick={handleViewMore}
                    className="mt-4 w-full rounded-lg border border-[#363636] py-2 text-sm text-[#eaeaea] hover:bg-[#242424]"
                  >
                    View More
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {error && <Toast message={error} />}

      {showMeetingDetails && (
        <MeetingDetailsModal
          meeting={selectedMeeting}
          onClose={() => {
            setShowMeetingDetails(false);
            setSelectedMeeting(null);
          }}
        />
      )}
    </div>
  );
};

export default MeetingsPage;
